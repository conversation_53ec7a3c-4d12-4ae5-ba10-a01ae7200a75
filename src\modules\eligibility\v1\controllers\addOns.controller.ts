import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AddOnsEligibilityRes,
	addOnsEligibilityReqSchema,
	addOnsEligibilityResSchema
} from '../schemas/api/addOns.schema';
import AddOnsEligibility from '../services/addOns.service';

const addOnsV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			AddOnsEligibility: new AddOnsEligibility(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/add-ons',
		async (ctx): Promise<AddOnsEligibilityRes> => {
			return await ctx.AddOnsEligibility.getAddOnsEligibility(ctx.body);
		},
		{
			detail: {
				description:
					"Get customer's addons subscription eligibility. <br><br> <b>Backend System:</b> NOVA SIEBEL & OMG",
				tags: ['Eligibility']
			},
			body: addOnsEligibilityReqSchema,
			response: {
				200: addOnsEligibilityResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default addOnsV1Routes;
