import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { SystemNameEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2SubmitRebateReq } from '../../../../integration/wso2/record/schemas/api/wso2SubmitRebate.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	icpBillingProfile,
	novaBillingProfile
} from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import { rebateTableSchema } from '../../../eligibility/v1/schemas/db/rebateRedeemEligibility.schema';
import type { SubmitRebateReq } from '../schemas/api/rebate.schema';

class Rebate {
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
	}

	async submitRebate(req: SubmitRebateReq): Promise<BaseResponse> {
		const decryptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);

		// Get the billing profile details
		const billingProfile: NovaIcpBillingProfile =
			req.SystemName === SystemNameEnum.ICP
				? await icpBillingProfile(
						this.integrationId,
						this.idTokenInfo.IdType,
						this.idTokenInfo.IdValue,
						decryptedBillAccNo
					)
				: await novaBillingProfile(this.integrationId, decryptedBillAccNo);

		// Prepare the request body for WSO2
		const wso2Req: Wso2SubmitRebateReq = {
			TMTaxPeriod: 'SST',
			TMServiceID: req.ServiceID,
			TMRequestAmount: '50.00',
			TMRemarks: '24 Hours Adjustment MyUnifi',
			TMProduct: 'HSI - High Speed Internet',
			TMCurrentMonthAdjustmentFlag: '',
			TMCTTNum: req.CTTNumber,
			TMBulkStatus: 'NEW',
			TMBillingAccountNumber: decryptedBillAccNo,
			TMAdjustmentType: 'Credit',
			TMAdjustmentReason: 'Amount not displayed',
			TMAdjustmentLevel: 'Account',
			TMAdjustmentCode: 'CR 20185 24hr Restore Rebate',
			Id: this.idTokenInfo.IdValue
		};

		const response: boolean =
			await this.mwIntegration.Wso2RecordIntegration.wso2SubmitRebate(wso2Req);

		let message = 'Rebate successfully submitted';
		if (response) {
			await this.db
				.insert(rebateTableSchema)
				.values({
					TTNumber: req.CTTNumber,
					CustomerName: billingProfile.AccountName,
					CustomerId: this.idTokenInfo.IdValue,
					AssetContactMobile: null,
					ReportedByContactMobile: null,
					BillingAccountNumber: decryptedBillAccNo,
					BillingAccountName: billingProfile.AccountName,
					RebateDescription: '24 Hours Adjustment MyUnifi',
					RebateRedeemed: true
				})
				.returning();
		} else {
			message = 'Submission failed';
		}

		return {
			Success: true,
			IntegrationId: this.integrationId,
			Code: StatusCodeEnum.CREATED,
			Message: message
		};
	}
}

export default Rebate;
