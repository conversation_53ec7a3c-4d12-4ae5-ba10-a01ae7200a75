import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2CTTCheckEligibilityRes } from '../../../../integration/wso2/eligibility/schemas/api/wso2Rebate.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import type {
	EligibilityRebate,
	ListOfEligibilityRebate,
	RebateRes
} from '../schemas/api/rebate.schema';
import {
	type SelectRebate,
	rebateTableSchema
} from '../schemas/db/rebateRedeemEligibility.schema';

class RebateEligibility {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
	}

	async getRebateEligibility(): Promise<RebateRes> {
		const wso2Res: Wso2CTTCheckEligibilityRes =
			await this.mwIntegration.Wso2EligibilityIntegration.wso2CTTCheckEligibility(
				this.idTokenInfo.IdValue
			);

		const dbResponse: SelectRebate[] = await this.db
			.select()
			.from(rebateTableSchema)
			.where(eq(rebateTableSchema.CustomerId, this.idTokenInfo.IdValue));

		if (dbResponse.length !== 0) {
			for (let i = 0; i < dbResponse.length; i++) {
				const claimedTicket: SelectRebate = dbResponse[i];
				wso2Res.RetrieveUnclaimedCTT = wso2Res.RetrieveUnclaimedCTT.filter(
					rebateList =>
						rebateList?.SRNumber?.toLowerCase() !==
						claimedTicket.TTNumber.toLowerCase()
				);
			}
		}

		let eligibleRebateResponse: ListOfEligibilityRebate = [];
		if (wso2Res.RetrieveUnclaimedCTT.length !== 0) {
			eligibleRebateResponse = wso2Res.RetrieveUnclaimedCTT.filter(
				(
					eligibleRecord
				): eligibleRecord is NonNullable<typeof eligibleRecord> =>
					eligibleRecord !== null
			).map(eligibleRecord => {
				const eligibleRebate: EligibilityRebate = {
					ReferenceNumber: eligibleRecord.SRNumber ?? '',
					AccountNo: eligibleRecord.AccountNo ?? '',
					Description: 'RM50 RebateEligibility Amount',
					ServiceID: eligibleRecord.ServiceID ?? ''
				};
				return eligibleRebate;
			});
		}

		const res: RebateRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: eligibleRebateResponse
		};
		return res;
	}
}

export default RebateEligibility;
