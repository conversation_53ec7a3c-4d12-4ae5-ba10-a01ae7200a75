import { type Static, t } from 'elysia';

export const omgOttEntitlementReqSchema = t.Object({
	accountType: t.String(),
	accountId: t.String(),
	ottMerchantId: t.Number()
});

export type OmgOttEntitlementReq = Static<typeof omgOttEntitlementReqSchema>;

/* * Response Codes
  200 : Ok
  201 : Created
  400 : Bad Request
  403 : Forbidden
  500 : Internal Server Error
  000 : Available
  111 : Not Available
  222 : Exists Al<PERSON> */
export const omgGetOttEntitlementResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String({ examples: ['Available'] })
});

export type OmgOttEntitlementRes = Static<typeof omgGetOttEntitlementResSchema>;
