import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
import {
	type IdentityRes,
	identityResSchema
} from '../../../identity/v1/schemas/api/identity';
import { authorizedHeaderSchema } from '../../../util/schemas/headers';
import {
	type DeleteSessionRes,
	deleteSessionResSchema
} from '../schemas/api/session';
import CheckSession from '../services/CheckSession';
import Session from '../services/Session';

export const sessionRoutes = new Elysia({ prefix: '/session' })
	.resolve(async ({ headers: { authorization } }) => {
		return {
			userId: await new CheckSession(randomUUID()).getUserId(
				authorization ?? ''
			)
		};
	})
	.get(
		'/user-info',
		async (ctx): Promise<IdentityRes> => {
			return await new Session(randomUUID()).validateSession(ctx.userId);
		},
		{
			response: {
				200: identityResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Validate user session by SessionId (as Bearer token in Authorization header). API will return user details if token is valid.<br>' +
					'User info details will be cached for 24 hours<br><br>' +
					'Tables: mw_identity, mw_identification, mw_credential, mw_sme_profile, mw_session, mw_user_auth',
				tags: ['UAID: Session']
			}
		}
	)
	.get(
		'/signout',
		async (ctx): Promise<DeleteSessionRes> => {
			return await new Session(randomUUID()).deleteSession(
				ctx.headers.authorization ?? ''
			);
		},
		{
			headers: authorizedHeaderSchema,
			response: {
				200: deleteSessionResSchema,
				401: errorBaseResponseSchema,
				404: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Delete user session by SessionId (as Bearer token in Authorization header).<br><br>' +
					'Tables: mw_identity, mw_identification, mw_credential, mw_sme_profile, mw_session, mw_user_auth',
				tags: ['UAID: Session']
			}
		}
	);
