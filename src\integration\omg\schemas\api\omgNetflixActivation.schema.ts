import { type Static, t } from 'elysia';

export const omgNetflixActivationUrlReqSchema = t.Object({
	accountType: t.String({
		minLength: 1
	}),
	accountId: t.String({
		minLength: 1
	}),
	tokenType: t.String({
		minLength: 1
	}),
	tokenChannel: t.String({
		minLength: 1
	}),
	tokenRefNo: t.String({
		minLength: 1
	}),
	tokenPromoID: t.Optional(
		t.String({
			examples: [''],
			description: ''
		})
	),
	tokenErrorUrl: t.String({
		minLength: 1
	})
});

export type OmgNetflixActivationUrlReq = Static<
	typeof omgNetflixActivationUrlReqSchema
>;

export const omgNetflixActivationUrlResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String(),
	tokenURL: t.String(),
	tokenExpiry: t.Date()
});

export type OmgNetflixActivationUrlRes = Static<
	typeof omgNetflixActivationUrlResSchema
>;
