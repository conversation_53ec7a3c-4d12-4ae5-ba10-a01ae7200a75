import { type Static, t } from 'elysia';

export const bizECommerceLoginResSchema = t.MaybeEmpty(
	t.Object({
		status: t.MaybeEmpty(t.Number()),
		message: t.<PERSON>Empty(t.String()),
		data: t.MaybeEmpty(
			t.Object({
				token_type: t.MaybeEmpty(t.String()),
				expires_in: t.MaybeEmpty(t.Number()),
				access_token: t.MaybeEmpty(t.String()),
				refresh_token: t.MaybeEmpty(t.String())
			})
		)
	})
);
export type BizECommerceLoginRes = Static<typeof bizECommerceLoginResSchema>;

const bizECommerceRegistrationReqSchema = t.Object({
	company_name: t.String(),
	customer_id_type: t.String(),
	segment_group: t.String(),
	owner_name: t.Nullable(t.String()),
	block: t.Nullable(t.String()),
	email_street_type: t.Nullable(t.String()),
	segment_sub_group: t.Number(),
	industry_code: t.Number(),
	brn_format: t.Number(),
	brn_type: t.Number(),
	relationship_code: t.Number(),
	contracting_party: t.Number(),
	foreign_telco: t.Number(),
	customer_id_number: t.String(),
	first_name: t.String(),
	last_name: t.String(),
	contact_id_number: t.Nullable(t.String()),
	preferred_language: t.Number(),
	mobile_phone: t.String(),
	bill_email_address: t.String(),
	address_type: t.Number(),
	unit_number: t.String(),
	floor_number: t.String(),
	building_name: t.String(),
	street_type: t.String(),
	street_name: t.String(),
	section: t.String(),
	post_code: t.String(),
	city: t.String(),
	state: t.Number(),
	country: t.Number(),
	training_plan: t.String(),
	existing_tm_customer: t.Number(),
	waiver: t.Number(),
	commission_to_id: t.Nullable(t.String()),
	commission_to_name: t.Nullable(t.String()),
	pack_code: t.String(),
	business_model: t.Number(),
	service_id: t.String(),
	email: t.String(),
	telephone: t.String(),
	order_number: t.String(),
	PlanName: t.String(),
	MonthlyFee: t.Number()
});
export type BizECommerceRegistrationReq = Static<
	typeof bizECommerceRegistrationReqSchema
>;

export const bizECommerceRegistrationResSchema = t.Object({
	status: t.MaybeEmpty(t.Number()),
	invalidFields: t.MaybeEmpty(
		t.Object({
			companyName: t.MaybeEmpty(t.String()),
			customerIdType: t.MaybeEmpty(t.String()),
			customerIdNumber: t.MaybeEmpty(t.String()),
			segmentGroup: t.MaybeEmpty(t.String()),
			segmentSubGroup: t.MaybeEmpty(t.String()),
			industryCode: t.MaybeEmpty(t.String()),
			brnFormat: t.MaybeEmpty(t.String()),
			brnType: t.MaybeEmpty(t.String()),
			relationshipCode: t.MaybeEmpty(t.String()),
			contractingParty: t.MaybeEmpty(t.String()),
			foreignTelco: t.MaybeEmpty(t.String()),
			contactFname: t.MaybeEmpty(t.String()),
			contactLname: t.MaybeEmpty(t.String()),
			preferredLanguage: t.MaybeEmpty(t.String()),
			mobilePhone: t.MaybeEmpty(t.String()),
			billEmailAddress: t.MaybeEmpty(t.String()),
			addressType: t.MaybeEmpty(t.String()),
			unitNumber: t.MaybeEmpty(t.String()),
			floorNumber: t.MaybeEmpty(t.String()),
			block: t.MaybeEmpty(t.String()),
			buildingName: t.MaybeEmpty(t.String()),
			streetName: t.MaybeEmpty(t.String()),
			streetType: t.MaybeEmpty(t.String()),
			section: t.MaybeEmpty(t.String()),
			postCode: t.MaybeEmpty(t.String()),
			city: t.MaybeEmpty(t.String()),
			state: t.MaybeEmpty(t.String()),
			country: t.MaybeEmpty(t.String()),
			trainingPlan: t.MaybeEmpty(t.String()),
			existingTmCustomer: t.MaybeEmpty(t.String()),
			waiver: t.MaybeEmpty(t.String()),
			serviceId: t.MaybeEmpty(t.String()),
			commissionToId: t.MaybeEmpty(t.String()),
			commissionToName: t.MaybeEmpty(t.String()),
			packCode: t.MaybeEmpty(t.String()),
			businessModel: t.MaybeEmpty(t.String()),
			email: t.MaybeEmpty(t.String()),
			telephone: t.MaybeEmpty(t.String())
		})
	),
	portalGeneratedOrderNumber: t.MaybeEmpty(t.String())
});
export type BizECommerceRegistrationRes = Static<
	typeof bizECommerceRegistrationResSchema
>;
