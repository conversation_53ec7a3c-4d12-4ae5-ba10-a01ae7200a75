import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { pinoLog } from '../../../../../config/pinoLog.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import ProfileHelper from '../../../profile/v1/helpers/ProfileHelper';
import { getEncryptedKeyValue } from '../../../util/encryption';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import type {
	CreateSmeProfileReq,
	CreateSmeProfileRes,
	DeleteBrnIdReqSchema,
	DeleteBrnIdResSchema
} from '../schemas/api/sme';
import {
	type SelectSmeProfile,
	smeProfileDbSchema
} from '../schemas/models/sme';

class Sme {
	private db: NodePgDatabase;
	private integrationId: string;
	private profileHelper: ProfileHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.profileHelper = new ProfileHelper(integrationId);
	}

	/**
	 * Create/add SME Profile by ADMIN role
	 * Status: Pending User Story
	 */
	async createSmeProfile(
		userId: string,
		req: CreateSmeProfileReq
	): Promise<CreateSmeProfileRes> {
		// check if profile exist
		await this.profileHelper.getUserProfileByUserId(userId);

		const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
			req.IdValue,
			req.IdType
		);

		// check if the SME Profile has been created/added
		const smeProfileRes: SelectSmeProfile[] = await this.db
			.select()
			.from(smeProfileDbSchema)
			.where(
				and(
					eq(smeProfileDbSchema.UserId, userId),
					eq(smeProfileDbSchema.IdKey, IdData.key)
				)
			)
			.limit(1);
		if (smeProfileRes.length > 0) {
			throw new UE_ERROR(
				'SME Profile (IdType and IdValue) for this ID already existed',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{ integrationId: this.integrationId, response: null }
			);
		}

		// const brnRes: SelectIdentification[] = await this.db
		// 	.select()
		// 	.from(identificationDbSchema)
		// 	.where(eq(identificationDbSchema.IdKey, IdData.key))
		// 	.limit(1);

		// const role = req.Role;
		// if (brnRes.length === 0) {
		// 	try {
		// 		await this.db.insert(identificationDbSchema).values({
		// 			IdKey: IdData.key,
		// 			IdType: req.IdType,
		// 			IdValue: IdData.encValue,
		// 			IsIdVerified: false
		// 		});
		// 	} catch (error) {
		// 		pinoLog.error('createSmeUser: ', error);
		// 		throw new UE_ERROR(
		// 			'Error when insert into mw_identification table',
		// 			StatusCodeEnum.UE_INTERNAL_SERVER,
		// 			{ integrationId: this.integrationId, response: error }
		// 		);
		// 	}
		// 	// 1st entry for the SME (IdType and IdValue) into Identification
		// 	// set default as "Super Admin"
		// 	// role = SmeUserRoleEnum.SUPER_ADMIN;
		// } else {
		// 	// for existing BRN, new User cannot self-assign as "Super Admin"
		// 	// re-assigned as "User"
		// 	// if (req.Role === SmeUserRoleEnum.SUPER_ADMIN) role = SmeUserRoleEnum.USER;
		// }

		// try {
		// 	await this.db.insert(smeProfileDbSchema).values({
		// 		UserId: userId,
		// 		IdKey: IdData.key,
		// 		DeptId: 0,
		// 		Role: role
		// 	});
		// } catch (error) {
		// 	pinoLog.error('addSmeUser: ', error);
		// 	throw new UE_ERROR(
		// 		'Error when insert into mw_sme_profile table',
		// 		StatusCodeEnum.UE_INTERNAL_SERVER,
		// 		{ integrationId: this.integrationId, response: error }
		// 	);
		// }

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'NO_ACTION',
				Message:
					'This endpoint is active but there is nothing to process at the moment.'
			}
		};
	}

	async deleteBrnIdFromProfile(
		userId: string,
		req: DeleteBrnIdReqSchema
	): Promise<DeleteBrnIdResSchema> {
		// check if profile exist
		await this.profileHelper.getUserProfileByUserId(userId);

		let msg = '';
		let action = '';
		try {
			const { rowCount = 0 } = await this.db
				.delete(smeProfileDbSchema)
				.where(
					and(
						eq(smeProfileDbSchema.UserId, userId),
						eq(smeProfileDbSchema.IdKey, req.BrnId)
					)
				)
				.execute();

			if (rowCount && rowCount > 0) {
				action = 'OK';
				msg = 'Selected BrnId was successfully deleted from the profile.';
			} else {
				action = 'NO_ACTION';
				msg = 'No matching data found to delete.';
			}
		} catch (error) {
			pinoLog.error('deleteBrnIdFromProfile: ', error);
			throw new UE_ERROR(
				'Error when delete row from mw_sme_profile',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId, response: error }
			);
		}

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: action,
				Message: msg
			}
		};
	}

	// PENDING (USER REQUIREMENT) TO BE FINALIZED
	// async addSmeDepartment(
	// 	userId: string,
	// 	req: AddSmeDeptReq
	// ): Promise<AddSmeDeptRes> {
	// 	// check if profile exist and BrnId is valid
	// 	const smeProfileRes: SelectSmeProfile =
	// 		await this.smeHelper.getSmeProfileInfo(userId, req);

	// 	// later to check access by Role(s)
	// 	if (smeProfileRes.Role?.includes(SmeUserRoleEnum.USER)) {
	// 		throw new UE_ERROR(
	// 			'This User dont have access to this endpoint. Required higher Role',
	// 			StatusCodeEnum.UNPROCESSABLE_ENTITY,
	// 			{ integrationId: this.integrationId, response: null }
	// 		);
	// 	}

	// 	try {
	// 		await this.db.insert(smeDeptDbSchema).values({
	// 			IdKey: req.BrnId,
	// 			DeptName: req.DeptName
	// 		});
	// 	} catch (error) {
	// 		pinoLog.error('addSmeDept: ', error);
	// 		throw new UE_ERROR(
	// 			'Error when insert into MwSmeDepartment table',
	// 			StatusCodeEnum.UE_INTERNAL_SERVER,
	// 			{ integrationId: this.integrationId, response: error }
	// 		);
	// 	}

	// 	return {
	// 		Success: true,
	// 		Code: 200,
	// 		IntegrationId: this.integrationId,
	// 		Response: {
	// 			Action: 'ADDED',
	// 			Message:
	// 				'New SME Department was successfully added into the selected SME Identification'
	// 		}
	// 	};
	// }

	// async listSmeDepartment(
	// 	userId: string,
	// 	req: SmeProfileReq
	// ): Promise<SmeDeptRes> {
	// 	// check if profile exist and Role allowed
	// 	const smeProfileRes: SelectSmeProfile =
	// 		await this.smeHelper.getSmeProfileInfo(userId, req);

	// 	const res: SmeDeptRes = {
	// 		Success: true,
	// 		Code: 200,
	// 		IntegrationId: this.integrationId,
	// 		Response: {} as SelectSmeDeptApi
	// 	};

	// 	res.Response = await this.smeHelper.getSmeDeptList(
	// 		smeProfileRes.IdKey || ''
	// 	);

	// 	return res;
	// }
}

export default Sme;
