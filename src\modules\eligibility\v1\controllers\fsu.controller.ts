import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia, { t } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type FsuEligibilityRes,
	fsuEligibilityResSchema
} from '../schemas/api/fsu.schema';
import FsuEligibility from '../services/fsu.service';

const fsuV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			FsuEligibility: new FsuEligibility(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/fsu',
		async (ctx): Promise<FsuEligibilityRes> => {
			const segment = ctx.headers.segment ?? 'CONSUMER';
			return await ctx.FsuEligibility.getFsuEligibility(
				ctx.query.ServiceId,
				segment
			);
		},
		{
			detail: {
				description:
					"FSU means Free Speed Upgrade. Check customer's FSU eligibility. <br><br> <b>Backend System:</b> EDWH",
				tags: ['Eligibility']
			},
			query: t.Object({
				ServiceId: t.String({ example: 'test@unifi', minLength: 1 })
			}),
			response: {
				200: fsuEligibilityResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default fsuV1Routes;
