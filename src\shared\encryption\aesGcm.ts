import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';

// AES-GCM with 128-bit key and 96-bit IV

const getSecretKeys = (
	scope: string
): { secretKey: string; saltKey: string } => {
	if (scope === 'WERAS') {
		return {
			secretKey: process.env.WERAS_SECRET_KEY ?? '',
			saltKey: process.env.WERAS_SALT_KEY ?? ''
		};
	}

	return {
		secretKey: process.env.SECRET_KEY ?? '',
		saltKey: process.env.SALT_KEY ?? ''
	};
};

const generateKey = async (
	secretKey: string,
	saltKey: string
): Promise<CryptoKey> => {
	const encoder = new TextEncoder();
	const passphraseKey = encoder.encode(secretKey);

	const keyMaterial = await crypto.subtle.importKey(
		'raw',
		passphraseKey,
		{ name: 'PBKDF2' },
		false,
		['deriveKey']
	);

	const key = await crypto.subtle.deriveKey(
		{
			name: 'PBKDF2',
			salt: encoder.encode(saltKey),
			iterations: 100000,
			hash: 'SHA-256'
		},
		keyMaterial,
		{ name: 'AES-GCM', length: 256 },
		true,
		['encrypt', 'decrypt']
	);

	if (!(key instanceof CryptoKey)) {
		throw new UE_ERROR(
			'Generated key is not a CryptoKey',
			StatusCodeEnum.FORBIDDEN_ERROR
		);
	}
	return key;
};

export const encrypt = async (
	text: string,
	scope = 'UNIFI'
): Promise<string> => {
	const { secretKey, saltKey } = getSecretKeys(scope);
	const key = await generateKey(secretKey, saltKey);

	const encoder = new TextEncoder();
	const data = encoder.encode(text);
	const iv = crypto.getRandomValues(new Uint8Array(12));

	const encrypted = await crypto.subtle.encrypt(
		{
			name: 'AES-GCM',
			iv: iv
		},
		key,
		data
	);

	// Combine IV and encrypted data into a single buffer
	const buffer = new Uint8Array(iv.length + encrypted.byteLength);
	buffer.set(iv, 0);
	buffer.set(new Uint8Array(encrypted), iv.length);

	return Buffer.from(buffer).toString('hex');
};

export const decrypt = async (
	hexString: string,
	scope = 'UNIFI'
): Promise<string> => {
	try {
		const { secretKey, saltKey } = getSecretKeys(scope);
		const key = await generateKey(secretKey, saltKey);
		// Convert hex string to buffer
		const buffer = Buffer.from(hexString, 'hex');
		const iv = buffer.subarray(0, 12); // Extract IV (first 12 bytes)
		const data = buffer.subarray(12); // Extract encrypted data

		const decrypted = await crypto.subtle.decrypt(
			{
				name: 'AES-GCM',
				iv: iv
			},
			key,
			data
		);

		const decoder = new TextDecoder();
		return decoder.decode(decrypted);
	} catch (error) {
		throw new UE_ERROR(
			'Decryption failed. Please check your encrypted data.',
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}
};
