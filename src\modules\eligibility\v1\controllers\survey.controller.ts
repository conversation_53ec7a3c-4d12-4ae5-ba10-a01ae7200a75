import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type VocEligibilityRes,
	vocEligibilityResSchema
} from '../schemas/api/survey.schema';
import SurveyEligibility from '../services/survey.service';

const surveyV1Routes = new Elysia({ prefix: '/survey' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			SurveyEligibility: new SurveyEligibility(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/tnps',
		async (ctx): Promise<VocEligibilityRes> => {
			return await ctx.SurveyEligibility.getVocEligibility();
		},
		{
			detail: {
				description:
					"Check customer's eligibility by VOC information. <br><br> <b>Backend System:</b> NOVA SIEBEL <br> <b>Table:</b> qualtrix_tnps_user_details",
				tags: ['Eligibility']
			},
			response: {
				200: vocEligibilityResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default surveyV1Routes;
