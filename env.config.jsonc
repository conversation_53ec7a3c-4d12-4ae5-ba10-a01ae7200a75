{"dev": {"SERVER_PREFIX_PATH": "", "WSO2_RESERVE_ORDER_URL": "https://api.apigate.tm.com.my/t/tm.com.my/ReserveOrderSIT/1.0.0/reserveorder", "WSO2_UNRESERVE_ORDER_URL": "https://api.apigate.tm.com.my/t/tm.com.my/UnreserveOrderSIT/1.0.0/unreserveorder", "WSO2_APIM_HOURLY_TOKEN": "https://apigw.dev.tmoip.tm.com.my/token", "WSO2_MMAG_TOKEN_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sd/dev/1.0.0/authentication.asmx/generatetoken", "WSO2_CHECK_STOCK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sd/dev/1.0.0/Inventory.asmx/query", "WSO2_BOOK_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/AppointmentBookingPortal", "WSO2_SWIFT_APPOINTMENT_CREATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/SWIFTAppointmentCreate", "WSO2_NOVA_ID_RESET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/ResetActivityId", "WSO2_RESERVE_IPTV_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/sdp/RESERVELOGIN/PerformLogin", "WSO2_RETRIEVE_CUSTOMER_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/customer/accounts", "WSO2_RETRIEVE_SERVICE_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/service/accounts", "WSO2_ORDER_REVIEW": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/review", "WSO2_ORDER_TRACKING": "http://localhost:3002/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/tracking", "WSO2_ORDER_SUBMIT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/dev/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/OrderSubmit", "WSO2_ORDER_MONITORING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/dev/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/NOVA/monitoring", "WSO2_SR_RETRIEVE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/sr/retrieve", "WSO2_UPDATE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/change/cancel/appointment", "WSO2_RETRIEVE_APPOINTMENT_SLOT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/appointment/slot", "WSO2_TMFORCE_ORDER_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderDetails", "WSO2_TMFORCE_ORDER_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderProgressUpdate", "WSO2_TMFORCE_TT_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProgressUpdate", "WSO2_TMFORCE_TECHNICIAN_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/TechnicianDetails", "WSO2_TMFORCE_POSTPONE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProposedReturnCustomerResponse", "WSO2_TMFORCE_ACCEPTANCE_FORM": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcotmforceapi/1.0.0/eai/dac/myunifi/ws/download-saf", "WSO2_SIEBEL_UPDATE_CONFIRM_FLAG": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/UpdateConfirmFlag", "WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/UpdateCustomerResponse", "WSO2_RETRIEVE_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/billing/details", "WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/concise/RetrieveConciseAccountDetails", "WSO2_PDF_ANNUAL_BILL_STATEMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tmrarchivalwso2api/1.0.0/sba/sync/myunifi/pdfbill", "WSO2_RETRIEVE_BILL_LINK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/bill/link", "WSO2_HARD_SOFT_BUNDLE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/novaeaiwso2api/1.0.0/sba/pimcore/novaogg/QueryHardSoftBundle", "WSO2_EXP_DISCOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/novabrmwso2api/1.0.0/v2/sba/sync/getexpdiscount", "WSO2_CREATE_SR": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/create/sr", "WSO2_NOVA_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBillingProfile", "WSO2_OUTSTANDING_AMOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/outstanding/amount", "WSO2_UPDATE_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/update/billing/profile", "WSO2_CONCISE_CUST_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/concisewso2api/1.0.0/sba/sync/myunifi/concisecustinfo", "WSO2_IBILL_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/iBill/retrieve/bill/details", "WSO2_AUTOPAY_SETTING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/register/autopay", "WSO2_AUTOPAY_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/check/autopay", "WSO2_SEND_EMAIL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/emailwso2api/1.0.0/sba/messaging/xe/mt/email", "WSO2_SEND_EMAIL_WITH_ATTACHMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/emailwso2api/1.0.0/sba/email/v2/xe/mt", "WSO2_SEND_SMS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/sms/smsoutbound/1.0.0/sba/uci/xe/mt/sms", "WSO2_SSM_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/cpc/retrieve/ssminfo", "WSO2_CUSTOMER_PROFILE_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/CustomerProfileCheck", "WSO2_DMS_CREDIT_SCORE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/edmswso2api/1.0.0/v2/sba/sync/xe/siebel/nova/db/eDMSCreditUtilization", "WSO2_EDWH_MOANA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/edwhwso2api/1.0.0/sba/sync/myunifi/MOANAEligibility", "WSO2_NOVA_TRANSFER_REQUEST_STATUS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/OpenTransferRequest", "WSO2_CREATE_SR_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateSR", "WSO2_CREATE_SR_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateSR", "WSO2_CREATE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateCustInfo", "WSO2_CREATE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateCustInfo", "WSO2_RETRIEVE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveCustInfo", "WSO2_RETRIEVE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/RetrieveCustInfo", "WSO2_CREATE_TT_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateTT", "WSO2_CREATE_TT_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateTT", "WSO2_RETRIEVE_NTT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/next/RetrieveLRInfo", "WSO2_CTT_CHECK_ELIGIBILITY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveUnclaimedCTT", "WSO2_REBATE_SUBMIT_TICKET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/BulkAdjustmentCreate", "WSO2_SUBMIT_NES_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/survey/detail/insert", "WSO2_EASYFIX_TNPS_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tnpswso2api/1.0.0/sba/sync/easyfix/InitialSurveyResult", "WSO2_ADDRESS_BY_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByAddressIdAndAddressDP", "WSO2_ADDRESS_BY_COORDINATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByLongitudeAndLatitude", "WSO2_ADDRESS_BY_KEYWORD_STATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByKeywordAndState", "WSO2_CREATE_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/octroikasit/1.0.0/troika/demand", "WSO2_QUERY_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/octroikasit/1.0.0/troika/demand/query", "WSO2_RETRIEVE_BUNDLE_DEVICE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBundleDevice", "OMG_GET_OTT_SUBSCRIPTION": "https://omg.hypp.tv/api3/apps/getOttSubscription", "OMG_NEW_OTT_ORDER": "https://omg.hypp.tv/api3/apps/newOttOrder", "OMG_NEW_OTT_SWAP_ORDER": "https://omg.hypp.tv/api3/apps/newOttSwapOrder", "OMG_VERIFY_OTT_USER_ID": "https://omg.hypp.tv/api3/apps/verifyOttUserId", "OMG_GET_OTT_ENTITLEMENT": "https://omg.hypp.tv/api3/apps/getOttEntitlement", "OMG_NETFLIX_GET_TOKEN": "https://omg.hypp.tv/api3/apps/getNetflixToken", "OMG_NETFLIX_GET_PLAN": "https://omg.hypp.tv/api3/apps/getNetflixPlan", "OMG_NETFLIX_CHANGE_PLAN_ORDER": "https://omg.hypp.tv/api3/apps/newNetflixPlanChangeOrder", "OMG_NETFLIX_CANCEL": "https://omg.hypp.tv/api3/apps/cancelNetflix", "OMG_HBO_GET_TOKEN": "https://omg.hypp.tv/api3/apps/getHBOToken", "OMG_HBO_GET_BUNDLE": "https://omg.hypp.tv/api3/apps/getHBOBundle", "OMG_DISNEY_CHANGE_MOBILE_NO": "https://omg.hypp.tv/api3/apps/disneyChangeMobileNo", "OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL": "https://omg.hypp.tv/api3/apps/getotturl-alacarte", "OMG_HBO_GET_ACTIVATION_BUNDLE_URL": "https://omg.hypp.tv/api3/apps/getotturl-bundle", "AUTOPAY_REGISTRATION_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/autopay-signup", "AUTOPAY_MODIFICATION_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/autopay-modify", "AUTOPAY_TERMINATION_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/autopay-terminate", "OSES_PAYMENT_RECEIPT_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/payment-receipt", "CLOUD_CONNECT_ECOMMERCE_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/ecommerce-hub", "SR_CREATE_REVAMP_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/service-terminate", "OSES_PORTAL_BILL_MERCHANT_ID": "DICEPORTALTEST", "OSES_CMC_SMS_MERCHANT_ID": "MYUNIFICMCTEST", "OSES_SMS_MERCHANT_ID": "MYUNIFITEST", "OSES_APP_MERCHANT_ID": "MYUNIFIBILLTES", "OSES_RETURN_URL": "https://myunifi-dev.myu.unifi.com.my/ue/api/v1/payment/callback/oses/response", "OSES_URL": "https://osesuat.tm.com.my/oses/ReqPaymentMode.jsp", "OSES_REVENUE_CODE_ICP": "014", "OSES_REVENUE_CODE_NOVA": "751", "CLOUD_CONNECT_CLIENT_LOGIN": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Auth/ClientCredential", "CLOUD_CONNECT_CLIENT_ID": "css.apps", "CLOUD_CONNECT_TENANT_REGISTER": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Tenant/Register", "ECOMMERCE_LOGIN": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/login", "ECOMMERCE_USERNAME": "<EMAIL>", "ECOMMERCE_REGISTER": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/tm/users", "PRIVATE_PDF_DOMAIN": "https://archival-dev.tm.com.my", "PUBLIC_PDF_DOMAIN": "https://portal-sit.myu.unifi.com.my", "SENDGRID_SEND_EMAIL": "https://api.sendgrid.com/v3/mail/send", "WERAS_BASE_URL": "https://rewardsstg.unifi.com.my", "WERAS_CLIENT_ID": "14", "WERAS_USERNAME": "<EMAIL>", "WERAS_TOKEN": "https://rewardsstg.unifi.com.my/oauth/token", "WERAS_GET_ITEMS": "https://rewardsstg.unifi.com.my/api/items", "WERAS_GET_REDEEM_ITEMS": "https://rewardsstg.unifi.com.my/api/redeem-item", "WERAS_GET_MEMBERSHIP": "https://rewardsstg.unifi.com.my/api/weras/get-membership-ua", "WERAS_GET_CUSTOMER_BILLS": "https://rewardsstg.unifi.com.my/api/weras/customer-bills", "WERAS_GET_PROMOTION_LIST": "https://rewardsstg.unifi.com.my/api/get-promotion-lists", "WERAS_GET_MY_REWARDS": "https://rewardsstg.unifi.com.my/api/rewards/get-my-rewards", "WERAS_GET_TRANSACTION": "https://rewardsstg.unifi.com.my/api/get-transaction", "WERAS_GET_ONLINE_CATALOGUE": "https://rewardsstg.unifi.com.my/api/online-items-category", "WERAS_UPDATE_REWARDS_FLAG": "https://rewardsstg.unifi.com.my/api/rewards/update-rewards-flag", "WERAS_PERSONALISED_REPORTING": "https://rewardsstg.unifi.com.my/api/weras/get-personalized-details", "OTT_VAR_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-var", "OTT_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-ultimate", "OTT_SWAPPING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-swapping", "OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-swapping-fail", "OTT_ALACARTE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-alacarte", "OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-ultimate-plus", "OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-ultimate-max", "TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL": "http://host.docker.internal:3004/api/start/user-task", "TEMPORAL_TRIGGER_WORKFLOW_URL": "http://localhost:3004/api/start/main", "DISNEY_CHANGE_MOBILE_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/disney-change-mobile", "CX_EMAIL_ADDRESS": "<EMAIL>", "CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/meshwifi-email", "CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/cloudgaming-email", "CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/varnam-email", "CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/aneka-email", "CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/ruby-email", "CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/ultimate-email", "CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/upb-email", "CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/sme-addon-smart-device-email", "TAAS_APIM_HOURLY_TOKEN": "https://gateway.dev.tmoip.tm.com.my/token", "TAAS_SERVICE_DETAILS_URL": "https://gateway.dev.tmoip.tm.com.my/t/tm.com.my/dev/service-details-retrieve/1.0.0/v3/sba/sync/siebel/nova/ServiceDetailsRetrieve", "MMAG_TRACK_ORDERS_URL": "https://dop-sit.mmag.com.my/rest/TM/Nova/delivery.asmx/track", "NETCORE_SEND_EMAIL_URL": "https://apim-dev/emailapi.netcorecloud.net/v6/mail/send", "NETCORE_ACTIVITY_URL": "https://apim-dev/api2.netcoresmartech.com/v1/activity/upload"}, "sit": {"SERVER_PREFIX_PATH": "/ue", "WSO2_RESERVE_ORDER_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/ReserveOrderSIT/1.0.0/reserveorder", "WSO2_UNRESERVE_ORDER_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/UnreserveOrderSIT/1.0.0/unreserveorder", "WSO2_APIM_HOURLY_TOKEN": "https://apigw.dev.tmoip.tm.com.my/token", "WSO2_MMAG_TOKEN_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sd/dev/1.0.0/authentication.asmx/generatetoken", "WSO2_CHECK_STOCK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sd/dev/1.0.0/Inventory.asmx/query", "WSO2_BOOK_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/AppointmentBookingPortal", "WSO2_SWIFT_APPOINTMENT_CREATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/SWIFTAppointmentCreate", "WSO2_NOVA_ID_RESET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/ResetActivityId", "WSO2_RESERVE_IPTV_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/sdp/RESERVELOGIN/PerformLogin", "WSO2_RETRIEVE_CUSTOMER_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/customer/accounts", "WSO2_RETRIEVE_SERVICE_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/service/accounts", "WSO2_ORDER_REVIEW": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/review", "WSO2_ORDER_TRACKING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/tracking", "WSO2_ORDER_SUBMIT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/OrderSubmit", "WSO2_ORDER_MONITORING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/NOVA/monitoring", "WSO2_SR_RETRIEVE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/sr/retrieve", "WSO2_UPDATE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/change/cancel/appointment", "WSO2_RETRIEVE_APPOINTMENT_SLOT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/appointment/slot", "WSO2_TMFORCE_ORDER_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderDetails", "WSO2_TMFORCE_ORDER_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderProgressUpdate", "WSO2_TMFORCE_TT_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProgressUpdate", "WSO2_TMFORCE_TECHNICIAN_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/TechnicianDetails", "WSO2_TMFORCE_POSTPONE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProposedReturnCustomerResponse", "WSO2_TMFORCE_ACCEPTANCE_FORM": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcotmforceapi/1.0.0/eai/dac/myunifi/ws/download-saf", "WSO2_SIEBEL_UPDATE_CONFIRM_FLAG": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/UpdateConfirmFlag", "WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/UpdateCustomerResponse", "WSO2_RETRIEVE_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/billing/details", "WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/concise/RetrieveConciseAccountDetails", "WSO2_PDF_ANNUAL_BILL_STATEMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tmrarchivalwso2api/1.0.0/sba/sync/myunifi/pdfbill", "WSO2_RETRIEVE_BILL_LINK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/bill/link", "WSO2_HARD_SOFT_BUNDLE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/novaeaiwso2api/1.0.0/sba/pimcore/novaogg/QueryHardSoftBundle", "WSO2_EXP_DISCOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/novabrmwso2api/1.0.0/v2/sba/sync/getexpdiscount", "WSO2_CREATE_SR": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/create/sr", "WSO2_NOVA_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBillingProfile", "WSO2_OUTSTANDING_AMOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/outstanding/amount", "WSO2_UPDATE_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/update/billing/profile", "WSO2_CONCISE_CUST_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/concisewso2api/1.0.0/sba/sync/myunifi/concisecustinfo", "WSO2_IBILL_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/iBill/retrieve/bill/details", "WSO2_AUTOPAY_SETTING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/register/autopay", "WSO2_AUTOPAY_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/check/autopay", "WSO2_SEND_EMAIL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/emailwso2api/1.0.0/sba/messaging/xe/mt/email", "WSO2_SEND_EMAIL_WITH_ATTACHMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/emailwso2api/1.0.0/sba/email/v2/xe/mt", "WSO2_SEND_SMS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/sms/smsoutbound/1.0.0/sba/uci/xe/mt/sms", "WSO2_SSM_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/cpc/retrieve/ssminfo", "WSO2_CUSTOMER_PROFILE_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/CustomerProfileCheck", "WSO2_DMS_CREDIT_SCORE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/edmswso2api/1.0.0/v2/sba/sync/xe/siebel/nova/db/eDMSCreditUtilization", "WSO2_EDWH_MOANA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/edwhwso2api/1.0.0/sba/sync/myunifi/MOANAEligibility", "WSO2_NOVA_TRANSFER_REQUEST_STATUS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/OpenTransferRequest", "WSO2_CREATE_SR_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateSR", "WSO2_CREATE_SR_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateSR", "WSO2_CREATE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateCustInfo", "WSO2_CREATE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateCustInfo", "WSO2_RETRIEVE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveCustInfo", "WSO2_RETRIEVE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/RetrieveCustInfo", "WSO2_CREATE_TT_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateTT", "WSO2_CREATE_TT_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateTT", "WSO2_RETRIEVE_NTT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/next/RetrieveLRInfo", "WSO2_CTT_CHECK_ELIGIBILITY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveUnclaimedCTT", "WSO2_REBATE_SUBMIT_TICKET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/BulkAdjustmentCreate", "WSO2_SUBMIT_NES_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/survey/detail/insert", "WSO2_EASYFIX_TNPS_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tnpswso2api/1.0.0/sba/sync/easyfix/InitialSurveyResult", "WSO2_ADDRESS_BY_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByAddressIdAndAddressDP", "WSO2_ADDRESS_BY_COORDINATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByLongitudeAndLatitude", "WSO2_ADDRESS_BY_KEYWORD_STATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByKeywordAndState", "WSO2_CREATE_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/octroikasit/1.0.0/troika/demand", "WSO2_QUERY_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/octroikasit/1.0.0/troika/demand/query", "WSO2_RETRIEVE_BUNDLE_DEVICE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/sit/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBundleDevice", "OMG_GET_OTT_SUBSCRIPTION": "https://omg.hypp.tv/api3/apps/getOttSubscription", "OMG_NEW_OTT_ORDER": "https://omg.hypp.tv/api3/apps/newOttOrder", "OMG_NEW_OTT_SWAP_ORDER": "https://omg.hypp.tv/api3/apps/newOttSwapOrder", "OMG_VERIFY_OTT_USER_ID": "https://omg.hypp.tv/api3/apps/verifyOttUserId", "OMG_GET_OTT_ENTITLEMENT": "https://omg.hypp.tv/api3/apps/getOttEntitlement", "OMG_NETFLIX_GET_TOKEN": "https://omg.hypp.tv/api3/apps/getNetflixToken", "OMG_NETFLIX_GET_PLAN": "https://omg.hypp.tv/api3/apps/getNetflixPlan", "OMG_NETFLIX_CHANGE_PLAN_ORDER": "https://omg.hypp.tv/api3/apps/newNetflixPlanChangeOrder", "OMG_NETFLIX_CANCEL": "https://omg.hypp.tv/api3/apps/cancelNetflix", "OMG_HBO_GET_TOKEN": "https://omg.hypp.tv/api3/apps/getHBOToken", "OMG_HBO_GET_BUNDLE": "https://omg.hypp.tv/api3/apps/getHBOBundle", "OMG_DISNEY_CHANGE_MOBILE_NO": "https://omg.hypp.tv/api3/apps/disneyChangeMobileNo", "OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL": "https://omg.hypp.tv/api3/apps/getotturl-alacarte", "OMG_HBO_GET_ACTIVATION_BUNDLE_URL": "https://omg.hypp.tv/api3/apps/getotturl-bundle", "AUTOPAY_REGISTRATION_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/autopay-signup", "AUTOPAY_MODIFICATION_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/autopay-modify", "AUTOPAY_TERMINATION_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/autopay-terminate", "OSES_PAYMENT_RECEIPT_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/payment-receipt", "CLOUD_CONNECT_ECOMMERCE_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/ecommerce-hub", "SR_CREATE_REVAMP_TEMPLATE": "https://selfcare.portal-sit.myu.unifi.com.my/email/service-terminate", "OSES_PORTAL_BILL_MERCHANT_ID": "DICEPORTALTEST", "OSES_CMC_SMS_MERCHANT_ID": "MYUNIFICMCTEST", "OSES_SMS_MERCHANT_ID": "MYUNIFITEST", "OSES_APP_MERCHANT_ID": "MYUNIFIBILLTES", "OSES_RETURN_URL": "https://myunifi-dev.myu.unifi.com.my/ue/api/v1/payment/callback/oses/response", "OSES_URL": "https://osesuat.tm.com.my/oses/ReqPaymentMode.jsp", "OSES_REVENUE_CODE_ICP": "014", "OSES_REVENUE_CODE_NOVA": "751", "CLOUD_CONNECT_CLIENT_LOGIN": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Auth/ClientCredential", "CLOUD_CONNECT_CLIENT_ID": "css.apps", "CLOUD_CONNECT_TENANT_REGISTER": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Tenant/Register", "ECOMMERCE_LOGIN": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/login", "ECOMMERCE_USERNAME": "<EMAIL>", "ECOMMERCE_REGISTER": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/tm/users", "PRIVATE_PDF_DOMAIN": "https://archival-dev.tm.com.my", "PUBLIC_PDF_DOMAIN": "https://portal-sit.myu.unifi.com.my", "SENDGRID_SEND_EMAIL": "https://api.sendgrid.com/v3/mail/send", "WERAS_BASE_URL": "https://rewardssit.unifi.com.my", "WERAS_CLIENT_ID": "15", "WERAS_USERNAME": "<EMAIL>", "WERAS_TOKEN": "https://rewardssit.unifi.com.my/oauth/token", "WERAS_GET_ITEMS": "https://rewardssit.unifi.com.my/api/items", "WERAS_GET_REDEEM_ITEMS": "https://rewardssit.unifi.com.my/api/redeem-item", "WERAS_GET_MEMBERSHIP": "https://rewardssit.unifi.com.my/api/weras/get-membership-ua", "WERAS_GET_CUSTOMER_BILLS": "https://rewardssit.unifi.com.my/api/weras/customer-bills", "WERAS_GET_PROMOTION_LIST": "https://rewardssit.unifi.com.my/api/get-promotion-lists", "WERAS_GET_MY_REWARDS": "https://rewardssit.unifi.com.my/api/rewards/get-my-rewards", "WERAS_GET_TRANSACTION": "https://rewardssit.unifi.com.my/api/get-transaction", "WERAS_GET_ONLINE_CATALOGUE": "https://rewardssit.unifi.com.my/api/online-items-category", "WERAS_UPDATE_REWARDS_FLAG": "https://rewardssit.unifi.com.my/api/rewards/update-rewards-flag", "WERAS_PERSONALISED_REPORTING": "https://rewardssit.unifi.com.my/api/weras/get-personalized-details", "OTT_VAR_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-var", "OTT_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-ultimate", "OTT_SWAPPING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-swapping", "OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-swapping-fail", "OTT_ALACARTE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-alacarte", "OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-ultimate-plus", "OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/tvapp-email-ultimate-max", "TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/user-task", "TEMPORAL_TRIGGER_WORKFLOW_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/main", "DISNEY_CHANGE_MOBILE_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/disney-change-mobile", "CX_EMAIL_ADDRESS": "<EMAIL>", "CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/meshwifi-email", "CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/cloudgaming-email", "CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/varnam-email", "CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/aneka-email", "CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/ruby-email", "CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/ultimate-email", "CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/upb-email", "CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-sit.myu.unifi.com.my/email/sme-addon-smart-device-email", "TAAS_APIM_HOURLY_TOKEN": "https://gateway.dev.tmoip.tm.com.my/token", "TAAS_SERVICE_DETAILS_URL": "https://gateway.dev.tmoip.tm.com.my/t/tm.com.my/sit/service-details-retrieve/1.0.0/v3/sba/sync/siebel/nova/ServiceDetailsRetrieve", "MMAG_TRACK_ORDERS_URL": "https://dop-sit.mmag.com.my/rest/TM/Nova/delivery.asmx/track", "NETCORE_SEND_EMAIL_URL": "https://apim-dev/emailapi.netcorecloud.net/v6/mail/send", "NETCORE_ACTIVITY_URL": "https://apim-dev/api2.netcoresmartech.com/v1/activity/upload"}, "uat": {"SERVER_PREFIX_PATH": "/ue-sit", "WSO2_RESERVE_ORDER_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/ReserveOrderPreprod/1.0.0/reserveorder", "WSO2_UNRESERVE_ORDER_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/UnreserveOrderPreprod/1.0.0/unreserveorder", "WSO2_APIM_HOURLY_TOKEN": "https://apigw.dev.tmoip.tm.com.my/token", "WSO2_MMAG_TOKEN_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/mmag/preprod/1.0.0/authentication.asmx/generatetoken", "WSO2_CHECK_STOCK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/mmag/preprod/1.0.0/Inventory.asmx/query", "WSO2_BOOK_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/AppointmentBookingPortal", "WSO2_SWIFT_APPOINTMENT_CREATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/SWIFTAppointmentCreate", "WSO2_NOVA_ID_RESET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/ResetActivityId", "WSO2_RESERVE_IPTV_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/sdp/RESERVELOGIN/PerformLogin", "WSO2_RETRIEVE_CUSTOMER_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/customer/accounts", "WSO2_RETRIEVE_SERVICE_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/service/accounts", "WSO2_ORDER_REVIEW": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/review", "WSO2_ORDER_TRACKING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/tracking", "WSO2_ORDER_SUBMIT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/OrderSubmit", "WSO2_ORDER_MONITORING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/NOVA/monitoring", "WSO2_SR_RETRIEVE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/sr/retrieve", "WSO2_UPDATE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/change/cancel/appointment", "WSO2_RETRIEVE_APPOINTMENT_SLOT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/appointment/slot", "WSO2_TMFORCE_ORDER_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderDetails", "WSO2_TMFORCE_ORDER_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderProgressUpdate", "WSO2_TMFORCE_TT_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProgressUpdate", "WSO2_TMFORCE_TECHNICIAN_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/TechnicianDetails", "WSO2_TMFORCE_POSTPONE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProposedReturnCustomerResponse", "WSO2_TMFORCE_ACCEPTANCE_FORM": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcotmforceapi/1.0.0/eai/dac/myunifi/ws/download-saf", "WSO2_SIEBEL_UPDATE_CONFIRM_FLAG": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/UpdateConfirmFlag", "WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/UpdateCustomerResponse", "WSO2_RETRIEVE_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/billing/details", "WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/concise/RetrieveConciseAccountDetails", "WSO2_PDF_ANNUAL_BILL_STATEMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tmrarchivalwso2api/1.0.0/sba/sync/myunifi/pdfbill", "WSO2_RETRIEVE_BILL_LINK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/bill/link", "WSO2_HARD_SOFT_BUNDLE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/novaeaiwso2api/1.0.0/sba/pimcore/novaogg/QueryHardSoftBundle", "WSO2_EXP_DISCOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/novabrmwso2api/1.0.0/v2/sba/sync/getexpdiscount", "WSO2_CREATE_SR": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/create/sr", "WSO2_NOVA_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBillingProfile", "WSO2_OUTSTANDING_AMOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/outstanding/amount", "WSO2_UPDATE_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/update/billing/profile", "WSO2_CONCISE_CUST_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/concisewso2api/1.0.0/sba/sync/myunifi/concisecustinfo", "WSO2_IBILL_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/iBill/retrieve/bill/details", "WSO2_AUTOPAY_SETTING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/register/autopay", "WSO2_AUTOPAY_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/check/autopay", "WSO2_SEND_EMAIL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/emailwso2api/1.0.0/sba/messaging/xe/mt/email", "WSO2_SEND_EMAIL_WITH_ATTACHMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/emailwso2api/1.0.0/sba/email/v2/xe/mt", "WSO2_SEND_SMS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/sms/smsoutbound/1.0.0/sba/uci/xe/mt/sms", "WSO2_SSM_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/cpc/retrieve/ssminfo", "WSO2_CUSTOMER_PROFILE_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/CustomerProfileCheck", "WSO2_DMS_CREDIT_SCORE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/edmswso2api/1.0.0/v2/sba/sync/xe/siebel/nova/db/eDMSCreditUtilization", "WSO2_EDWH_MOANA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/edwhwso2api/1.0.0/sba/sync/myunifi/MOANAEligibility", "WSO2_NOVA_TRANSFER_REQUEST_STATUS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/OpenTransferRequest", "WSO2_CREATE_SR_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateSR", "WSO2_CREATE_SR_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateSR", "WSO2_CREATE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateCustInfo", "WSO2_CREATE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateCustInfo", "WSO2_RETRIEVE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveCustInfo", "WSO2_RETRIEVE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/RetrieveCustInfo", "WSO2_CREATE_TT_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateTT", "WSO2_CREATE_TT_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateTT", "WSO2_RETRIEVE_NTT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/next/RetrieveLRInfo", "WSO2_CTT_CHECK_ELIGIBILITY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveUnclaimedCTT", "WSO2_REBATE_SUBMIT_TICKET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/BulkAdjustmentCreate", "WSO2_SUBMIT_NES_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/survey/detail/insert", "WSO2_EASYFIX_TNPS_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tnpswso2api/1.0.0/sba/sync/easyfix/InitialSurveyResult", "WSO2_ADDRESS_BY_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByAddressIdAndAddressDP", "WSO2_ADDRESS_BY_COORDINATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByLongitudeAndLatitude", "WSO2_ADDRESS_BY_KEYWORD_STATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByKeywordAndState", "WSO2_CREATE_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/dev2/octroika/1.0.0/troika/demand", "WSO2_QUERY_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/dev2/octroika/1.0.0/troika/demand", "WSO2_RETRIEVE_BUNDLE_DEVICE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBundleDevice", "OMG_GET_OTT_SUBSCRIPTION": "https://activate.unifi.com.my/api3-pp/apps/getOttSubscription", "OMG_NEW_OTT_ORDER": "https://activate.unifi.com.my/api3-pp/apps/newOttOrder", "OMG_NEW_OTT_SWAP_ORDER": "https://activate.unifi.com.my/api3-pp/apps/newOttSwapOrder", "OMG_VERIFY_OTT_USER_ID": "https://activate.unifi.com.my/api3-pp/apps/verifyOttUserId", "OMG_GET_OTT_ENTITLEMENT": "https://activate.unifi.com.my/api3-pp/apps/getOttEntitlement", "OMG_NETFLIX_GET_TOKEN": "https://activate.unifi.com.my/api3-pp/apps/getNetflixToken", "OMG_NETFLIX_GET_PLAN": "https://activate.unifi.com.my/api3-pp/apps/getNetflixPlan", "OMG_NETFLIX_CHANGE_PLAN_ORDER": "https://activate.unifi.com.my/api3-pp/apps/newNetflixPlanChangeOrder", "OMG_NETFLIX_CANCEL": "https://activate.unifi.com.my/api3-pp/apps/cancelNetflix", "OMG_HBO_GET_TOKEN": "https://activate.unifi.com.my/api3-pp/apps/getHBOToken", "OMG_HBO_GET_BUNDLE": "https://activate.unifi.com.my/api3-pp/apps/getHBOBundle", "OMG_DISNEY_CHANGE_MOBILE_NO": "https://activate.unifi.com.my/api3-pp/apps/disneyChangeMobileNo", "OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL": "https://activate.unifi.com.my/api3-pp/apps/getotturl-alacarte", "OMG_HBO_GET_ACTIVATION_BUNDLE_URL": "https://activate.unifi.com.my/api3-pp/apps/getotturl-bundle", "AUTOPAY_REGISTRATION_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/autopay-signup", "AUTOPAY_MODIFICATION_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/autopay-modify", "AUTOPAY_TERMINATION_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/autopay-terminate", "OSES_PAYMENT_RECEIPT_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/payment-receipt", "CLOUD_CONNECT_ECOMMERCE_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/ecommerce-hub", "SR_CREATE_REVAMP_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/service-terminate", "OSES_PORTAL_BILL_MERCHANT_ID": "DICEPORTALTEST", "OSES_CMC_SMS_MERCHANT_ID": "MYUNIFICMCTEST", "OSES_SMS_MERCHANT_ID": "MYUNIFITEST", "OSES_APP_MERCHANT_ID": "MYUNIFIBILLTES", "OSES_RETURN_URL": "https://myunifi-dev.myu.unifi.com.my/ue/api/v1/payment/callback/oses/response", "OSES_URL": "https://osesuat.tm.com.my/oses/ReqPaymentMode.jsp", "OSES_REVENUE_CODE_ICP": "014", "OSES_REVENUE_CODE_NOVA": "751", "CLOUD_CONNECT_CLIENT_LOGIN": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Auth/ClientCredential", "CLOUD_CONNECT_CLIENT_ID": "css.apps", "CLOUD_CONNECT_TENANT_REGISTER": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Tenant/Register", "ECOMMERCE_LOGIN": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/login", "ECOMMERCE_USERNAME": "<EMAIL>", "ECOMMERCE_REGISTER": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/tm/users", "PRIVATE_PDF_DOMAIN": "https://archival-dev.tm.com.my", "PUBLIC_PDF_DOMAIN": "https://portal-pre.myu.unifi.com.my", "SENDGRID_SEND_EMAIL": "https://api.sendgrid.com/v3/mail/send", "WERAS_BASE_URL": "https://rewardsstg.unifi.com.my", "WERAS_CLIENT_ID": "14", "WERAS_USERNAME": "<EMAIL>", "WERAS_TOKEN": "https://rewardsstg.unifi.com.my/oauth/token", "WERAS_GET_ITEMS": "https://rewardsstg.unifi.com.my/api/items", "WERAS_GET_REDEEM_ITEMS": "https://rewardsstg.unifi.com.my/api/redeem-item", "WERAS_GET_MEMBERSHIP": "https://rewardsstg.unifi.com.my/api/weras/get-membership-ua", "WERAS_GET_CUSTOMER_BILLS": "https://rewardsstg.unifi.com.my/api/weras/customer-bills", "WERAS_GET_PROMOTION_LIST": "https://rewardsstg.unifi.com.my/api/get-promotion-lists", "WERAS_GET_MY_REWARDS": "https://rewardsstg.unifi.com.my/api/rewards/get-my-rewards", "WERAS_GET_TRANSACTION": "https://rewardsstg.unifi.com.my/api/get-transaction", "WERAS_GET_ONLINE_CATALOGUE": "https://rewardsstg.unifi.com.my/api/online-items-category", "WERAS_UPDATE_REWARDS_FLAG": "https://rewardsstg.unifi.com.my/api/rewards/update-rewards-flag", "WERAS_PERSONALISED_REPORTING": "https://rewardsstg.unifi.com.my/api/weras/get-personalized-details", "OTT_VAR_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-var", "OTT_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-ultimate", "OTT_SWAPPING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-swapping", "OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-swapping-fail", "OTT_ALACARTE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-alacarte", "OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-ultimate-plus", "OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-ultimate-max", "TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/user-task", "TEMPORAL_TRIGGER_WORKFLOW_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/main", "DISNEY_CHANGE_MOBILE_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/disney-change-mobile", "CX_EMAIL_ADDRESS": "<EMAIL>", "CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/meshwifi-email", "CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/cloudgaming-email", "CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/varnam-email", "CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/aneka-email", "CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/ruby-email", "CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/ultimate-email", "CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/upb-email", "CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/sme-addon-smart-device-email", "TAAS_APIM_HOURLY_TOKEN": "https://gateway.dev.tmoip.tm.com.my/token", "TAAS_SERVICE_DETAILS_URL": "https://gateway.dev.tmoip.tm.com.my/t/tm.com.my/preprod/service-details-retrieve/1.0.0/v3/sba/sync/siebel/nova/ServiceDetailsRetrieve", "MMAG_TRACK_ORDERS_URL": "https://dop-preprod.mmag.com.my/rest/TM/Nova/delivery.asmx/track", "NETCORE_SEND_EMAIL_URL": "https://apim-dev/emailapi.netcorecloud.net/v6/mail/send", "NETCORE_ACTIVITY_URL": "https://apim-dev/api2.netcoresmartech.com/v1/activity/upload"}, "preprod": {"SERVER_PREFIX_PATH": "/ue", "WSO2_RESERVE_ORDER_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/ReserveOrderPreprod/1.0.0/reserveorder", "WSO2_UNRESERVE_ORDER_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/UnreserveOrderPreprod/1.0.0/unreserveorder", "WSO2_APIM_HOURLY_TOKEN": "https://apigw.dev.tmoip.tm.com.my/token", "WSO2_MMAG_TOKEN_URL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/mmag/preprod/1.0.0/authentication.asmx/generatetoken", "WSO2_CHECK_STOCK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/mmag/preprod/1.0.0/Inventory.asmx/query", "WSO2_BOOK_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/AppointmentBookingPortal", "WSO2_SWIFT_APPOINTMENT_CREATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/SWIFTAppointmentCreate", "WSO2_NOVA_ID_RESET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/ResetActivityId", "WSO2_RESERVE_IPTV_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/sdp/RESERVELOGIN/PerformLogin", "WSO2_RETRIEVE_CUSTOMER_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/customer/accounts", "WSO2_RETRIEVE_SERVICE_ACCOUNTS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/service/accounts", "WSO2_ORDER_REVIEW": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/review", "WSO2_ORDER_TRACKING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/tracking", "WSO2_ORDER_SUBMIT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/OrderSubmit", "WSO2_ORDER_MONITORING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/NOVA/monitoring", "WSO2_SR_RETRIEVE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/sr/retrieve", "WSO2_UPDATE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/change/cancel/appointment", "WSO2_RETRIEVE_APPOINTMENT_SLOT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/appointment/slot", "WSO2_TMFORCE_ORDER_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderDetails", "WSO2_TMFORCE_ORDER_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderProgressUpdate", "WSO2_TMFORCE_TT_PROGRESS_UPDATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProgressUpdate", "WSO2_TMFORCE_TECHNICIAN_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/TechnicianDetails", "WSO2_TMFORCE_POSTPONE_APPOINTMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProposedReturnCustomerResponse", "WSO2_TMFORCE_ACCEPTANCE_FORM": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcotmforceapi/1.0.0/eai/dac/myunifi/ws/download-saf", "WSO2_SIEBEL_UPDATE_CONFIRM_FLAG": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/UpdateConfirmFlag", "WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/UpdateCustomerResponse", "WSO2_RETRIEVE_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/billing/details", "WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/concise/RetrieveConciseAccountDetails", "WSO2_PDF_ANNUAL_BILL_STATEMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tmrarchivalwso2api/1.0.0/sba/sync/myunifi/pdfbill", "WSO2_RETRIEVE_BILL_LINK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/bill/link", "WSO2_HARD_SOFT_BUNDLE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/novaeaiwso2api/1.0.0/sba/pimcore/novaogg/QueryHardSoftBundle", "WSO2_EXP_DISCOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/novabrmwso2api/1.0.0/v2/sba/sync/getexpdiscount", "WSO2_CREATE_SR": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/create/sr", "WSO2_NOVA_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBillingProfile", "WSO2_OUTSTANDING_AMOUNT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/outstanding/amount", "WSO2_UPDATE_BILLING_PROFILE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/update/billing/profile", "WSO2_CONCISE_CUST_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/concisewso2api/1.0.0/sba/sync/myunifi/concisecustinfo", "WSO2_IBILL_BILLING_DETAILS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/iBill/retrieve/bill/details", "WSO2_AUTOPAY_SETTING": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/register/autopay", "WSO2_AUTOPAY_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/check/autopay", "WSO2_SEND_EMAIL": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/emailwso2api/1.0.0/sba/messaging/xe/mt/email", "WSO2_SEND_EMAIL_WITH_ATTACHMENT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/emailwso2api/1.0.0/sba/email/v2/xe/mt", "WSO2_SEND_SMS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/sms/smsoutbound/1.0.0/sba/uci/xe/mt/sms", "WSO2_SSM_INFO": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/cpc/retrieve/ssminfo", "WSO2_CUSTOMER_PROFILE_CHECK": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/CustomerProfileCheck", "WSO2_DMS_CREDIT_SCORE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/edmswso2api/1.0.0/v2/sba/sync/xe/siebel/nova/db/eDMSCreditUtilization", "WSO2_EDWH_MOANA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/edwhwso2api/1.0.0/sba/sync/myunifi/MOANAEligibility", "WSO2_NOVA_TRANSFER_REQUEST_STATUS": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/OpenTransferRequest", "WSO2_CREATE_SR_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateSR", "WSO2_CREATE_SR_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateSR", "WSO2_CREATE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateCustInfo", "WSO2_CREATE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateCustInfo", "WSO2_RETRIEVE_CUSTOMER_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveCustInfo", "WSO2_RETRIEVE_CUSTOMER_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/RetrieveCustInfo", "WSO2_CREATE_TT_NOVA": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateTT", "WSO2_CREATE_TT_ICP": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateTT", "WSO2_RETRIEVE_NTT": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/next/RetrieveLRInfo", "WSO2_CTT_CHECK_ELIGIBILITY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveUnclaimedCTT", "WSO2_REBATE_SUBMIT_TICKET": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/BulkAdjustmentCreate", "WSO2_SUBMIT_NES_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/survey/detail/insert", "WSO2_EASYFIX_TNPS_SURVEY": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tnpswso2api/1.0.0/sba/sync/easyfix/InitialSurveyResult", "WSO2_ADDRESS_BY_ID": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByAddressIdAndAddressDP", "WSO2_ADDRESS_BY_COORDINATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByLongitudeAndLatitude", "WSO2_ADDRESS_BY_KEYWORD_STATE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByKeywordAndState", "WSO2_CREATE_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/dev2/octroika/1.0.0/troika/demand", "WSO2_QUERY_TROIKA_DEMAND": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/dev2/octroika/1.0.0/troika/demand", "WSO2_RETRIEVE_BUNDLE_DEVICE": "https://apigw.dev.tmoip.tm.com.my/t/tm.com.my/preprod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBundleDevice", "OMG_GET_OTT_SUBSCRIPTION": "https://activate.unifi.com.my/api3-pp/apps/getOttSubscription", "OMG_NEW_OTT_ORDER": "https://activate.unifi.com.my/api3-pp/apps/newOttOrder", "OMG_NEW_OTT_SWAP_ORDER": "https://activate.unifi.com.my/api3-pp/apps/newOttSwapOrder", "OMG_VERIFY_OTT_USER_ID": "https://activate.unifi.com.my/api3-pp/apps/verifyOttUserId", "OMG_GET_OTT_ENTITLEMENT": "https://activate.unifi.com.my/api3-pp/apps/getOttEntitlement", "OMG_NETFLIX_GET_TOKEN": "https://activate.unifi.com.my/api3-pp/apps/getNetflixToken", "OMG_NETFLIX_GET_PLAN": "https://activate.unifi.com.my/api3-pp/apps/getNetflixPlan", "OMG_NETFLIX_CHANGE_PLAN_ORDER": "https://activate.unifi.com.my/api3-pp/apps/newNetflixPlanChangeOrder", "OMG_NETFLIX_CANCEL": "https://activate.unifi.com.my/api3-pp/apps/cancelNetflix", "OMG_HBO_GET_TOKEN": "https://activate.unifi.com.my/api3-pp/apps/getHBOToken", "OMG_HBO_GET_BUNDLE": "https://activate.unifi.com.my/api3-pp/apps/getHBOBundle", "OMG_DISNEY_CHANGE_MOBILE_NO": "https://activate.unifi.com.my/api3-pp/apps/disneyChangeMobileNo", "OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL": "https://activate.unifi.com.my/api3-pp/apps/getotturl-alacarte", "OMG_HBO_GET_ACTIVATION_BUNDLE_URL": "https://activate.unifi.com.my/api3-pp/apps/getotturl-bundle", "AUTOPAY_REGISTRATION_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/autopay-signup", "AUTOPAY_MODIFICATION_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/autopay-modify", "AUTOPAY_TERMINATION_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/autopay-terminate", "OSES_PAYMENT_RECEIPT_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/payment-receipt", "CLOUD_CONNECT_ECOMMERCE_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/ecommerce-hub", "SR_CREATE_REVAMP_TEMPLATE": "https://selfcare.portal-pre.myu.unifi.com.my/email/service-terminate", "OSES_PORTAL_BILL_MERCHANT_ID": "DICEPORTALTEST", "OSES_CMC_SMS_MERCHANT_ID": "MYUNIFICMCTEST", "OSES_SMS_MERCHANT_ID": "MYUNIFITEST", "OSES_APP_MERCHANT_ID": "MYUNIFIBILLTES", "OSES_RETURN_URL": "https://myunifi-dev.myu.unifi.com.my/ue/api/v1/payment/callback/oses/response", "OSES_URL": "https://osesuat.tm.com.my/oses/ReqPaymentMode.jsp", "OSES_REVENUE_CODE_ICP": "014", "OSES_REVENUE_CODE_NOVA": "751", "CLOUD_CONNECT_CLIENT_LOGIN": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Auth/ClientCredential", "CLOUD_CONNECT_CLIENT_ID": "css.apps", "CLOUD_CONNECT_TENANT_REGISTER": "https://apis-unifi-qa.alpha.cloud-connect.asia/api/Tenant/Register", "ECOMMERCE_LOGIN": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/login", "ECOMMERCE_USERNAME": "<EMAIL>", "ECOMMERCE_REGISTER": "https://sso-api-dev.ecommercehub.unifi.com.my/api/v1/tm/users", "PRIVATE_PDF_DOMAIN": "https://archival-dev.tm.com.my", "PUBLIC_PDF_DOMAIN": "https://portal-pre.myu.unifi.com.my", "SENDGRID_SEND_EMAIL": "https://api.sendgrid.com/v3/mail/send", "WERAS_BASE_URL": "https://rewardsstg.unifi.com.my", "WERAS_CLIENT_ID": "14", "WERAS_USERNAME": "<EMAIL>", "WERAS_TOKEN": "https://rewardsstg.unifi.com.my/oauth/token", "WERAS_GET_ITEMS": "https://rewardsstg.unifi.com.my/api/items", "WERAS_GET_REDEEM_ITEMS": "https://rewardsstg.unifi.com.my/api/redeem-item", "WERAS_GET_MEMBERSHIP": "https://rewardsstg.unifi.com.my/api/weras/get-membership-ua", "WERAS_GET_CUSTOMER_BILLS": "https://rewardsstg.unifi.com.my/api/weras/customer-bills", "WERAS_GET_PROMOTION_LIST": "https://rewardsstg.unifi.com.my/api/get-promotion-lists", "WERAS_GET_MY_REWARDS": "https://rewardsstg.unifi.com.my/api/rewards/get-my-rewards", "WERAS_GET_TRANSACTION": "https://rewardsstg.unifi.com.my/api/get-transaction", "WERAS_GET_ONLINE_CATALOGUE": "https://rewardsstg.unifi.com.my/api/online-items-category", "WERAS_UPDATE_REWARDS_FLAG": "https://rewardsstg.unifi.com.my/api/rewards/update-rewards-flag", "WERAS_PERSONALISED_REPORTING": "https://rewardsstg.unifi.com.my/api/weras/get-personalized-details", "OTT_VAR_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-var", "OTT_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-ultimate", "OTT_SWAPPING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-swapping", "OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-swapping-fail", "OTT_ALACARTE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-alacarte", "OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-ultimate-plus", "OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/tvapp-email-ultimate-max", "TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/user-task", "TEMPORAL_TRIGGER_WORKFLOW_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/main", "DISNEY_CHANGE_MOBILE_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/disney-change-mobile", "CX_EMAIL_ADDRESS": "<EMAIL>", "CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/meshwifi-email", "CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/cloudgaming-email", "CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/varnam-email", "CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/aneka-email", "CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/ruby-email", "CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/ultimate-email", "CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/upb-email", "CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/sme-addon-smart-device-email", "TAAS_APIM_HOURLY_TOKEN": "https://gateway.dev.tmoip.tm.com.my/token", "TAAS_SERVICE_DETAILS_URL": "https://gateway.dev.tmoip.tm.com.my/t/tm.com.my/preprod/service-details-retrieve/1.0.0/v3/sba/sync/siebel/nova/ServiceDetailsRetrieve", "MMAG_TRACK_ORDERS_URL": "https://dop-preprod.mmag.com.my/rest/TM/Nova/delivery.asmx/track", "NETCORE_SEND_EMAIL_URL": "https://apim-dev/emailapi.netcorecloud.net/v6/mail/send", "NETCORE_ACTIVITY_URL": "https://apim-dev/api2.netcoresmartech.com/v1/activity/upload"}, "staging": {"SERVER_PREFIX_PATH": "/ue", "WSO2_RESERVE_ORDER_URL": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/1.0.0/reserveorder", "WSO2_UNRESERVE_ORDER_URL": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/1.0.0/unreserveorder", "WSO2_APIM_HOURLY_TOKEN": "https://api.apigate.tm.com.my/token", "WSO2_MMAG_TOKEN_URL": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/prod/1.0.0/authentication.asmx/generatetoken", "WSO2_CHECK_STOCK": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/prod/1.0.0/Inventory.asmx/query", "WSO2_BOOK_APPOINTMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/AppointmentBookingPortal", "WSO2_SWIFT_APPOINTMENT_CREATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/SWIFTAppointmentCreate", "WSO2_NOVA_ID_RESET": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/ResetActivityId", "WSO2_RESERVE_IPTV_ID": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/sdp/RESERVELOGIN/PerformLogin", "WSO2_RETRIEVE_CUSTOMER_ACCOUNTS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/profiling/digital/cs/xe/retrieve/customer/accounts", "WSO2_RETRIEVE_SERVICE_ACCOUNTS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/service/accounts", "WSO2_ORDER_REVIEW": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/order/review", "WSO2_ORDER_TRACKING": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/tracking", "WSO2_ORDER_SUBMIT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/OrderSubmit", "WSO2_ORDER_MONITORING": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/NOVA/monitoring", "WSO2_SR_RETRIEVE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/sr/retrieve", "WSO2_UPDATE_APPOINTMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/change/cancel/appointment", "WSO2_RETRIEVE_APPOINTMENT_SLOT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/appointment/slot", "WSO2_TMFORCE_ORDER_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderDetails", "WSO2_TMFORCE_ORDER_PROGRESS_UPDATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderProgressUpdate", "WSO2_TMFORCE_TT_PROGRESS_UPDATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProgressUpdate", "WSO2_TMFORCE_TECHNICIAN_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/TechnicianDetails", "WSO2_TMFORCE_POSTPONE_APPOINTMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProposedReturnCustomerResponse", "WSO2_TMFORCE_ACCEPTANCE_FORM": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcotmforceapi/1.0.0/eai/dac/myunifi/ws/download-saf", "WSO2_SIEBEL_UPDATE_CONFIRM_FLAG": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/UpdateConfirmFlag", "WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/swift/UpdateCustomerResponse", "WSO2_RETRIEVE_BILLING_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/billing/details", "WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/concise/RetrieveConciseAccountDetails", "WSO2_PDF_ANNUAL_BILL_STATEMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tmarchivalwso2api/1.0.0/sba/sync/myunifi/pdfbill", "WSO2_RETRIEVE_BILL_LINK": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/bill/link", "WSO2_HARD_SOFT_BUNDLE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/novaeaiwso2api/1.0.0/sba/pimcore/novaogg/QueryHardSoftBundle", "WSO2_EXP_DISCOUNT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/novabrmwso2api/1.0.0/v2/sba/sync/getexpdiscount", "WSO2_CREATE_SR": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/create/sr", "WSO2_NOVA_BILLING_PROFILE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBillingProfile", "WSO2_OUTSTANDING_AMOUNT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/outstanding/amount", "WSO2_UPDATE_BILLING_PROFILE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/update/billing/profile", "WSO2_CONCISE_CUST_INFO": "https://api.apigate.tm.com.my/t/tm.com.my/prod/concisewso2api/1.0.0/sba/sync/myunifi/concisecustinfo", "WSO2_IBILL_BILLING_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/iBill/retrieve/bill/details", "WSO2_AUTOPAY_SETTING": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/register/autopay", "WSO2_AUTOPAY_CHECK": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/check/autopay", "WSO2_SEND_EMAIL": "https://api.apigate.tm.com.my/t/tm.com.my/prod/emailwso2api/1.0.0/sba/messaging/xe/mt/email", "WSO2_SEND_EMAIL_WITH_ATTACHMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/emailwso2api/1.0.0/sba/email/v2/xe/mt", "WSO2_SEND_SMS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/sms/smsoutbound/1.0.0/sba/uci/xe/mt/sms", "WSO2_SSM_INFO": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/cpc/retrieve/ssminfo", "WSO2_CUSTOMER_PROFILE_CHECK": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/CustomerProfileCheck", "WSO2_DMS_CREDIT_SCORE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/edmswso2api/1.0.0/v2/sba/sync/xe/siebel/nova/db/eDMSCreditUtilization", "WSO2_EDWH_MOANA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/edwhwso2api/1.0.0/sba/sync/myunifi/MOANAEligibility", "WSO2_NOVA_TRANSFER_REQUEST_STATUS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/OpenTransferRequest", "WSO2_CREATE_SR_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateSR", "WSO2_CREATE_SR_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateSR", "WSO2_CREATE_CUSTOMER_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/nova/CreateCustInfo", "WSO2_CREATE_CUSTOMER_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/icp/CreateCustInfo", "WSO2_RETRIEVE_CUSTOMER_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveCustInfo", "WSO2_RETRIEVE_CUSTOMER_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/RetrieveCustInfo", "WSO2_CREATE_TT_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/nova/CreateTT", "WSO2_CREATE_TT_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/icp/CreateTT", "WSO2_RETRIEVE_NTT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/next/RetrieveLRInfo", "WSO2_CTT_CHECK_ELIGIBILITY": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveUnclaimedCTT", "WSO2_REBATE_SUBMIT_TICKET": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/BulkAdjustmentCreate", "WSO2_SUBMIT_NES_SURVEY": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/survey/detail/insert", "WSO2_EASYFIX_TNPS_SURVEY": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tnpswso2api/1.0.0/sba/sync/easyfix/InitialSurveyResult", "WSO2_ADDRESS_BY_ID": "https://api.apigate.tm.com.my/t/tm.com.my/prod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByAddressIdAndAddressDP", "WSO2_ADDRESS_BY_COORDINATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByLongitudeAndLatitude", "WSO2_ADDRESS_BY_KEYWORD_STATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByKeywordAndState", "WSO2_CREATE_TROIKA_DEMAND": "https://api.apigate.tm.com.my/t/tm.com.my/prd/octroikaprd/1.0.0/troika/demand", "WSO2_QUERY_TROIKA_DEMAND": "https://api.apigate.tm.com.my/t/tm.com.my/prd/octroikaprd/1.0.0/troika/demand/query", "WSO2_RETRIEVE_BUNDLE_DEVICE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBundleDevice", "OMG_GET_OTT_SUBSCRIPTION": "https://activate.unifi.com.my/api3/apps/getOttSubscription", "OMG_NEW_OTT_ORDER": "https://activate.unifi.com.my/api3/apps/newOttOrder", "OMG_NEW_OTT_SWAP_ORDER": "https://activate.unifi.com.my/api3/apps/newOttSwapOrder", "OMG_VERIFY_OTT_USER_ID": "https://activate.unifi.com.my/api3/apps/verifyOttUserId", "OMG_GET_OTT_ENTITLEMENT": "https://activate.unifi.com.my/api3/apps/getOttEntitlement", "OMG_NETFLIX_GET_TOKEN": "https://activate.unifi.com.my/api3/apps/getNetflixToken", "OMG_NETFLIX_GET_PLAN": "https://activate.unifi.com.my/api3/apps/getNetflixPlan", "OMG_NETFLIX_CHANGE_PLAN_ORDER": "https://activate.unifi.com.my/api3/apps/newNetflixPlanChangeOrder", "OMG_NETFLIX_CANCEL": "https://activate.unifi.com.my/api3/apps/cancelNetflix", "OMG_HBO_GET_TOKEN": "https://activate.unifi.com.my/api3/apps/getHBOToken", "OMG_HBO_GET_BUNDLE": "https://activate.unifi.com.my/api3/apps/getHBOBundle", "OMG_DISNEY_CHANGE_MOBILE_NO": "https://activate.unifi.com.my/api3/apps/disneyChangeMobileNo", "OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL": "https://activate.unifi.com.my/api3/apps/getotturl-alacarte", "OMG_HBO_GET_ACTIVATION_BUNDLE_URL": "https://activate.unifi.com.my/api3/apps/getotturl-bundle", "AUTOPAY_REGISTRATION_TEMPLATE": "https://selfcare.unifi.com.my/email/autopay-signup", "AUTOPAY_MODIFICATION_TEMPLATE": "https://selfcare.unifi.com.my/email/autopay-modify", "AUTOPAY_TERMINATION_TEMPLATE": "https://selfcare.unifi.com.my/email/autopay-terminate", "OSES_PAYMENT_RECEIPT_TEMPLATE": "https://selfcare.unifi.com.my/email/payment-receipt", "CLOUD_CONNECT_ECOMMERCE_TEMPLATE": "https://selfcare.unifi.com.my/email/ecommerce-hub", "SR_CREATE_REVAMP_TEMPLATE": "https://selfcare.unifi.com.my/email/service-terminate", "OSES_PORTAL_BILL_MERCHANT_ID": "DICEPORTALBIL", "OSES_CMC_SMS_MERCHANT_ID": "MYUNIFICMC", "OSES_SMS_MERCHANT_ID": "MYUNIFISMS", "OSES_APP_MERCHANT_ID": "MYUNIFIBILL", "OSES_RETURN_URL": "https://myunifi-dev.myu.unifi.com.my/ue/api/v1/payment/callback/oses/response", "OSES_URL": "https://oses.tm.com.my/oses/ReqPaymentMode.jsp", "OSES_REVENUE_CODE_ICP": "014", "OSES_REVENUE_CODE_NOVA": "751", "CLOUD_CONNECT_CLIENT_LOGIN": "https://apis.cloudstorage.unifi.com.my/api/Auth/ClientCredential", "CLOUD_CONNECT_CLIENT_ID": "css.apps", "CLOUD_CONNECT_TENANT_REGISTER": "https://apis.cloudstorage.unifi.com.my/api/Tenant/Register", "ECOMMERCE_LOGIN": "https://sso-api.ecommercehub.unifi.com.my/api/v1/login", "ECOMMERCE_USERNAME": "<EMAIL>", "ECOMMERCE_REGISTER": "https://sso-api.ecommercehub.unifi.com.my/api/v1/tm/users", "PRIVATE_PDF_DOMAIN": "https://archival.tm.com.my", "PUBLIC_PDF_DOMAIN": "https://unifi.com.my", "SENDGRID_SEND_EMAIL": "https://api.sendgrid.com/v3/mail/send", "WERAS_BASE_URL": "https://rewards.unifi.com.my", "WERAS_CLIENT_ID": "13", "WERAS_USERNAME": "<EMAIL>", "WERAS_TOKEN": "https://rewards.unifi.com.my/oauth/token", "WERAS_GET_ITEMS": "https://rewards.unifi.com.my/api/items", "WERAS_GET_REDEEM_ITEMS": "https://rewards.unifi.com.my/api/redeem-item", "WERAS_GET_MEMBERSHIP": "https://rewards.unifi.com.my/api/weras/get-membership-ua", "WERAS_GET_CUSTOMER_BILLS": "https://rewards.unifi.com.my/api/weras/customer-bills", "WERAS_GET_PROMOTION_LIST": "https://rewards.unifi.com.my/api/get-promotion-lists", "WERAS_GET_MY_REWARDS": "https://rewards.unifi.com.my/api/rewards/get-my-rewards", "WERAS_GET_TRANSACTION": "https://rewards.unifi.com.my/api/get-transaction", "WERAS_GET_ONLINE_CATALOGUE": "https://rewards.unifi.com.my/api/online-items-category", "WERAS_UPDATE_REWARDS_FLAG": "https://rewards.unifi.com.my/api/rewards/update-rewards-flag", "WERAS_PERSONALISED_REPORTING": "https://rewards.unifi.com.my/api/weras/get-personalized-details", "OTT_VAR_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-var", "OTT_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-ultimate", "OTT_SWAPPING_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-swapping", "OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-swapping-fail", "OTT_ALACARTE_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-alacarte", "OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-ultimate-plus", "OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-ultimate-max", "TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/user-task", "TEMPORAL_TRIGGER_WORKFLOW_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/main", "DISNEY_CHANGE_MOBILE_EMAIL_URL": "https://selfcare.unifi.com.my/email/disney-change-mobile", "CX_EMAIL_ADDRESS": "<EMAIL>", "CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/meshwifi-email", "CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/cloudgaming-email", "CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/varnam-email", "CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/aneka-email", "CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/ruby-email", "CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/ultimate-email", "CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/upb-email", "CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL": "https://selfcare.portal-pre.myu.unifi.com.my/email/sme-addon-smart-device-email", "TAAS_APIM_HOURLY_TOKEN": "https://gateway.apigate.tm.com.my/token", "TAAS_SERVICE_DETAILS_URL": "https://gateway.apigate.tm.com.my/t/tm.com.my/prod/service-details-retrieve/1.0.0/v3/sba/sync/siebel/nova/ServiceDetailsRetrieve", "MMAG_TRACK_ORDERS_URL": "", "NETCORE_SEND_EMAIL_URL": "https://apim-dev/emailapi.netcorecloud.net/v6/mail/send", "NETCORE_ACTIVITY_URL": "https://apim-dev/api2.netcoresmartech.com/v1/activity/upload"}, "production": {"SERVER_PREFIX_PATH": "/ue", "WSO2_RESERVE_ORDER_URL": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/1.0.0/reserveorder", "WSO2_UNRESERVE_ORDER_URL": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/1.0.0/unreserveorder", "WSO2_APIM_HOURLY_TOKEN": "https://api.apigate.tm.com.my/token", "WSO2_MMAG_TOKEN_URL": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/prod/1.0.0/authentication.asmx/generatetoken", "WSO2_CHECK_STOCK": "https://api.apigate.tm.com.my/t/tm.com.my/mmag/prod/1.0.0/Inventory.asmx/query", "WSO2_BOOK_APPOINTMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/AppointmentBookingPortal", "WSO2_SWIFT_APPOINTMENT_CREATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/SWIFTAppointmentCreate", "WSO2_NOVA_ID_RESET": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/swift/ResetActivityId", "WSO2_RESERVE_IPTV_ID": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/sdp/RESERVELOGIN/PerformLogin", "WSO2_RETRIEVE_CUSTOMER_ACCOUNTS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/profiling/digital/cs/xe/retrieve/customer/accounts", "WSO2_RETRIEVE_SERVICE_ACCOUNTS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/service/accounts", "WSO2_ORDER_REVIEW": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/order/review", "WSO2_ORDER_TRACKING": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/tracking", "WSO2_ORDER_SUBMIT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/OrderSubmit", "WSO2_ORDER_MONITORING": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/order/NOVA/monitoring", "WSO2_SR_RETRIEVE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/sr/retrieve", "WSO2_UPDATE_APPOINTMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/change/cancel/appointment", "WSO2_RETRIEVE_APPOINTMENT_SLOT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/appointment/slot", "WSO2_TMFORCE_ORDER_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderDetails", "WSO2_TMFORCE_ORDER_PROGRESS_UPDATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/GetOrderProgressUpdate", "WSO2_TMFORCE_TT_PROGRESS_UPDATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProgressUpdate", "WSO2_TMFORCE_TECHNICIAN_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/TechnicianDetails", "WSO2_TMFORCE_POSTPONE_APPOINTMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/tmforce/ProposedReturnCustomerResponse", "WSO2_TMFORCE_ACCEPTANCE_FORM": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcotmforceapi/1.0.0/eai/dac/myunifi/ws/download-saf", "WSO2_SIEBEL_UPDATE_CONFIRM_FLAG": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/UpdateConfirmFlag", "WSO2_SWIFT_UPDATE_CUSTOMER_RESPONSE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/swift/UpdateCustomerResponse", "WSO2_RETRIEVE_BILLING_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/billing/details", "WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/concise/RetrieveConciseAccountDetails", "WSO2_PDF_ANNUAL_BILL_STATEMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tmarchivalwso2api/1.0.0/sba/sync/myunifi/pdfbill", "WSO2_RETRIEVE_BILL_LINK": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/bill/link", "WSO2_HARD_SOFT_BUNDLE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/novaeaiwso2api/1.0.0/sba/pimcore/novaogg/QueryHardSoftBundle", "WSO2_EXP_DISCOUNT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/novabrmwso2api/1.0.0/v2/sba/sync/getexpdiscount", "WSO2_CREATE_SR": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/create/sr", "WSO2_NOVA_BILLING_PROFILE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBillingProfile", "WSO2_OUTSTANDING_AMOUNT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/retrieve/outstanding/amount", "WSO2_UPDATE_BILLING_PROFILE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/update/billing/profile", "WSO2_CONCISE_CUST_INFO": "https://api.apigate.tm.com.my/t/tm.com.my/prod/concisewso2api/1.0.0/sba/sync/myunifi/concisecustinfo", "WSO2_IBILL_BILLING_DETAILS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/iBill/retrieve/bill/details", "WSO2_AUTOPAY_SETTING": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/register/autopay", "WSO2_AUTOPAY_CHECK": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/check/autopay", "WSO2_SEND_EMAIL": "https://api.apigate.tm.com.my/t/tm.com.my/prod/emailwso2api/1.0.0/sba/messaging/xe/mt/email", "WSO2_SEND_EMAIL_WITH_ATTACHMENT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/emailwso2api/1.0.0/sba/email/v2/xe/mt", "WSO2_SEND_SMS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/sms/smsoutbound/1.0.0/sba/uci/xe/mt/sms", "WSO2_SSM_INFO": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/cpc/retrieve/ssminfo", "WSO2_CUSTOMER_PROFILE_CHECK": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/cs/xe/CustomerProfileCheck", "WSO2_DMS_CREDIT_SCORE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/edmswso2api/1.0.0/v2/sba/sync/xe/siebel/nova/db/eDMSCreditUtilization", "WSO2_EDWH_MOANA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/edwhwso2api/1.0.0/sba/sync/myunifi/MOANAEligibility", "WSO2_NOVA_TRANSFER_REQUEST_STATUS": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/OpenTransferRequest", "WSO2_CREATE_SR_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/CreateSR", "WSO2_CREATE_SR_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/CreateSR", "WSO2_CREATE_CUSTOMER_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/nova/CreateCustInfo", "WSO2_CREATE_CUSTOMER_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/icp/CreateCustInfo", "WSO2_RETRIEVE_CUSTOMER_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveCustInfo", "WSO2_RETRIEVE_CUSTOMER_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/icp/RetrieveCustInfo", "WSO2_CREATE_TT_NOVA": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/nova/CreateTT", "WSO2_CREATE_TT_ICP": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/xe/siebel/icp/CreateTT", "WSO2_RETRIEVE_NTT": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/next/RetrieveLRInfo", "WSO2_CTT_CHECK_ELIGIBILITY": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveUnclaimedCTT", "WSO2_REBATE_SUBMIT_TICKET": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/BulkAdjustmentCreate", "WSO2_SUBMIT_NES_SURVEY": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/crud/digital/cs/xe/survey/detail/insert", "WSO2_EASYFIX_TNPS_SURVEY": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tnpswso2api/1.0.0/sba/sync/easyfix/InitialSurveyResult", "WSO2_ADDRESS_BY_ID": "https://api.apigate.tm.com.my/t/tm.com.my/prod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByAddressIdAndAddressDP", "WSO2_ADDRESS_BY_COORDINATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByLongitudeAndLatitude", "WSO2_ADDRESS_BY_KEYWORD_STATE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/granitewso2api/1.0.0/sba/sync/granite/UnifiPortal/AddressSearchByKeywordAndState", "WSO2_CREATE_TROIKA_DEMAND": "https://api.apigate.tm.com.my/t/tm.com.my/prd/octroikaprd/1.0.0/troika/demand", "WSO2_QUERY_TROIKA_DEMAND": "https://api.apigate.tm.com.my/t/tm.com.my/prd/octroikaprd/1.0.0/troika/demand/query", "WSO2_RETRIEVE_BUNDLE_DEVICE": "https://api.apigate.tm.com.my/t/tm.com.my/prod/tibcowso2api/1.0.0/sba/sync/digital/xe/siebel/nova/RetrieveBundleDevice", "OMG_GET_OTT_SUBSCRIPTION": "https://activate.unifi.com.my/api3/apps/getOttSubscription", "OMG_NEW_OTT_ORDER": "https://activate.unifi.com.my/api3/apps/newOttOrder", "OMG_NEW_OTT_SWAP_ORDER": "https://activate.unifi.com.my/api3/apps/newOttSwapOrder", "OMG_VERIFY_OTT_USER_ID": "https://activate.unifi.com.my/api3/apps/verifyOttUserId", "OMG_GET_OTT_ENTITLEMENT": "https://activate.unifi.com.my/api3/apps/getOttEntitlement", "OMG_NETFLIX_GET_TOKEN": "https://activate.unifi.com.my/api3/apps/getNetflixToken", "OMG_NETFLIX_GET_PLAN": "https://activate.unifi.com.my/api3/apps/getNetflixPlan", "OMG_NETFLIX_CHANGE_PLAN_ORDER": "https://activate.unifi.com.my/api3/apps/newNetflixPlanChangeOrder", "OMG_NETFLIX_CANCEL": "https://activate.unifi.com.my/api3/apps/cancelNetflix", "OMG_HBO_GET_TOKEN": "https://activate.unifi.com.my/api3/apps/getHBOToken", "OMG_HBO_GET_BUNDLE": "https://activate.unifi.com.my/api3/apps/getHBOBundle", "OMG_DISNEY_CHANGE_MOBILE_NO": "https://activate.unifi.com.my/api3/apps/disneyChangeMobileNo", "OMG_HBO_GET_ACTIVATION_ALA_CARTE_URL": "https://activate.unifi.com.my/api3/apps/getotturl-alacarte", "OMG_HBO_GET_ACTIVATION_BUNDLE_URL": "https://activate.unifi.com.my/api3/apps/getotturl-bundle", "AUTOPAY_REGISTRATION_TEMPLATE": "https://selfcare.unifi.com.my/email/autopay-signup", "AUTOPAY_MODIFICATION_TEMPLATE": "https://selfcare.unifi.com.my/email/autopay-modify", "AUTOPAY_TERMINATION_TEMPLATE": "https://selfcare.unifi.com.my/email/autopay-terminate", "OSES_PAYMENT_RECEIPT_TEMPLATE": "https://selfcare.unifi.com.my/email/payment-receipt", "CLOUD_CONNECT_ECOMMERCE_TEMPLATE": "https://selfcare.unifi.com.my/email/ecommerce-hub", "SR_CREATE_REVAMP_TEMPLATE": "https://selfcare.unifi.com.my/email/service-terminate", "OSES_PORTAL_BILL_MERCHANT_ID": "DICEPORTALBIL", "OSES_CMC_SMS_MERCHANT_ID": "MYUNIFICMC", "OSES_SMS_MERCHANT_ID": "MYUNIFISMS", "OSES_APP_MERCHANT_ID": "MYUNIFIBILL", "OSES_RETURN_URL": "https://myunifi-dev.myu.unifi.com.my/ue/api/v1/payment/callback/oses/response", "OSES_URL": "https://oses.tm.com.my/oses/ReqPaymentMode.jsp", "OSES_REVENUE_CODE_ICP": "014", "OSES_REVENUE_CODE_NOVA": "751", "CLOUD_CONNECT_CLIENT_LOGIN": "https://apis.cloudstorage.unifi.com.my/api/Auth/ClientCredential", "CLOUD_CONNECT_CLIENT_ID": "css.apps", "CLOUD_CONNECT_TENANT_REGISTER": "https://apis.cloudstorage.unifi.com.my/api/Tenant/Register", "ECOMMERCE_LOGIN": "https://sso-api.ecommercehub.unifi.com.my/api/v1/login", "ECOMMERCE_USERNAME": "<EMAIL>", "ECOMMERCE_REGISTER": "https://sso-api.ecommercehub.unifi.com.my/api/v1/tm/users", "PRIVATE_PDF_DOMAIN": "https://archival.tm.com.my", "PUBLIC_PDF_DOMAIN": "https://unifi.com.my", "SENDGRID_SEND_EMAIL": "https://api.sendgrid.com/v3/mail/send", "WERAS_BASE_URL": "https://rewards.unifi.com.my", "WERAS_CLIENT_ID": "13", "WERAS_USERNAME": "<EMAIL>", "WERAS_TOKEN": "https://rewards.unifi.com.my/oauth/token", "WERAS_GET_ITEMS": "https://rewards.unifi.com.my/api/items", "WERAS_GET_REDEEM_ITEMS": "https://rewards.unifi.com.my/api/redeem-item", "WERAS_GET_MEMBERSHIP": "https://rewards.unifi.com.my/api/weras/get-membership-ua", "WERAS_GET_CUSTOMER_BILLS": "https://rewards.unifi.com.my/api/weras/customer-bills", "WERAS_GET_PROMOTION_LIST": "https://rewards.unifi.com.my/api/get-promotion-lists", "WERAS_GET_MY_REWARDS": "https://rewards.unifi.com.my/api/rewards/get-my-rewards", "WERAS_GET_TRANSACTION": "https://rewards.unifi.com.my/api/get-transaction", "WERAS_GET_ONLINE_CATALOGUE": "https://rewards.unifi.com.my/api/online-items-category", "WERAS_UPDATE_REWARDS_FLAG": "https://rewards.unifi.com.my/api/rewards/update-rewards-flag", "WERAS_PERSONALISED_REPORTING": "https://rewards.unifi.com.my/api/weras/get-personalized-details", "OTT_VAR_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-var", "OTT_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-ultimate", "OTT_SWAPPING_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-swapping", "OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-swapping-fail", "OTT_ALACARTE_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-alacarte", "OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-ultimate-plus", "OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/tvapp-email-ultimate-max", "TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/user-task", "TEMPORAL_TRIGGER_WORKFLOW_URL": "https://myunifi-dev.myu.unifi.com.my/temporal-client/api/start/main", "DISNEY_CHANGE_MOBILE_EMAIL_URL": "https://selfcare.unifi.com.my/email/disney-change-mobile", "CX_EMAIL_ADDRESS": "<EMAIL>", "CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/meshwifi-email", "CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/cloudgaming-email", "CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/varnam-email", "CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/aneka-email", "CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/ruby-email", "CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/ultimate-email", "CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/upb-email", "CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL": "https://selfcare.unifi.com.my/email/sme-addon-smart-device-email", "TAAS_APIM_HOURLY_TOKEN": "https://gateway.apigate.tm.com.my/token", "TAAS_SERVICE_DETAILS_URL": "https://gateway.apigate.tm.com.my/t/tm.com.my/prod/service-details-retrieve/1.0.0/v3/sba/sync/siebel/nova/ServiceDetailsRetrieve", "MMAG_TRACK_ORDERS_URL": "", "NETCORE_SEND_EMAIL_URL": "https://apim-dev/emailapi.netcorecloud.net/v6/mail/send", "NETCORE_ACTIVITY_URL": "https://apim-dev/api2.netcoresmartech.com/v1/activity/upload"}}