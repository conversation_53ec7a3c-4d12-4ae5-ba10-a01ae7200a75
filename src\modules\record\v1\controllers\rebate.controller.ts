import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import { submitRebateReqSchema } from '../schemas/api/rebate.schema';
import Rebate from '../services/rebate.service';

const rebateV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			Rebate: new Rebate(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/rebate',
		async (ctx): Promise<BaseResponse> => {
			const res = await ctx.Rebate.submitRebate(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Submit a rebate request to WSO2. <br><br> <b>Backend:</b> Unknown <br> <b>Table: </b> rebate_redeem_eligibility',
				tags: ['Record']
			},
			body: submitRebateReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default rebateV1Routes;
