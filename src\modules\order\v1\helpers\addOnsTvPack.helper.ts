import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { TvPackPlanTypeEnum } from '../../../../enum/addOns.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	OrderLineItemsSchema,
	OrderSchema
} from '../../../../integration/wso2/order/schemas/api/wso2CreateAddOnsOrder';
import type {
	ReservationResult,
	Wso2MvReserveIptvRes,
	Wso2MwReserveIptvReq
} from '../../../../integration/wso2/order/schemas/api/wso2ReserveIPTV';
import type {
	Wso2ServiceAccountOli,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { SelectTvPackCatalogue } from '../../../catalogue/v1/schemas/db/tvPackCatalogue.schema';
import { tvPackCatalogueTableSchema } from '../../../catalogue/v1/schemas/db/tvPackCatalogue.schema';
import type {
	AddOnsOrderReq,
	ProductList
} from '../schemas/api/addOnsOrder.schema';
import type { SelectSiebelProductMap } from '../schemas/db/siebelProductMap.schema';

class AddOnsTvPackHelper {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private UNIFI_TV_RESIDENTIAL = 'unifi TV Residential';
	private TV_PACK_VARNAM = 'Varnam Plus Pack';
	private TV_PACK_ANEKA = 'Aneka Plus Pack';
	private TV_PACK_RUBY = 'Ruby Plus Pack';

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.db = getDbInstance();
	}

	addExistingServiceLineItem(
		orderId: string,
		req: AddOnsOrderReq,
		serviceAccounts: Wso2ServiceAccountRes,
		tvPack: SelectTvPackCatalogue
	): OrderSchema {
		const serviceAccount = serviceAccounts?.Response?.ServiceAccount?.[0];
		if (
			!serviceAccount?.BillingAccountId ||
			!serviceAccount?.PrimaryBillingProfileId ||
			!serviceAccount?.ServicePointId ||
			!serviceAccount?.['TmCutAssetMgmt-ServiceMeterIntegration']
				?.TMExchangeName
		) {
			throw new UE_ERROR('SA Record not found', StatusCodeEnum.CONFLICT, {
				integrationId: this.integrationId,
				response: 'ADDON-0003'
			});
		}

		const order: OrderSchema = {
			AccountId: req.CustomerInfo.AccountNo,
			BillingAccountId: serviceAccount.BillingAccountId,
			BillingProfileId: serviceAccount.PrimaryBillingProfileId,
			ServicePointId: serviceAccount.ServicePointId,
			ContactId: req.CustomerInfo.ContactId,
			OrderType: 'Modify',
			PriceList: 'TM Consumer Price List',
			TMOrderSource: 'Customer Portal',
			TMPromotionName: '',
			TMDiCEOrderNumber: orderId,
			OrderNumber: orderId,
			TMServingExchange:
				serviceAccount?.['TmCutAssetMgmt-ServiceMeterIntegration']
					?.TMExchangeName || '',
			Description: tvPack.Summary ?? 'No description available',
			DeliveryAddressId:
				serviceAccount?.['TmCutAssetMgmt-ServiceMeterIntegration']
					?.TmCutAddressIntegration?.Id || '',
			RecipientName: req.CustomerInfo.FullName,
			RecipientContactNum: req.CustomerInfo.BillingContactNo,
			ListOfOrderLineItems: { OrderLineItems: [] }
		};

		for (const serviceAccountMoli of serviceAccount?.ServiceAccountMoli || []) {
			const orderLineItems1Tier: OrderLineItemsSchema = {
				ActionCode:
					serviceAccountMoli.ProductName === this.UNIFI_TV_RESIDENTIAL
						? 'Update'
						: '-',
				AssetIntegrationId: serviceAccountMoli.IntegrationId,
				BillingAccountId: serviceAccountMoli.BillingAccountId ?? '',
				PartNumber: serviceAccountMoli.ProductPartNumber,
				ProdPromInstanceId: serviceAccountMoli.ProdPromInstanceId,
				ProdPromName: serviceAccountMoli.ProductPartNumber,
				Product: serviceAccountMoli.ProductName ?? '',
				ProductId: serviceAccountMoli.ProductId,
				ProductType: serviceAccountMoli.ProductType,
				ServiceAccountId: req.CustomerInfo.AccountNo,
				ServiceId: serviceAccountMoli.SerialNumber,
				ServicePointId: serviceAccountMoli.ServicePointId,
				ReservationStatus: 'Reserve Success',
				Qty: serviceAccountMoli.Quantity,
				CfgStateCode: 'New Item',
				BillingProfileId: serviceAccount?.PrimaryBillingProfileId,
				TMServiceTaxFlag: serviceAccountMoli.TMServiceTaxFlag ?? 'N',

				OrderLineItems: []
			};

			const lineItemsList2Tier: OrderLineItemsSchema[] = [];

			for (const serviceAccountOli of serviceAccountMoli?.ServiceAccountOli ??
				[]) {
				const actionCode =
					serviceAccountOli.Type === 'HyppTV Package' ? 'Delete' : '-';
				const cfgStateCode =
					serviceAccountOli.Type === 'HyppTV Package'
						? 'User Requested Item'
						: 'New Item';

				if (serviceAccountMoli.ProductName !== this.UNIFI_TV_RESIDENTIAL) {
					const orderLineItems2Tier: OrderLineItemsSchema = {
						ActionCode: actionCode,
						BillingAccountId: serviceAccountOli.BillingAccountId ?? '',
						PartNumber: serviceAccountOli.ProductPartNumber,
						Product: serviceAccountOli.ProductName ?? '',
						ProductType: 'Product',
						ServiceAccountId: req.CustomerInfo.AccountNo,
						ReservationStatus: 'Reserve Success',
						CfgStateCode: cfgStateCode,
						AssetIntegrationId: serviceAccountOli.IntegrationId,
						BillingProfileId: serviceAccount?.PrimaryBillingProfileId,
						ProdPromInstanceId: serviceAccountOli.ProdPromInstanceId,
						ProdPromName: serviceAccountOli.ProdPromName,
						Qty: serviceAccountOli.Quantity,
						ServiceId: serviceAccountOli.SerialNumber,
						ServicePointId: serviceAccountOli.ServicePointId,
						TMServiceTaxFlag: 'N'
					};

					// to set assets if available
					if (
						serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
							'TmAssetMgmt-AssetXaIntegration'
						]?.some(xaAsset => xaAsset?.Name)
					) {
						orderLineItems2Tier.ListOfOrderItemXa = {
							OrderItemXa:
								serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
									'TmAssetMgmt-AssetXaIntegration'
								]?.map(xaAsset => ({
									ActionCode: actionCode,
									Name: xaAsset.Name,
									TextValue: xaAsset.Value
								})) ?? []
						};
					}

					lineItemsList2Tier.push(orderLineItems2Tier);
				} else {
					// to set second tier
					let orderLineItems2Tier: OrderLineItemsSchema =
						{} as OrderLineItemsSchema;
					const secondTierOlis = serviceAccountMoli?.ServiceAccountOli?.find(
						oli => oli.Id === serviceAccountOli.ParentAssetId
					);
					if (!secondTierOlis) {
						orderLineItems2Tier = {
							ActionCode: actionCode,
							BillingAccountId: serviceAccountOli.BillingAccountId ?? '',
							PartNumber: serviceAccountOli.ProductPartNumber,
							Product: serviceAccountOli.ProductName ?? '',
							ProductType: 'Product',
							ServiceAccountId: req.CustomerInfo.AccountNo,
							ReservationStatus: 'Reserve Success',
							CfgStateCode: cfgStateCode,
							AssetIntegrationId: serviceAccountOli.IntegrationId,
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId,
							ProdPromInstanceId: serviceAccountOli.ProdPromInstanceId,
							ProdPromName: serviceAccountOli.ProdPromName,
							Qty: serviceAccountOli.Quantity,
							ServiceId: serviceAccountOli.SerialNumber,
							ServicePointId: serviceAccountOli.ServicePointId,
							TMServiceTaxFlag: 'N'
						};

						// to set assets if available
						if (
							serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
								'TmAssetMgmt-AssetXaIntegration'
							]?.some(xaAsset => xaAsset?.Name)
						) {
							orderLineItems2Tier.ListOfOrderItemXa = {
								OrderItemXa:
									serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
										'TmAssetMgmt-AssetXaIntegration'
									]?.map(xaAsset => ({
										ActionCode: actionCode,
										Name: xaAsset.Name,
										TextValue: xaAsset.Value
									})) ?? []
							};
						}
					}

					// to set third tier
					const thirdTierOlis = serviceAccountMoli?.ServiceAccountOli?.filter(
						oli => oli.ParentAssetId === serviceAccountOli.Id
					);
					const orderLineItems3Tier: OrderLineItemsSchema[] = [];
					for (const thirdTierOli of thirdTierOlis ?? []) {
						const orderLineItem3Tier: OrderLineItemsSchema = {
							ActionCode: actionCode,
							BillingAccountId: thirdTierOli.BillingAccountId ?? '',
							PartNumber: thirdTierOli.ProductPartNumber,
							Product: thirdTierOli.ProductName ?? '',
							ProductType: 'Product',
							ServiceAccountId: req.CustomerInfo.AccountNo,
							ReservationStatus: 'Reserve Success',
							CfgStateCode: cfgStateCode,
							AssetIntegrationId: thirdTierOli.IntegrationId,
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId,
							ProdPromInstanceId: thirdTierOli.ProdPromInstanceId,
							ProdPromName: thirdTierOli.ProdPromName,
							Qty: thirdTierOli.Quantity,
							ServiceId: thirdTierOli.SerialNumber,
							ServicePointId: thirdTierOli.ServicePointId,
							TMServiceTaxFlag: 'N'
						};

						// to set assets if available
						if (
							thirdTierOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
								'TmAssetMgmt-AssetXaIntegration'
							]?.some(xaAsset => xaAsset?.Name)
						) {
							orderLineItem3Tier.ListOfOrderItemXa = {
								OrderItemXa:
									thirdTierOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
										'TmAssetMgmt-AssetXaIntegration'
									]?.map(xaAsset => ({
										ActionCode: actionCode,
										Name: xaAsset.Name,
										TextValue: xaAsset.Value
									})) ?? []
							};
						}
						orderLineItems3Tier.push(orderLineItem3Tier);
					}

					// Ensure no empty objects are added
					if (Object.keys(orderLineItems2Tier).length > 0) {
						if (orderLineItems3Tier.length > 0) {
							orderLineItems2Tier.OrderLineItems =
								orderLineItems2Tier.OrderLineItems || [];
							orderLineItems2Tier.OrderLineItems?.push(...orderLineItems3Tier);
						}
						lineItemsList2Tier.push(orderLineItems2Tier);
					}
				}
			}
			orderLineItems1Tier.OrderLineItems?.push(...lineItemsList2Tier);
			order.ListOfOrderLineItems.OrderLineItems.push(orderLineItems1Tier);
		}

		return order;
	}

	addNewServiceLineItem(
		hasTvPack: boolean,
		existTvPackPartNum: string,
		product: ProductList,
		order: OrderSchema,
		tvPack: SelectTvPackCatalogue,
		siebelProductMap: SelectSiebelProductMap[],
		commitmentOli: Wso2ServiceAccountOli
	): void {
		if (
			!tvPack.PartNumber ||
			!tvPack.ProductId ||
			!tvPack.CommitmentName ||
			!tvPack.CommitmentPartNumber ||
			!tvPack.CommitmentProductId
		) {
			throw new UE_ERROR(
				'TV Pack mapping not found - ADDON-0010',
				StatusCodeEnum.CONFLICT,
				{
					integrationId: this.integrationId,
					response: tvPack
				}
			);
		}

		// Add TV Pack OLI
		for (const orderLineItem of order.ListOfOrderLineItems.OrderLineItems) {
			if (
				orderLineItem?.Product?.toLowerCase() ===
					this.UNIFI_TV_RESIDENTIAL.toLowerCase() ||
				orderLineItem?.Product?.toLowerCase() === 'hypptv residential' ||
				orderLineItem?.Product?.toLowerCase() === 'hypptv everywhere lite'
			) {
				let orderLineItem3Tier: OrderLineItemsSchema;
				// Add TV pack OLI if user is not subscribed to any TV pack
				if (!hasTvPack) {
					orderLineItem3Tier = {
						ActionCode: 'Add',
						AssetIntegrationId: '',
						BillingAccountId: order.BillingAccountId,
						PartNumber: tvPack.PartNumber,
						ProdPromInstanceId: '',
						Product: tvPack.SiebelTvPackName,
						ProductId: tvPack.ProductId,
						ProdPromName: '',
						ProductType: 'Product',
						ServiceAccountId: order.AccountId,
						ServicePointId: orderLineItem.ServicePointId,
						ReservationStatus: 'Reserve Success',
						Qty: product.TotalQuantity.toString(),
						TMServiceTaxFlag: 'N',
						CfgStateCode: 'User Requested Item',
						IntegrationId: '',
						BillingProfileId: order.BillingProfileId,
						OrderLineItems: [
							{
								ActionCode: 'Add',
								ProductType: 'Product',
								PartNumber: tvPack.CommitmentPartNumber,
								Product: tvPack.CommitmentName,
								ReservationStatus: 'Reserve Success',
								CfgStateCode: 'Engine Picked Item',
								BillingAccountId: order.BillingAccountId,
								ServiceAccountId: order.AccountId,
								IntegrationId: '',
								AssetIntegrationId: '',
								BillingProfileId: order.BillingProfileId,
								ProdPromInstanceId: '',
								ProdPromName: '',
								Qty: product.TotalQuantity.toString(),
								ServicePointId: '',
								ProductId: tvPack.CommitmentProductId,
								TMServiceTaxFlag: 'N',
								ListOfOrderItemXa: {
									OrderItemXa: [
										{
											ActionCode: 'Add',
											Name: 'Number of years',
											TextValue: `${tvPack.ContractTerm / 12}`
										}
									]
								}
							}
						]
					};

					// Pack Content Discount
					const deviceMapPackContents = siebelProductMap.filter(
						obj =>
							obj.ProductName ===
							`${product.ProductName} - Pack Content Discount`
					);
					if (deviceMapPackContents.length > 0) {
						for (const deviceMapPackContent of deviceMapPackContents) {
							const orderLineItemsWithContentDiscount: OrderLineItemsSchema = {
								ActionCode: 'Add',
								ProductType: 'Product',
								PartNumber: deviceMapPackContent.PartNumber,
								Product: deviceMapPackContent.ProductName.split(' - ')[1],
								ReservationStatus: 'Reserve Success',
								CfgStateCode: 'Engine Picked Item',
								BillingAccountId: order.BillingAccountId,
								ServiceAccountId: order.AccountId,
								IntegrationId: '',
								AssetIntegrationId: '',
								BillingProfileId: order.BillingProfileId,
								ProdPromInstanceId: '',
								ProdPromName: '',
								Qty: product.TotalQuantity.toString(),
								ServicePointId: '',
								ProductId: deviceMapPackContent.ProductId,
								TMServiceTaxFlag: 'N'
							};

							orderLineItem3Tier.OrderLineItems = [
								...(orderLineItem3Tier.OrderLineItems || []),
								orderLineItemsWithContentDiscount
							];
						}
					}

					// Waiver - Skinny Pack 2
					const waiverSkinnyPack2 = siebelProductMap.filter(
						obj => obj.ProductName === `${product.ProductName} - Waiver`
					);
					if (waiverSkinnyPack2.length > 0) {
						for (const waiver of waiverSkinnyPack2) {
							const orderLineItemsWithWaiver: OrderLineItemsSchema = {
								ActionCode: 'Add',
								ProductType: 'Product',
								PartNumber: waiver.PartNumber,
								Product: waiver.ProductName.split(' - ')[1],
								ReservationStatus: 'Reserve Success',
								CfgStateCode: 'Engine Picked Item',
								BillingAccountId: order.BillingAccountId,
								ServiceAccountId: order.AccountId,
								IntegrationId: '',
								AssetIntegrationId: '',
								BillingProfileId: order.BillingProfileId,
								ProdPromInstanceId: '',
								ProdPromName: '',
								Qty: product.TotalQuantity.toString(),
								ServicePointId: '',
								ProductId: waiver.ProductId,
								TMServiceTaxFlag: 'N'
							};

							orderLineItem3Tier.OrderLineItems =
								orderLineItem3Tier.OrderLineItems || [];
							orderLineItem3Tier?.OrderLineItems?.push(
								orderLineItemsWithWaiver
							);
						}
					}

					/**
					 * VAR+ are not eligible for unifi TV Free
					 */
					if (
						![
							this.TV_PACK_VARNAM,
							this.TV_PACK_ANEKA,
							this.TV_PACK_RUBY
						].includes(product.ProductName)
					) {
						const orderLineItemsUnifiTvFree: OrderLineItemsSchema = {
							ActionCode: 'Add',
							ProductType: 'Product',
							PartNumber: 'PR000221',
							Product: 'unifi TV Free',
							ReservationStatus: 'Reserve Success',
							CfgStateCode: 'Engine Picked Item',
							BillingAccountId: order.BillingAccountId,
							ServiceAccountId: order.AccountId,
							IntegrationId: '',
							AssetIntegrationId: '',
							BillingProfileId: order.BillingProfileId,
							ProdPromInstanceId: '',
							ProdPromName: '',
							Qty: product.TotalQuantity.toString(),
							ServiceId: '',
							SecondaryServiceId: '',
							ServicePointId: '',
							TMServiceTaxFlag: 'N',
							ListOfOrderItemXa: {
								OrderItemXa: [
									{
										ActionCode: 'Add',
										Name: 'IPTV Type',
										TextValue: 'Basic'
									}
								]
							}
						};
						orderLineItem.OrderLineItems = orderLineItem.OrderLineItems || [];
						orderLineItem?.OrderLineItems?.push(orderLineItemsUnifiTvFree);
					}
				} else {
					// User is swapping or upgrading the TV Pack
					// User with VAR pack and swapping to another VAR pack requires commitment period from the old pack
					// User with tv pack and upgrade to another pack requires commitment period from the new pack
					const isSwapping = [
						this.TV_PACK_VARNAM,
						this.TV_PACK_ANEKA,
						this.TV_PACK_RUBY
					].includes(tvPack.SiebelTvPackName);

					const siebelCommitment = isSwapping
						? {
								PartNumber: commitmentOli.ProductPartNumber,
								ProductName: commitmentOli.ProductName || '',
								ProductId: commitmentOli.ProductId
							}
						: {
								PartNumber: tvPack.CommitmentPartNumber,
								ProductName: tvPack.CommitmentName,
								ProductId: tvPack.CommitmentProductId
							};

					const year = isSwapping
						? commitmentOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
								'TmAssetMgmt-AssetXaIntegration'
							]?.find(xa => xa.Name === 'Number of years')?.Value || '2'
						: `${tvPack.ContractTerm / 12}`;

					// Upgrade TV Pack
					orderLineItem3Tier = {
						ActionCode: 'Add',
						BillingAccountId: order.BillingAccountId,
						PartNumber: tvPack.PartNumber,
						Product: tvPack.SiebelTvPackName,
						ProductType: 'Product',
						ServiceAccountId: order.AccountId,
						ReservationStatus: 'Reserve Success',
						CfgStateCode: 'User Requested Item',
						IntegrationId: '',
						AssetIntegrationId: '',
						BillingProfileId: order.BillingProfileId,
						ProdPromInstanceId: '',
						ProdPromName: '',
						Qty: product.TotalQuantity.toString(),
						ServicePointId: orderLineItem.ServicePointId,
						ProductId: tvPack.ProductId,
						TMServiceTaxFlag: 'N',
						OrderLineItems: [
							{
								ActionCode: 'Add',
								ProductType: 'Product',
								PartNumber: siebelCommitment.PartNumber,
								Product: siebelCommitment.ProductName,
								ReservationStatus: 'Reserve Success',
								CfgStateCode: 'Engine Picked Item',
								BillingAccountId: order.BillingAccountId,
								ServiceAccountId: order.AccountId,
								IntegrationId: '',
								AssetIntegrationId: '',
								BillingProfileId: order.BillingProfileId,
								ProdPromInstanceId: '',
								ProdPromName: '',
								Qty: product.TotalQuantity.toString(),
								ServicePointId: '',
								ProductId: siebelCommitment.ProductId,
								TMServiceTaxFlag: 'N',
								ListOfOrderItemXa: {
									OrderItemXa: [
										{
											ActionCode: 'Add',
											Name: 'Number of years',
											TextValue: year
										}
									]
								}
							}
						]
					};
				}

				// Waiver
				if (`${process.env.TV_PACK_WAIVER}`.toUpperCase() === 'YES') {
					if (
						product.ProductName.toLowerCase().includes('ultimate plus') ||
						product.ProductName.toLowerCase().includes('ultimate max')
					) {
						const deviceMapWaiver = siebelProductMap.find(
							obj =>
								obj.ProductName ===
								`${product.ProductName} ${process.env.TV_PACK_MONTH_WAIVER} Month Waiver`
						);

						if (!deviceMapWaiver) {
							throw new UE_ERROR(
								'No waiver mapping found',
								StatusCodeEnum.CONFLICT,
								{
									integrationId: this.integrationId,
									response: 'ADDON-0014'
								}
							);
						}

						const orderLineItemsWithWaiver: OrderLineItemsSchema = {
							ActionCode: 'Add',
							ProductType: 'Product',
							PartNumber: deviceMapWaiver.PartNumber,
							Product: deviceMapWaiver.ProductName,
							ReservationStatus: 'Reserve Success',
							CfgStateCode: 'Engine Picked Item',
							BillingAccountId: order.BillingAccountId,
							ServiceAccountId: order.AccountId,
							IntegrationId: '',
							AssetIntegrationId: '',
							BillingProfileId: order.BillingProfileId,
							ProdPromInstanceId: '',
							ProdPromName: '',
							Qty: product.TotalQuantity.toString(),
							ServicePointId: '',
							ProductId: deviceMapWaiver.ProductId,
							TMServiceTaxFlag: 'N'
						};

						orderLineItem3Tier.OrderLineItems =
							orderLineItem3Tier.OrderLineItems || [];
						orderLineItem3Tier.OrderLineItems?.push(orderLineItemsWithWaiver);
					}
				}

				// Add unifi playTv / HyppTV Everywhere
				if (
					!order.ListOfOrderLineItems.OrderLineItems.find(oli =>
						oli.OrderLineItems?.some(
							item =>
								item.Product?.toLowerCase() === 'hypptv everywhere' ||
								item.Product?.toLowerCase() === 'unifi playtv'
						)
					)
				) {
					const orderLineItemUnifiPlayTv: OrderLineItemsSchema = {
						ActionCode: 'Add',
						BillingAccountId: order.BillingAccountId,
						PartNumber: 'PR006555',
						Product: 'unifi playTV',
						ProductType: 'Product',
						ServiceAccountId: order.AccountId,
						ReservationStatus: 'Reserve Success',
						CfgStateCode: 'User Requested Item',
						IntegrationId: '',
						AssetIntegrationId: '',
						BillingProfileId: order.BillingProfileId,
						ProdPromInstanceId: '',
						ProdPromName: '',
						Qty: product.TotalQuantity.toString(),
						ServiceId: orderLineItem.ServiceId,
						SecondaryServiceId: '',
						ServicePointId: '',
						TMServiceTaxFlag: 'N',
						OrderLineItems: [
							{
								ActionCode: 'Add',
								ProductType: 'Product',
								PartNumber: 'PR006558',
								Product: 'unifi playTV Plan A (Promo)',
								ReservationStatus: 'Reserve Success',
								CfgStateCode: 'User Requested Item',
								BillingAccountId: order.BillingAccountId,
								ServiceAccountId: order.AccountId,
								IntegrationId: '',
								AssetIntegrationId: '',
								BillingProfileId: order.BillingProfileId,
								ProdPromInstanceId: '',
								ProdPromName: '',
								Qty: product.TotalQuantity.toString(),
								ServicePointId: '',
								TMServiceTaxFlag: 'N',
								ListOfOrderItemXa: {
									OrderItemXa: [
										{
											ActionCode: 'Add',
											Name: 'Number of devices',
											TextValue: '2'
										}
									]
								}
							}
						]
					};

					orderLineItem.OrderLineItems = orderLineItem.OrderLineItems || [];
					orderLineItem.OrderLineItems?.push(orderLineItemUnifiPlayTv);
				}

				// VAR+ are not eligible for unifi Plus Box (ATV) Device Charge
				if (
					![
						this.TV_PACK_VARNAM,
						this.TV_PACK_ANEKA,
						this.TV_PACK_RUBY
					].includes(product.ProductName)
				) {
					const upbAtvs = siebelProductMap.find(
						obj => obj.ProductName === 'unifi Plus Box (ATV) Device Charge'
					);

					if (!upbAtvs) {
						throw new UE_ERROR(
							'No unifi Plus Box (ATV) Device Charge mapping found',
							StatusCodeEnum.CONFLICT,
							{
								integrationId: this.integrationId,
								response: 'ADDON-0015'
							}
						);
					}

					const orderLineItemsUnifiTvDeviceCharge: OrderLineItemsSchema = {
						ActionCode: 'Add',
						BillingAccountId: order.BillingAccountId,
						PartNumber: upbAtvs.PartNumber,
						Product: upbAtvs.ProductName,
						ProductType: 'Product',
						ServiceAccountId: order.AccountId,
						ReservationStatus: 'Reserve Success',
						CfgStateCode: 'New Item',
						IntegrationId: '',
						AssetIntegrationId: '',
						BillingProfileId: order.BillingProfileId,
						ProdPromInstanceId: '',
						ProdPromName: '',
						Qty: '1',
						ServicePointId: orderLineItem.ServicePointId,
						ProductId: upbAtvs.ProductId,
						TMServiceTaxFlag: 'N'
					};

					orderLineItem.OrderLineItems = orderLineItem.OrderLineItems || [];
					orderLineItem.OrderLineItems?.push(orderLineItemsUnifiTvDeviceCharge);
				}

				// HyppTV Free Lite for VAR+
				if (
					[this.TV_PACK_VARNAM, this.TV_PACK_ANEKA, this.TV_PACK_RUBY].includes(
						product.ProductName
					)
				) {
					const orderLineHyppTVFreeLite: OrderLineItemsSchema = {
						ActionCode: 'Add',
						ProductType: 'Product',
						PartNumber: 'PR000221',
						Product: 'HyppTV Free',
						ReservationStatus: 'Reserve Success',
						CfgStateCode: 'User Requested Item',
						BillingAccountId: order.BillingAccountId,
						ServiceAccountId: order.AccountId,
						IntegrationId: '',
						AssetIntegrationId: '',
						BillingProfileId: order.BillingProfileId,
						ProdPromInstanceId: '',
						ProdPromName: '',
						Qty: product.TotalQuantity.toString(),
						ServicePointId: '',
						ProductId: '',
						TMServiceTaxFlag: 'N',
						ListOfOrderItemXa: {
							OrderItemXa: [
								{
									ActionCode: 'Add',
									Name: 'IPTV Type',
									TextValue: 'Basic'
								}
							]
						}
					};
					orderLineItem.OrderLineItems = orderLineItem.OrderLineItems || [];
					orderLineItem.OrderLineItems?.push(orderLineHyppTVFreeLite);
				}

				orderLineItem.OrderLineItems = orderLineItem.OrderLineItems || [];
				orderLineItem?.OrderLineItems?.push(orderLineItem3Tier);
			}
		}

		/**
		 * This logic is carried over from legacy XE code.
		 * The condition is now outdated as the New Ultimate Pack is no longer offered.
		 * Safe to remove in the future after verification.
		 * @deprecated
		 */
		if (
			existTvPackPartNum.toLowerCase() === 'pr006970' &&
			product.ProductName.toLowerCase() === 'new ultimate pack'
		) {
			for (let i = 0; i < product.TotalQuantity; i++) {
				const upbMapping = siebelProductMap.find(
					obj => obj.ProductName === 'unifi Plus Box'
				);

				if (!upbMapping) {
					throw new UE_ERROR(
						'No unifi Plus Box mapping found',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0016'
						}
					);
				}

				const orderLineItemsUpb: OrderLineItemsSchema = {
					Product: 'unifi Plus Box',
					ActionCode: 'Add',
					ProductId: upbMapping.ProductId,
					PartNumber: upbMapping.PartNumber,
					Qty: '1',
					ProductType: 'Product',
					ReservationStatus: 'Reserve Success',
					CfgStateCode: 'New Item',
					BillingProfileId: order.BillingProfileId,
					BillingAccountId: order.BillingAccountId,
					ProdPromInstanceId: '',
					ProdPromName: '',
					ServiceId: '',
					ServicePointId: '',
					OrderLineItems: [],
					AssetIntegrationId: '',
					ServiceAccountId: '',
					TMServiceTaxFlag: 'N',
					IntegrationId: '',
					SecondaryServiceId: '',
					ListOfActivity: { Activity: [] },
					ListOfOrderItemXa: { OrderItemXa: [] }
				};

				const courierChargeMapping = siebelProductMap.find(
					obj => obj.ProductName === 'Courier Fee - Free of Charge'
				);

				if (!courierChargeMapping) {
					throw new UE_ERROR(
						'No courier charge mapping found',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0017'
						}
					);
				}

				const orderLineItemsCourierFee: OrderLineItemsSchema = {
					Product: 'Courier Fee - Free of Charge',
					ActionCode: 'Add',
					ProductId: courierChargeMapping.ProductId,
					PartNumber: courierChargeMapping.PartNumber,
					Qty: '1',
					ProductType: 'Product',
					ReservationStatus: 'Reserve Success',
					CfgStateCode: 'New Item',
					BillingAccountId: order.BillingAccountId,
					BillingProfileId: order.BillingProfileId,
					ProdPromInstanceId: '',
					ProdPromName: '',
					ServiceId: '',
					ServicePointId: '',
					OrderLineItems: [],
					AssetIntegrationId: '',
					ServiceAccountId: '',
					TMServiceTaxFlag: 'N',
					ListOfOrderItemXa: { OrderItemXa: [] }
				};

				orderLineItemsUpb.OrderLineItems = [orderLineItemsCourierFee];

				for (const orderLineItem of order.ListOfOrderLineItems
					?.OrderLineItems || []) {
					if (orderLineItem.Product === this.UNIFI_TV_RESIDENTIAL) {
						if (!orderLineItem.OrderLineItems) {
							orderLineItem.OrderLineItems = [];
						}
						orderLineItem.OrderLineItems.push(orderLineItemsUpb);
					}
				}
			}
		}
	}

	addHyppTvMoli(
		reservationResult: ReservationResult,
		order: OrderSchema,
		rhsiMoli: OrderLineItemsSchema,
		serviceAccountId: string,
		productName: string
	): void {
		// VAR+ entitled to HyppTV Residential
		const isVarPlus = [
			this.TV_PACK_VARNAM,
			this.TV_PACK_ANEKA,
			this.TV_PACK_RUBY
		].includes(productName);
		const orderLineItems1Tier: OrderLineItemsSchema = {
			ActionCode: 'Add',
			BillingAccountId: order.BillingAccountId,
			BillingProfileId: order.BillingProfileId,
			ReservationStatus: 'Reserve Success',
			CfgStateCode: 'New Item',
			Qty: '1',
			ProductType: 'Product',
			ServiceId: reservationResult.loginName,
			SecondaryServiceId: reservationResult.password,
			Product: isVarPlus ? 'HyppTV Residential' : this.UNIFI_TV_RESIDENTIAL,
			PartNumber: 'PR000200',
			AssetIntegrationId: '',
			IntegrationId: '',
			ProdPromInstanceId: rhsiMoli.ProdPromInstanceId,
			ProdPromName: rhsiMoli.ProdPromName,
			ProductId: '',
			ServiceAccountId: serviceAccountId,
			ServicePointId: '',
			TMServiceTaxFlag: 'N'
		};

		order.ListOfOrderLineItems.OrderLineItems.push(orderLineItems1Tier);
	}

	hasHyppTvMoli(order: OrderSchema): boolean {
		for (const orderLineItem of order.ListOfOrderLineItems.OrderLineItems) {
			if (orderLineItem.Product === this.UNIFI_TV_RESIDENTIAL) {
				return true;
			}
		}

		return false;
	}

	getServiceId(order: OrderSchema): string {
		if (order.ListOfOrderLineItems?.OrderLineItems) {
			for (const orderLineItem of order.ListOfOrderLineItems.OrderLineItems) {
				if (
					orderLineItem.Product?.toLowerCase() ===
					'residential high speed internet'
				) {
					if (orderLineItem.OrderLineItems) {
						for (const nestedOrderLineItem of orderLineItem.OrderLineItems) {
							if (
								nestedOrderLineItem.Product?.includes(
									'High Speed Internet - Residential'
								) ||
								nestedOrderLineItem.Product?.includes(
									'High Speed Internet - Home'
								)
							) {
								return nestedOrderLineItem.ServiceId || '';
							}
						}
					}
				}
			}
		}
		return '';
	}

	getResidentialHsiMoli(order: OrderSchema): OrderLineItemsSchema | null {
		if (order.ListOfOrderLineItems?.OrderLineItems) {
			for (const orderLineItem of order.ListOfOrderLineItems.OrderLineItems) {
				if (
					orderLineItem.Product?.toLowerCase() ===
					'residential high speed internet'
				) {
					return orderLineItem;
				}
			}
		}
		return null;
	}

	getTvPackCommitmentOli(
		wso2SARes: Wso2ServiceAccountRes
	): Wso2ServiceAccountOli {
		for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
			for (const moli of sa.ServiceAccountMoli ?? []) {
				if (moli.ProductName !== this.UNIFI_TV_RESIDENTIAL) continue;
				for (const oli of moli.ServiceAccountOli ?? []) {
					if (
						oli.ProductName?.includes('Commitment Period') &&
						oli.Type === 'Commitment'
					) {
						return oli;
					}
				}
			}
		}

		return {} as Wso2ServiceAccountOli;
	}

	getCurrentTvPackPartNo(serviceAccounts: Wso2ServiceAccountRes): string {
		const serviceAccount = serviceAccounts?.Response?.ServiceAccount?.[0];

		if (!serviceAccount) return '';

		for (const moli of serviceAccount.ServiceAccountMoli ?? []) {
			if (moli.ProductName !== this.UNIFI_TV_RESIDENTIAL) continue;

			for (const oli of moli.ServiceAccountOli ?? []) {
				if (oli.Type === 'HyppTV Package' && oli.ProductPartNumber) {
					return oli.ProductPartNumber;
				}
			}
		}

		return '';
	}

	async getTvPackByProductName(
		id: number,
		siebelTvPackName: string
	): Promise<SelectTvPackCatalogue> {
		const [tvPack] = await this.db
			.select()
			.from(tvPackCatalogueTableSchema)
			.where(
				and(
					eq(tvPackCatalogueTableSchema.Id, id),
					eq(tvPackCatalogueTableSchema.SiebelTvPackName, siebelTvPackName),
					eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE)
				)
			)
			.execute()
			.catch((err: Error) => {
				throw new UE_ERROR(String(err), StatusCodeEnum.UE_INTERNAL_SERVER, {
					integrationId: this.integrationId,
					response: 'ADDON-0008'
				});
			});

		if (!tvPack) {
			throw new UE_ERROR(
				`TV pack record not found for id: ${id}`,
				StatusCodeEnum.CONFLICT,
				{
					integrationId: this.integrationId,
					response: 'ADDON-0009'
				}
			);
		}
		return tvPack;
	}

	async reserveIptvId(
		iptvId: string,
		accountNo: string
	): Promise<ReservationResult> {
		const mwReserveIptvRequest: Wso2MwReserveIptvReq = {
			PerformLoginRequest: {
				PerformLoginReq: [
					{
						customerId: accountNo,
						LoginDetails: [
							{
								orderLineItemId: 'PR000200-1',
								loginName: iptvId,
								offer: 'PR000200'
							}
						]
					}
				]
			}
		};

		const mwReserveIptvResponse: Wso2MvReserveIptvRes =
			await this.mwIntegration.Wso2OrderIntegration.getReserveIPTV(
				mwReserveIptvRequest
			);

		for (const lr of mwReserveIptvResponse.PerformLoginResponse.LoginResponse) {
			for (const rs of lr.reservationResult) {
				if (rs.statusCode === '1') {
					throw new UE_ERROR(
						'Login provided in input is already in use',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0018'
						}
					);
				}

				return {
					orderLineItemId: rs.orderLineItemId,
					loginName: rs.loginName,
					password: rs.password,
					statusCode: rs.statusCode
				};
			}
		}

		throw new UE_ERROR('Failed to reserve IPTV ID', StatusCodeEnum.CONFLICT, {
			integrationId: this.integrationId,
			response: 'ADDON-0019'
		});
	}
}

export default AddOnsTvPackHelper;
