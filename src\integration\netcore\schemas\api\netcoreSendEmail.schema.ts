import { type Static, t } from 'elysia';

/** REQUEST */
export const netcoreSendEmailReqSchema = t.Object({
	from: t.Object({
		email: t.String(),
		name: t.Optional(t.String())
	}),
	subject: t.String(),
	content: t.Array(
		t.Object({
			type: t.String(),
			value: t.String()
		})
	),
	personalizations: t.Array(
		t.Object({
			to: t.Array(
				t.Object({
					email: t.String(),
					name: t.Optional(t.String())
				})
			)
		})
	)
});
export type NetcoreSendEmailReq = Static<typeof netcoreSendEmailReqSchema>;
