import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type ValidateOrderIdRes,
	validateOrderIdReqSchema,
	validateOrderIdResSchema
} from '../schemas/api/miniOrderTracker.schema';
import { default as MiniOrderTracker } from '../services/miniOrderTracker.service';

const miniOrderTrackerV1Routes = new Elysia({ prefix: '/mini-order-tracker' })
	.resolve(() => {
		return {
			MiniOrderTracker: new MiniOrderTracker(randomUUID())
		};
	})
	.post(
		'/orderable/validate',
		async (ctx): Promise<ValidateOrderIdRes> => {
			return await ctx.MiniOrderTracker.validateOrderId(
				ctx.body.OrderId,
				ctx.body.IdType,
				ctx.body.CustomerID
			);
		},
		{
			detail: {
				description:
					'Validate order id to check if it is existing. This API is for mini order tracker. <br><br> <b>Backend System: </b> NOVA SIEBEL',
				tags: ['Record']
			},
			body: validateOrderIdReqSchema,
			response: {
				200: validateOrderIdResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default miniOrderTrackerV1Routes;
