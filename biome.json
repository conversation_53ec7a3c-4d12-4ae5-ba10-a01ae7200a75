{"vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "files": {"ignore": ["node_modules", "package-lock.json", "yarn.lock", "drizzle", "migrations", "tsconfig.json"]}, "linter": {"rules": {"suspicious": {"noExplicitAny": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "none", "semicolons": "always", "lineWidth": 80, "arrowParentheses": "asNeeded"}}, "json": {"parser": {"allowComments": true}}}