import BizUserIntegration from './biz/bizUser.integration';
import EmailIntegration from './emailTemplate/emailTemplate.integration';
import MmagIntegration from './mmag/mmag.integration';
import NetcoreIntegration from './netcore/netcore.integration';
import OmgIntegration from './omg/omg.integration';
import TaasUserIntegration from './taas/user/taasUser.integration';
import TemporalIntegration from './temporal/temporal.integration';
import WerasIntegration from './weras/weras.integration';
import Wso2AddressIntegration from './wso2/address/wso2Address.integration';
import Wso2EligibilityIntegration from './wso2/eligibility/wso2Eligibility.integration';
import Wso2NotificationIntegration from './wso2/notification/wso2Notification.integration';
import Wso2OrderIntegration from './wso2/order/wso2Order.integration';
import Wso2PaymentIntegration from './wso2/payment/wso2Payment.integration';
import Wso2RecordIntegration from './wso2/record/wso2Record.integration';
import Wso2TroikaIntegration from './wso2/troika/Wso2TroikaIntergration';
import Wso2UserIntegration from './wso2/user/wso2User.integration';

export class MwIntegration {
	private integrationId: string;
	private wso2UserIntegration: Wso2UserIntegration | null = null;
	private wso2RecordIntegration: Wso2RecordIntegration | null = null;
	private wso2OrderIntegration: Wso2OrderIntegration | null = null;
	private wso2NotificationIntegration: Wso2NotificationIntegration | null =
		null;
	private wso2EligibilityIntegration: Wso2EligibilityIntegration | null = null;
	private wso2PaymentIntegration: Wso2PaymentIntegration | null = null;
	private wso2AddressIntegration: Wso2AddressIntegration | null = null;
	private wso2TroikaIntegration: Wso2TroikaIntegration | null = null;
	private omgIntegration: OmgIntegration | null = null;
	private bizUserIntegration: BizUserIntegration | null = null;
	private emailIntegration: EmailIntegration | null = null;
	private werasIntegration: WerasIntegration | null = null;
	private temporalIntegration: TemporalIntegration | null = null;
	private netcoreIntegration: NetcoreIntegration | null = null;
	private taasUserIntegration: TaasUserIntegration | null = null;
	private mmagIntegration: MmagIntegration | null = null;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	get Wso2UserIntegration() {
		if (!this.wso2UserIntegration) {
			this.wso2UserIntegration = new Wso2UserIntegration(this.integrationId);
		}
		return this.wso2UserIntegration;
	}

	get Wso2RecordIntegration() {
		if (!this.wso2RecordIntegration) {
			this.wso2RecordIntegration = new Wso2RecordIntegration(
				this.integrationId
			);
		}
		return this.wso2RecordIntegration;
	}

	get Wso2OrderIntegration() {
		if (!this.wso2OrderIntegration) {
			this.wso2OrderIntegration = new Wso2OrderIntegration(this.integrationId);
		}
		return this.wso2OrderIntegration;
	}

	getWso2NotificationIntegration() {
		if (!this.wso2NotificationIntegration) {
			this.wso2NotificationIntegration = new Wso2NotificationIntegration(
				this.integrationId
			);
		}
		return this.wso2NotificationIntegration;
	}

	get Wso2EligibilityIntegration() {
		if (!this.wso2EligibilityIntegration) {
			this.wso2EligibilityIntegration = new Wso2EligibilityIntegration(
				this.integrationId
			);
		}
		return this.wso2EligibilityIntegration;
	}

	get Wso2PaymentIntegration() {
		if (!this.wso2PaymentIntegration) {
			this.wso2PaymentIntegration = new Wso2PaymentIntegration(
				this.integrationId
			);
		}
		return this.wso2PaymentIntegration;
	}

	get Wso2AddressIntegration() {
		if (!this.wso2AddressIntegration) {
			this.wso2AddressIntegration = new Wso2AddressIntegration(
				this.integrationId
			);
		}
		return this.wso2AddressIntegration;
	}

	get Wso2TroikaIntegration() {
		if (!this.wso2TroikaIntegration) {
			this.wso2TroikaIntegration = new Wso2TroikaIntegration(
				this.integrationId
			);
		}
		return this.wso2TroikaIntegration;
	}

	get OmgIntegration() {
		if (!this.omgIntegration) {
			this.omgIntegration = new OmgIntegration(this.integrationId);
		}
		return this.omgIntegration;
	}

	get TemporalIntegration() {
		if (!this.temporalIntegration) {
			this.temporalIntegration = new TemporalIntegration(this.integrationId);
		}
		return this.temporalIntegration;
	}

	get BizUserIntegration() {
		if (!this.bizUserIntegration) {
			this.bizUserIntegration = new BizUserIntegration(this.integrationId);
		}
		return this.bizUserIntegration;
	}

	get EmailTemplateIntegration() {
		if (!this.emailIntegration) {
			this.emailIntegration = new EmailIntegration(this.integrationId);
		}
		return this.emailIntegration;
	}

	get WerasIntegration() {
		if (!this.werasIntegration) {
			this.werasIntegration = new WerasIntegration(this.integrationId);
		}
		return this.werasIntegration;
	}

	get TaasUserIntegration() {
		if (!this.taasUserIntegration) {
			this.taasUserIntegration = new TaasUserIntegration(this.integrationId);
		}
		return this.taasUserIntegration;
	}

	get MmagIntegration() {
		if (!this.mmagIntegration) {
			this.mmagIntegration = new MmagIntegration(this.integrationId);
		}
		return this.mmagIntegration;
	}

	get NetcoreIntegration() {
		if (!this.netcoreIntegration) {
			this.netcoreIntegration = new NetcoreIntegration(this.integrationId);
		}
		return this.netcoreIntegration;
	}
}
