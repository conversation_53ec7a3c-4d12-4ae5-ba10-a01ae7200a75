import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const temporalAddOnsOrderReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456'] })
});

export type TemporalAddOnsOrderReq = Static<
	typeof temporalAddOnsOrderReqSchema
>;

export const addOnsOrderReserveResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		ReservationNo: t.String()
	})
});

export type AddOnsReserveOrderRes = Static<typeof addOnsOrderReserveResSchema>;

export const addonsAppointmentResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Optional(
		t.Object({
			ActivityId: t.String()
		})
	)
});

export type AddonsAppointmentRes = Static<typeof addonsAppointmentResSchema>;
