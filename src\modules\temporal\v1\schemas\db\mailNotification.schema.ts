import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const mailNotificationTableSchema = pgTable('mail_notification', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	NextOrderStatus: text('next_order_status'),
	NextOrderProgress: text('next_order_progress'),
	Language: text('language'),
	SendNotification: boolean('send_notification'),
	UpdateOrder: boolean('update_order'),
	TroikaCancel: boolean('troika_cancel'),
	TroikaCancelRemark: text('troika_cancel_remark'),
	TitleText: text('title_text').notNull(),
	BodyText: text('body_text').notNull(),
	CtaTitle: text('cta_title'),
	CtaLink: text('cta_link'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectMailNotificationSchema =
	typeof mailNotificationTableSchema.$inferSelect;
