import { getCache, setCache } from '../../config/cache.config';
import { envConfig } from '../../config/env.config';
import { pinoLog } from '../../config/pinoLog.config';
import { CacheKeyEnum } from '../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import type * as moduleSchema from '../../modules/reward/v1/schemas/api/rewards.schema';
import { fetchApi } from '../helper/fetchApi.helper';
import { getWerasToken } from './helper/werasToken.helper';
import type {
	WerasGetCustomerBillsReq,
	WerasGetCustomerBillsRes
} from './schemas/api/werasGetCustomerBills.schema';
import type { WerasGetItemsRes } from './schemas/api/werasGetItems.schema';
import type {
	TierInfo,
	WerasGetMembershipReq,
	WerasGetMembershipRes
} from './schemas/api/werasGetMembership.schema';
import type { WerasGetMyRewardsRes } from './schemas/api/werasGetMyRewards.schema';
import type { WerasGetOnlineCatalogueRes } from './schemas/api/werasGetOnlineCatalogue.schema';
import type {
	WerasGetPersonalisedReportingReq,
	WerasGetPersonalisedReportingRes
} from './schemas/api/werasGetPersonalisedReporting.schema';
import type { WerasGetPromotionListRes } from './schemas/api/werasGetPromotionList.schema';
import type {
	WerasGetRedeemItemReq,
	WerasGetRedeemItemRes
} from './schemas/api/werasGetRedeemItem.schema';
import type { WerasGetTransactionsRes } from './schemas/api/werasGetTransactions.schema';
import type {
	WerasUpdateRewardsFlagReq,
	WerasUpdateRewardsFlagRes
} from './schemas/api/werasUpdateRewardsFlag.schema';

class WerasIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getItemsWeras(
		payload: moduleSchema.GetItemsReqBody
	): Promise<WerasGetItemsRes> {
		const cacheKey = `${CacheKeyEnum.WERAS_GET_ITEMS}_${payload.Type}_${
			payload.Page || '1'
		}_${payload.SortBy}_${payload.SortType}_${payload.CategoryId || '0'}_${
			payload.Tier
		}`;

		try {
			const cachedData = await getCache(cacheKey);
			if (cachedData) {
				return JSON.parse(cachedData) as WerasGetItemsRes;
			}
		} catch (error) {
			pinoLog.warn('Cache retrieval failed for getItems:', error);
		}

		const token = await getWerasToken();

		const baseUrl = envConfig().WERAS_GET_ITEMS;
		const url = new URL(`${baseUrl}/${payload.Type}`);

		if (payload.Page) url.searchParams.append('page', payload.Page.toString());
		url.searchParams.append('sort_by', payload.SortBy);
		url.searchParams.append('sort_type', payload.SortType);
		if (payload.CategoryId !== undefined)
			url.searchParams.append('category_id', payload.CategoryId.toString());
		if (payload.Tier) url.searchParams.append('tier', payload.Tier);

		const options = {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		};

		const res = await fetchApi(this.integrationId, url.toString(), options);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		try {
			await setCache(cacheKey, JSON.stringify(resBody), 900);
		} catch (error) {
			pinoLog.warn('Cache set failed for getItems:', error);
		}

		return resBody as WerasGetItemsRes;
	}

	async redeemItemWeras(
		payload: WerasGetRedeemItemReq
	): Promise<WerasGetRedeemItemRes> {
		const token = await getWerasToken();
		const url = `${envConfig().WERAS_GET_REDEEM_ITEMS}`;

		const body = {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(payload)
		};

		const res = await fetchApi(this.integrationId, url, body);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			const badReqError = [
				'Item is not available.',
				'No stock available',
				'Customer rewards account is inactive',
				'Customer exceeded redemption limit'
			];

			if (badReqError.includes(resBody.data?.message)) {
				throw new UE_ERROR(
					resBody.data?.message,
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{
						integrationId: this.integrationId
					}
				);
			}

			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as WerasGetRedeemItemRes;
	}

	async getMembershipWeras(
		payload: WerasGetMembershipReq
	): Promise<WerasGetMembershipRes> {
		const token = await getWerasToken();
		const url = envConfig().WERAS_GET_MEMBERSHIP;

		const body = {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(payload)
		};

		const res = await fetchApi(this.integrationId, url, body);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			const badReqError = ['Bad Request'];

			if (badReqError.includes(resBody.data ?? '')) {
				throw new UE_ERROR(
					'Weras external API error',
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{ integrationId: this.integrationId }
				);
			}

			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const data = resBody.data;

		if (Array.isArray(data.tier_info)) {
			data.tier_info = data.tier_info.map((entry: TierInfo) => ({
				...entry,
				banner_url: `${envConfig().WERAS_BASE_URL}${entry.banner_url ?? ''}`,
				bannerh_url: `${envConfig().WERAS_BASE_URL}${entry.bannerh_url ?? ''}`
			}));
		}

		return resBody as WerasGetMembershipRes;
	}

	async getCustomerBillWeras(
		payload: WerasGetCustomerBillsReq
	): Promise<WerasGetCustomerBillsRes> {
		const token = await getWerasToken();

		const url = `${envConfig().WERAS_GET_CUSTOMER_BILLS}`;

		const body = {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(payload)
		};

		const res = await fetchApi(this.integrationId, url, body);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			const badReqError = ['No bill available.'];

			if (badReqError.includes(resBody.data?.message)) {
				throw new UE_ERROR(
					resBody.data?.message,
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{
						integrationId: this.integrationId
					}
				);
			}

			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as WerasGetCustomerBillsRes;
	}

	async getPromotionWeras(
		payload: moduleSchema.GetPromotionListReqBody
	): Promise<WerasGetPromotionListRes> {
		const cacheKey = `${CacheKeyEnum.WERAS_GET_PROMOTION_LIST}_${
			payload.CustomerId
		}_${payload.Take || '100'}`;

		try {
			const cachedData = await getCache(cacheKey);
			if (cachedData) {
				return JSON.parse(cachedData) as WerasGetPromotionListRes;
			}
		} catch (error) {
			pinoLog.warn('Cache retrieval failed for getPromotionList:', error);
		}

		const token = await getWerasToken();

		const url = `${envConfig().WERAS_GET_PROMOTION_LIST}/${
			payload.CustomerId
		}/${payload.Take}`;

		const options = {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		};

		const res = await fetchApi(this.integrationId, url, options);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			const badReqError = ['No query results for model'];

			if (badReqError.includes(resBody.data?.message)) {
				throw new UE_ERROR(
					resBody.data?.message,
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{
						integrationId: this.integrationId
					}
				);
			}

			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		try {
			await setCache(cacheKey, JSON.stringify(resBody), 900);
		} catch (error) {
			pinoLog.warn('Cache set failed for getPromotionList:', error);
		}

		return resBody as WerasGetPromotionListRes;
	}

	async getMyRewardsWeras(
		payload: moduleSchema.GetMyRewardsReqBody
	): Promise<WerasGetMyRewardsRes> {
		const token = await getWerasToken();

		const queries = payload.Page
			? `customerId=${payload.CustomerId}&fulfilStatus=${
					payload.FulfilStatus
				}&exclude=${payload.Exclude || ''}&page=${payload.Page}`
			: `customerId=${payload.CustomerId}&fulfilStatus=${
					payload.FulfilStatus
				}&exclude=${payload.Exclude || ''}`;

		const url = `${envConfig().WERAS_GET_MY_REWARDS}?${queries}`;

		const options = {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		};

		const res = await fetchApi(this.integrationId, url, options);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			const badReqError = ['The given data was invalid.'];

			if (badReqError.includes(resBody.data?.message)) {
				throw new UE_ERROR(
					resBody.data?.message,
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{ integrationId: this.integrationId }
				);
			}

			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as WerasGetMyRewardsRes;
	}

	async getTransactionsWeras(
		payload: moduleSchema.GetTransactionsReqBody
	): Promise<WerasGetTransactionsRes> {
		const token = await getWerasToken();

		const url = [
			payload.CustomerId, // Required parameter
			payload.Page || '', // Optional parameter
			payload.PerPage || '', // Optional parameter
			payload.Type || '', // Optional parameter
			payload.FromDate || '', // Optional parameter
			payload.ToDate || '' // Optional parameter
		].join('/'); // Join parameters with slashes

		const baseUrl = envConfig().WERAS_GET_TRANSACTION;
		const finalUrl = `${baseUrl}/${url}`;

		const options = {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		};

		const res = await fetchApi(this.integrationId, finalUrl, options);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			const badReqError = ['A non well formed numeric value encountered'];

			if (badReqError.includes(resBody.data?.message)) {
				throw new UE_ERROR(
					resBody.data?.message,
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{ integrationId: this.integrationId }
				);
			}

			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as WerasGetTransactionsRes;
	}

	async getOnlineCataloguesWeras(): Promise<WerasGetOnlineCatalogueRes> {
		const cacheName = CacheKeyEnum.WERAS_GET_ONLINE_CATALOGUE;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as WerasGetOnlineCatalogueRes;
		}
		const token = await getWerasToken();
		const url = envConfig().WERAS_GET_ONLINE_CATALOGUE;
		const body = {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		};

		const res = await fetchApi(this.integrationId, url, body);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900);
		return resBody as WerasGetOnlineCatalogueRes;
	}

	async updateRewardFlagWeras(
		reqBody: WerasUpdateRewardsFlagReq
	): Promise<WerasUpdateRewardsFlagRes> {
		const token = await getWerasToken();

		const url = `${envConfig().WERAS_UPDATE_REWARDS_FLAG}`;

		const body = {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(reqBody)
		};

		const res = await fetchApi(this.integrationId, url, body);

		const resBody = await res.json();

		if (!res.ok || !resBody.status) {
			const badReqError = ['Voucher already used'];

			if (badReqError.includes(resBody.data?.message)) {
				throw new UE_ERROR(
					resBody.data?.message,
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{
						integrationId: this.integrationId
					}
				);
			}

			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.WERAS_SERVICE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as WerasUpdateRewardsFlagRes;
	}

	async getPersonalisedReportingWeras(
		reqBody: WerasGetPersonalisedReportingReq
	): Promise<WerasGetPersonalisedReportingRes> {
		const token = await getWerasToken();

		const url = `${envConfig().WERAS_PERSONALISED_REPORTING}`;

		const requestHeaders = {
			Authorization: `Bearer ${token}`,
			'Content-Type': 'application/json'
		};

		const res = await fetchApi(this.integrationId, url, {
			method: 'POST',
			headers: requestHeaders,
			body: JSON.stringify(reqBody)
		});

		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Weras external API error',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		return resBody;
	}
}

export default WerasIntegration;
