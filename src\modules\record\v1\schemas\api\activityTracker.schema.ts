import { type Static, t } from 'elysia';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const activityListReqSchema = t.Object({
	AccountNo: t.String({ example: '**********', minLength: 1 }),
	SystemName: t.Enum(SystemNameEnum)
});

export type ActivityListReq = Static<typeof activityListReqSchema>;

const activityListObjSchema = t.Array(
	t.Object({
		Title: t.String({ examples: ['Unifi Home Technical Report'] }),
		CaseTitle: t.String({ examples: [''] }),
		Category: t.String({ examples: ['TMFORCE'] }),
		SubCategory: t.String({ examples: ['Unifi Home Technical Report'] }),
		Status: t.String({ examples: ['In Progress'] }),
		CreatedDate: t.String({ examples: ['15 August 2024 08:30:45 PM'] }),
		ClosedDate: t.String({ examples: ['15 August 2024 03:45 PM'] }),
		BillingAccountNo: t.String({ examples: ['**********'] }),
		ReferenceNo: t.String({ examples: ['1-***********'] }),
		AccountNo: t.String({ examples: ['1-R9QA3P1'] }),
		ServiceId: t.String({ examples: ['test@unifi'] })
	})
);

export type ActivityListObj = Static<typeof activityListObjSchema>;

export const activityListResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			SRActivityList: activityListObjSchema,
			TTActivityList: activityListObjSchema
		})
	},
	{
		description: 'Activity list successfully retrieved.'
	}
);

export type ActivityListRes = Static<typeof activityListResSchema>;

export const activityDetailsReqSchema = t.Object({
	ReferenceNo: t.String({
		example: '1-A253WST4',
		description: 'Either TTNumber or Digital Order ID',
		minLength: 1
	})
});

export type ActivityDetailsReq = Static<typeof activityDetailsReqSchema>;

const caseDetails = t.Object({
	CaseInformation: t.Optional(
		t.String({
			examples: ['RNO Troubleshooting']
		})
	),
	AppointmentDate: t.Optional(
		t.String({
			examples: ['10 October 2024']
		})
	),
	AppointmentTime: t.Optional(t.String({ examples: ['11:00 AM'] })), //
	CompletedDate: t.Optional(
		t.String({
			examples: ['10 October 2024']
		})
	),
	CompletedTime: t.Optional(t.String({ examples: ['11:00 AM'] })) //
});

export type CaseDetails = Static<typeof caseDetails>;

const technicianDetails = t.Optional(
	t.Nullable(
		t.Object({
			ProfilePicture: t.Optional(
				t.Nullable(
					t.String({
						examples: ['/q4/ASG9Hasjdkgl/asg4e2q34'],
						description: 'A blob data'
					})
				)
			),
			TechnicianName: t.Optional(
				t.Nullable(
					t.String({
						examples: ['Test Technician']
					})
				)
			),
			PhoneNumber: t.Optional(
				t.Nullable(
					t.String({
						examples: ['0123456789']
					})
				)
			),
			Latitude: t.Optional(t.Nullable(t.String({ examples: ['3.1243461'] }))),
			Longitude: t.Optional(
				t.Nullable(
					t.String({
						examples: ['101.82375534']
					})
				)
			),
			ETTA: t.Optional(
				t.Nullable(
					t.String({
						examples: ['10/10/2024 10:30:00']
					})
				)
			)
		})
	)
);

export type TechnicianDetails = Static<typeof technicianDetails>;

const reportItem = t.Object({
	OperationFlag: t.Optional(t.Nullable(t.Boolean())),
	Title: t.String({
		examples: ['On Site'],
		description: 'Ticket Status'
	}),
	Description: t.String({
		examples: [
			'Our technician is on site and your case is in progress of being resolved'
		],
		description: 'Ticket Status Description'
	}),
	Timestamp: t.String({
		examples: ['10 October 2024 , 10:28 AM']
	})
});

export type ReportItem = Static<typeof reportItem>;

const reportItemList = t.Array(reportItem);

export type ReportItemList = Static<typeof reportItemList>;

export const activityDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			ReferenceNumber: t.String({
				examples: ['1-3295789047190']
			}),
			TicketStatusDescription: t.String({
				examples: ['Your case is currently being addressed']
			}),
			Status: t.String({ examples: ['Scheduled'] }),
			CaseDetails: caseDetails,
			TechnicianDetails: t.Nullable(technicianDetails),
			ReportStatus: reportItemList
		})
	},
	{
		description: 'Activity details successfully retrieved.'
	}
);

export type ActivityDetailsRes = Static<typeof activityDetailsResSchema>;
