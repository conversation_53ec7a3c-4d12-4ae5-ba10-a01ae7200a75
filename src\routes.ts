import { Elysia, t } from 'elysia';
import { SourceEnum } from './enum/header.enum';
import {
	restrictToInternal,
	validateApiKey,
	validateToken
} from './middleware/auth';
import uaidRoutes from './middleware/uaid';
import { privateAddressV1Routes } from './modules/address';
import { privateBatchV1Routes } from './modules/batch';
import { privateCatalogueV1Routes } from './modules/catalogue';
import { privateEligibilityV1Routes } from './modules/eligibility';
import { internalRoutes } from './modules/internal';
import { privateNotificationV1Routes } from './modules/notification';
import { privateOrderV1Routes, protectedOrderV1Routes } from './modules/order';
import {
	privatePaymentV1Routes,
	protectedPaymentV1Routes,
	publicPaymentV1Routes
} from './modules/payment';
import {
	privateRecordV1Routes,
	protectedRecordV1Routes
} from './modules/record';
import { privateRewardV1Routes } from './modules/reward';
import { privateSecurityV1Routes } from './modules/security';
import { privateSettingV1Routes } from './modules/setting';
import {
	privateTemporalV1Routes,
	protectedTemporalV1Routes
} from './modules/temporal';
import { privateUserV1Routes, protectedUserV1Routes } from './modules/user';
import { privateUtilV1Routes, protectedUtilV1Routes } from './modules/util';
import { baseHeaderSchema } from './shared/schemas/api/headers.schema';

export const apiModuleRoutes = new Elysia({ prefix: '/api' });

/**
 * 🔒 Private API
 * Requires BOTH a Bearer Token and an API Key for authentication.
 * Used for highly sensitive operations.
 */
apiModuleRoutes.guard(
	{
		headers: baseHeaderSchema,
		beforeHandle: async ({
			headers: { authorization, 'x-api-key': xApiKey }
		}) => {
			await validateToken(authorization);
			validateApiKey(xApiKey);
		}
	},
	app =>
		app
			.use(privateAddressV1Routes)
			.use(privateBatchV1Routes)
			.use(privateCatalogueV1Routes)
			.use(privateEligibilityV1Routes)
			.use(privateNotificationV1Routes)
			.use(privateOrderV1Routes)
			.use(privatePaymentV1Routes)
			.use(privateRewardV1Routes)
			.use(privateRecordV1Routes)
			.use(privateSecurityV1Routes)
			.use(privateSettingV1Routes)
			.use(privateUserV1Routes)
			.use(privateUtilV1Routes)
			.use(privateTemporalV1Routes)
);

/**
 * 🔐 Protected API
 * Requires an API Key for authentication.
 * Used for trusted integrations where API keys are sufficient.
 */
apiModuleRoutes.guard(
	{
		headers: t.Object({
			'x-api-key': t.String(),
			source: t.Enum(SourceEnum),
			segment: t.String()
		}),
		beforeHandle({ headers: { 'x-api-key': xApiKey } }) {
			validateApiKey(xApiKey);
		}
	},
	app =>
		app
			.use(protectedUserV1Routes)
			.use(protectedPaymentV1Routes)
			.use(protectedTemporalV1Routes)
			.use(protectedRecordV1Routes)
			.use(protectedOrderV1Routes)
			.use(protectedUtilV1Routes)
);

/**
 * 🛠️ Internal API
 * Restricted to developers only.
 * Not intended for external use. Use caution when exposing.
 */

apiModuleRoutes.guard(
	{
		beforeHandle(ctx) {
			restrictToInternal(
				ctx.headers['internal-key'] ?? '',
				ctx.headers['x-forwarded-for'] ?? '',
				ctx.headers.host ?? ''
			);
		},
		detail: {
			hide: true
		}
	},
	app => app.use(internalRoutes)
);

/**
 * 🌐 Public API
 * Requires no authentication.
 * Used for public operations.
 */
apiModuleRoutes.use(publicPaymentV1Routes);

/**
 * 📱 UAID API
 * Used for authentication and authorization operations.
 */
apiModuleRoutes.use(uaidRoutes);
