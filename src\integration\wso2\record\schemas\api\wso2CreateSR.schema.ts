import { type Static, t } from 'elysia';

const wso2CreateSRReqSchema = t.Object({
	SubmitGeneralSR: t.Object({
		SystemName: t.String(),
		CustomerIDType: t.String(),
		CustomerIDValue: t.String(),
		CustomerAccountNo: t.String(),
		Type: t.String(),
		Category: t.String(),
		SubCategory: t.String(),
		Case: t.String(),
		CreatedDate: t.String(),
		Source: t.String(),
		CreatedSource: t.String(),
		Priority: t.String(),
		Status: t.String(),
		InterfaceStatus: t.String(),
		OwnerGroup: t.String(),
		CustomerComments: t.String(),
		ProductCategory: t.String(),
		Product: t.String(),
		ProductType: t.String(),
		ServiceRowID: t.String(),
		ClosureCategory: t.String(),
		ClosureReason: t.String(),
		ClosureRemarks: t.String(),
		ContactDetailRowID: t.String(),
		ContactDetailReportedID: t.String(),
		BillingAccountRowID: t.String(),
		BillingAccountNo: t.String(),
		PreferredAcknowledgement: t.String(),
		ContactID: t.String(),
		ChangePreferredCommFlag: t.String(),
		ChangePreferredCommMethod: t.String(),
		ChangePreferredContactName: t.String(),
		ChangePreferredCellularPhone: t.String(),
		ChangePreferredEmailAddress: t.String(),
		ChangePreferredHomePhone: t.String(),
		ChangePreferredWorkPhone: t.String(),
		SerialNumber: t.String(),
		TMBModifyOrderRequest: t.String(),
		TMBModifyOrderReason: t.String(),
		TMBSMARTProduct: t.String(),
		LoginIDType: t.String(),
		TMBLoginID: t.String(),
		TMTAndCFlag: t.String(),
		TMTTBankAcctNum: t.Optional(t.String()),
		TMTTBankName: t.Optional(t.String()),
		TMTTPaymentMode: t.Optional(t.String()),
		TMTTIDNum: t.Optional(t.String()),
		TMTTIDType: t.Optional(t.String()),
		TMTTConfirmBankAcctNum: t.Optional(t.String())
	})
});
export type Wso2CreateSRReq = Static<typeof wso2CreateSRReqSchema>;

export const wso2CreateSRResSchema = t.Optional(
	t.Object({
		Status: t.Optional(
			t.Object({
				Type: t.Optional(t.String()),
				Code: t.Optional(t.String()),
				Message: t.Optional(t.String())
			})
		),
		RespondSubmitGeneralSR: t.Optional(
			t.Object({
				ErrorCode: t.Optional(t.String()),
				ErrorMessage: t.Optional(t.String()),
				SRNumber: t.Optional(t.String())
			})
		)
	})
);
export type Wso2CreateSRRes = Static<typeof wso2CreateSRResSchema>;
