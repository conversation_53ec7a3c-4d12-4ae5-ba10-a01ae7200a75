import { randomUUID } from 'node:crypto';
import { bearer } from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import {
	ottActivateReqSchema,
	ottActivateResSchema
} from '../../../order/v1/schemas/api/ottActivate.schema';
import {
	ottSwapReqSchema,
	ottSwapResSchema
} from '../../../order/v1/schemas/api/ottSwap.schema';
import { temporalSubmitOttOrderReqSchema } from '../schemas/api/ott.schema';
import { OttService } from '../services/ott.service';

const prefix = '/ott';

// Private API Routes (Called by UA)
export const privateOttV1Routes = new Elysia({ prefix })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			ottService: new OttService(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/prepare-activation',
		async ctx => {
			const source = ctx.headers.source;
			const segment = ctx.headers.segment;
			return await ctx.ottService.handlePrepareOttActivationOrder(
				source,
				segment,
				ctx.body
			);
		},
		{
			headers: baseHeaderSchema,
			body: ottActivateReqSchema,
			response: {
				200: ottActivateResSchema
			},
			detail: {
				tags: ['Temporal'],
				description:
					'Save OTT Activation Order to database for later submission. <br><br><b>Used by:</b> Universal App (UA)<br><b>Target:</b> OMG'
			}
		}
	)
	.post(
		'/prepare-swap',
		async ctx => {
			const source = ctx.headers.source;
			const segment = ctx.headers.segment;
			return await ctx.ottService.handlePrepareOttSwapOrder(
				source,
				segment,
				ctx.body
			);
		},
		{
			headers: baseHeaderSchema,
			body: ottSwapReqSchema,
			response: {
				200: ottSwapResSchema
			},
			detail: {
				tags: ['Temporal'],
				description:
					'Save OTT Swap Order to database for later submission. <br><br><b>Used by:</b> Universal App (UA)<br><b>Target:</b> OMG'
			}
		}
	);

// Protected API Routes (Called by Temporal)
export const protectedOttV1Routes = new Elysia({ prefix })
	.post(
		'/submit-activation',
		async ctx => {
			return await new OttService(randomUUID()).handleSubmitOttActivation(
				ctx.body
			);
		},
		{
			body: temporalSubmitOttOrderReqSchema,
			response: {
				200: ottActivateResSchema
			},
			detail: {
				tags: ['Temporal'],
				description:
					'Submit previously prepared OTT Activation Order to OMG. <br><br><b>Used by:</b> Temporal Client<br><b>Target:</b> OMG'
			}
		}
	)
	.post(
		'/submit-swap',
		async ctx => {
			return await new OttService(randomUUID()).handleSubmitOttSwap(ctx.body);
		},
		{
			body: temporalSubmitOttOrderReqSchema,
			response: {
				200: ottSwapResSchema
			},
			detail: {
				tags: ['Temporal'],
				description:
					'Submit previously prepared OTT Swap Order to OMG. <br><br><b>Used by:</b> Temporal Client<br><b>Target:</b> OMG'
			}
		}
	);
