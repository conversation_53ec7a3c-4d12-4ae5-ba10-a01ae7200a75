/** This enum is introduced to avoid developers from using the same cache key name for different purposes.
 This will help to avoid cache key conflicts.
 If you want to add a new cache key, add it here and use it in the code. */
export enum CacheKeyEnum {
	// UE cache keys
	LINKED_ACCOUNT_PROFILE = 'linkedAccountProfile',
	ANNUAL_BILL_STATEMENT_LAST_FIVE_YEARS = 'annualBillStatementLastFiveYears',
	ANNUAL_BILL_STATEMENT_BY_YEAR = 'annualBillStatementByYear',
	LOV_ASSURANCE_LIST = 'lovAssuranceList',
	LOV_NON_TRUCKROLL_LIST = 'lovNonTruckRollList',
	LOV_TRUCKROLL_LIST = 'lovTruckRollList',

	// UAID cache keys
	UAID_ID_TOKEN_INFO = 'uaidIdTokenInfo',

	// APIM cache keys
	APIM_TOKEN = 'apimToken',
	TAAS_APIM_TOKEN = 'taasApimToken',

	// MMAG cache keys
	MMAG_TOKEN = 'mmagToken',

	// WSO2 cache keys
	WSO2_CUSTOMER_ACCOUNTS = 'wso2CustomerAccounts',
	WSO2_SERVICE_ACCOUNTS = 'wso2ServiceAccounts',
	WSO2_LIGHTWEIGHT_BILLING_DETAILS = 'wso2LightweightBillingDetails',
	WSO2_CONCISE_BA_DETAILS = 'wso2ConciseBaDetails',
	WSO2_CONCISE_CUST_INFO = 'wso2ConciseCustInfo',
	WSO2_NOVA_BILLING_PROFILE = 'wso2NovaBillingProfile',
	WSO2_EXPIRY_DISCOUNT = 'wso2ExpiryDiscount',
	WSO2_CUSTOMER_PROFILE_CHECK = 'wso2CustomerProfileCheck',
	WSO2_EDWH_MOANA = 'wso2EdwhMoana',
	WSO2_QUERY_HARD_SOFT_BUNDLE = 'wso2QueryHardSoftBundle',
	WSO2_GRANITE_ADDRESS_BY_ADDRESS_ID = 'wso2GraniteAddressByAddressId',
	WSO2_GRANITE_ADDRESS_BY_COORDINATE = 'wso2GraniteAddressByCoordinate',
	WSO2_GRANITE_ADDRESS_BY_KEYWORD_AND_STATE = 'wso2GraniteAddressByKeywordAndState',

	// WERAS cache keys
	WERAS_TOKEN = 'werasToken',
	WERAS_GET_ITEMS = 'werasGetItems',
	WERAS_GET_PROMOTION_LIST = 'werasGetPromotionList',
	WERAS_GET_ONLINE_CATALOGUE = 'werasGetOnlineCatalogue'
}
