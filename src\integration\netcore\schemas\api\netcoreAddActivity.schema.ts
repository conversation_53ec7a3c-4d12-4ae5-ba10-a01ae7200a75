import { type Static, t } from 'elysia';

/** Single Activity Schema */
const netcoreActivityItemSchema = t.Object({
	activity_name: t.String(),
	asset_id: t.Optional(t.String()),
	timestamp: t.String(),
	identity: t.Union([t.String(), t.Number()]),
	annonid: t.Optional(t.String()),
	activity_source: t.Union([t.Literal('web'), t.Literal('app')]),
	activity_params: t.Record(t.String(), t.Unknown())
});

/** Bulk Request Schema */
export const netcoreAddActivityReqSchema = t.Array(netcoreActivityItemSchema);
export type NetcoreAddActivityReq = Static<typeof netcoreAddActivityReqSchema>;
