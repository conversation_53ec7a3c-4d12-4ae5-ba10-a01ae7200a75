import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';
import { AddOnsCatalogueCategoryEnum } from '../../../../../enum/addOns.enum';

const tncDescriptionSchema = t.Array(t.String());
export type TncDescription = Static<typeof tncDescriptionSchema>;

export const addonsMetadataTableSchema = pgTable('addons_metadata', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Category: text('category', {
		enum: Object.values(AddOnsCatalogueCategoryEnum) as [string, ...string[]]
	})
		.notNull()
		.unique(),
	Type: text('description').notNull(),
	FaqUrl: text('faq_url').notNull(),
	TncUrl: text('tnc_url').notNull(),
	TncDescription: json('tnc_description').$type<TncDescription>(),
	WarrantyPolicyUrl: text('warranty_policy_url'),
	PrivacyNoticeUrl: text('privacy_notice_url'),
	UserGuideUrl: text('user_guide_url'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAddonsMetadata =
	typeof addonsMetadataTableSchema.$inferSelect;
