import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const complaintLovTableSchema = pgTable('complaint_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	SystemName: text('system_name').notNull(),
	ComplaintType: text('complaint_type').notNull(),
	CategoryKey: text('category_key').notNull(),
	CategoryValue: text('category_value').notNull(),
	SubcategoryKey: text('subcategory_key'),
	SubcategoryValue: text('subcategory_value'),
	GroupNameKey: text('group_name_key'),
	GroupNameValue: text('group_name_value'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});
