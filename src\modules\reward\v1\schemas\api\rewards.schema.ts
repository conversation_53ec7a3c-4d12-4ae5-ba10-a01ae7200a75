import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

// Schema for get-items START here
export const getItemsReqBodySchema = t.Object({
	Type: t.Enum(
		{
			All: 'all',
			Highlighted: 'highlighted'
		},
		{
			description: 'Fetch either all items or only highlighted ones.',
			example: 'all'
		}
	),
	Page: t.Optional(
		t.Number({
			description: 'Pagination page number, starting from 1.',
			minimum: 1,
			example: 1
		})
	),
	SortBy: t.Enum(
		{
			Name: 'name',
			CreatedAt: 'created_at',
			PointValue: 'point_value'
		},
		{
			description: 'Field to sort by: name, created_at, or point_value.',
			example: 'point_value'
		}
	),
	SortType: t.Enum(
		{
			Asc: 'asc',
			Desc: 'desc'
		},
		{
			description: 'Sort order: ascending (asc) or descending (desc).',
			example: 'asc'
		}
	),
	CategoryId: t.Optional(
		t.Integer({
			description:
				'Category ID in the WERAS database. 0 is for all categories.',
			example: 0
		})
	),
	Tier: t.Enum(
		{
			Silver: 'Silver',
			Gold: 'Gold',
			Platinum: 'Platinum',
			Diamond: 'Diamond'
		},
		{
			description: 'Tier to fetch: Silver, Gold, Platinum, or Diamond.',
			example: 'Gold'
		}
	)
});

export type GetItemsReqBody = Static<typeof getItemsReqBodySchema>;

export const paginationDetailsSchema = t.Object({
	PerPage: t.String({ description: 'Number of items per page', example: '10' }),
	CurrentPage: t.String({ description: 'Current page number', example: '1' }),
	Total: t.String({ description: 'Total number of items', example: '15' }),
	LastPage: t.String({ description: 'Last page number', example: '2' })
});

export const stocksSchema = t.Array(
	t.Object({
		Id: t.Integer({ description: 'Stock ID', example: 257 }),
		BranchId: t.Integer({ description: 'Branch ID', example: 1 }),
		ItemId: t.Integer({ description: 'Item ID', example: 3110 }),
		Type: t.String({ description: 'Stock type', example: 'online' }),
		Quantity: t.Integer({ description: 'Stock quantity', example: 10 }),
		QuantityThreshold: t.Integer({
			description: 'Threshold before low-stock warning',
			example: 4
		}),
		TypeThreshold: t.String({
			description: 'Threshold type',
			example: 'quantity'
		}),
		QuantityUsed: t.Integer({ description: 'Used quantity', example: 2 }),
		Infinite: t.Integer({ description: 'Is quantity infinite', example: 0 }),
		CreatedBy: t.Integer({ description: 'Creator user ID', example: 2900 }),
		ModifiedBy: t.Integer({
			description: 'Last modifier user ID',
			example: 1700
		}),
		DeletedBy: t.Integer({ description: 'Deleted by user ID', example: 0 }),
		CreatedAt: t.String({
			description: 'Creation timestamp',
			example: '2025-01-15 16:10:52'
		}),
		UpdatedAt: t.String({
			description: 'Update timestamp',
			example: '2025-02-24 23:57:43'
		}),
		DeletedAt: t.String({
			description: 'Deletion timestamp if any',
			example: '',
			default: ''
		})
	})
);

export const redemptionLimitSchema = t.Object({
	Quantity: t.Integer({ description: 'Total allowed redemptions', example: 1 }),
	QuantityPeriod: t.String({
		description: 'Redemption period type',
		example: 'day'
	}),
	QuantityUsed: t.Integer({
		description: 'Total redemptions used',
		example: 0
	}),
	QuantityAvailable: t.Integer({
		description: 'Remaining redemptions',
		example: 0
	})
});

export const feGetItemsSchema = t.Object({
	Id: t.Integer({ description: 'Unique ID of the reward item', example: 4109 }),
	Name: t.String({
		description: 'Name of the reward item',
		example: 'RM10 Setel E-Voucher'
	}),
	Description: t.String({
		description: 'HTML-formatted description of the reward item',
		example: '<p>Balik Kampung Raya with Unifi Rewards</p>'
	}),
	Category: t.Integer({ description: 'Category ID of the item', example: 52 }),
	Brand: t.Integer({ description: 'Brand ID of the item', example: 66 }),
	InventoryChannel: t.String({
		description: 'Inventory channel type',
		example: 'online'
	}),
	RedemptionFlow: t.String({
		description: 'Redemption flow type',
		example: 'default'
	}),
	RedemptionScope: t.String({
		description: 'Redemption scope',
		example: 'home'
	}),
	GameFlag: t.Integer({ description: 'Game feature flag', example: 0 }),
	Status: t.String({
		description: 'Status of the reward item',
		example: 'active'
	}),
	Segment: t.String({ description: 'User segment', example: 'both' }),
	AdditionalDescription: t.String({
		description: 'Additional description of the item',
		example: '',
		default: ''
	}),
	RmValue: t.Integer({ description: 'Ringgit value of the item', example: 0 }),
	PointValue: t.Integer({
		description: 'Point value needed to redeem',
		example: 0
	}),
	CodeValue: t.String({
		description: 'Redemption code value',
		example: '',
		default: ''
	}),
	StartDate: t.String({
		description: 'Start date of availability',
		example: '2025-03-29 00:00:00'
	}),
	EndDate: t.String({
		description: 'End date of availability',
		example: '2025-12-31 00:00:00'
	}),
	Image: t.String({
		description: 'URL of the item image',
		example: '/item-images/rm10-setel-e-voucher.jpg'
	}),
	Csvfile: t.String({
		description: 'CSV metadata file',
		example: '/csv/rm10-setel-e-voucher.csv'
	}),
	FastTrack: t.Integer({ description: 'Fast track flag', example: 0 }),
	Tnc: t.String({
		description: 'Terms and Conditions in HTML format',
		example: '<p>Terms here</p>'
	}),
	DownloadUrl: t.String({
		description: 'Optional download URL',
		example: '',
		default: ''
	}),
	Highlighted: t.Integer({ description: 'Highlight display flag', example: 1 }),
	Exclude: t.Integer({ description: 'Exclude flag', example: 0 }),
	Barcode: t.String({ description: 'Barcode info', example: 'No barcode' }),
	Qrcode: t.String({ description: 'QR code info', example: 'No QR code' }),
	Display: t.String({ description: 'Display target', example: 'both' }),
	CreatedBy: t.Integer({
		description: 'User ID who created the item',
		example: 1482
	}),
	ExpiryDate: t.String({
		description: 'Expiration date of the item',
		example: '2025-12-31 00:00:00'
	}),
	ModifiedBy: t.Integer({
		description: 'User ID who last modified the item',
		example: 1482
	}),
	DeletedBy: t.Integer({
		description: 'User ID who deleted the item (if any)',
		example: 0
	}),
	CreatedAt: t.String({
		description: 'Creation timestamp',
		example: '2025-03-29 11:01:46'
	}),
	UpdatedAt: t.String({
		description: 'Last updated timestamp',
		example: '2025-03-29 11:03:32'
	}),
	DeletedAt: t.String({
		description: 'Deletion timestamp if any',
		example: '',
		default: ''
	}),
	Campaign: t.String({ description: 'Campaign tag', example: '', default: '' }),
	Stocks: stocksSchema,
	RedemptionLimit: redemptionLimitSchema
});

export const getItemsResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		Items: t.Array(feGetItemsSchema, { description: 'List of reward items' }),
		PaginationDetails: paginationDetailsSchema
	})
});

export type GetItemsRes = Static<typeof getItemsResSchema>;
// Schema for get-items END here

// Schema for redeem-item START here
export const getRedeemItemReqBodySchema = t.Object({
	ItemId: t.Number({
		description: 'Unique identifier for the item to be redeemed',
		minimum: 1,
		example: 3826
	}),
	CustomerId: t.Number({
		description: 'Customer Unique ID',
		minimum: 1,
		example: 4831920
	}),
	Quantity: t.Number({
		description: 'Quantity of the item to be redeemed',
		minimum: 1,
		example: 1
	}),
	AccountId: t.Optional(
		t.Integer({
			description:
				'Account Unique ID obtained from “Get Customer Bills” API call. This parameter is required for bill rebate redemption.',
			example: 987654
		})
	)
});

export type GetRedeemItemReqBody = Static<typeof getRedeemItemReqBodySchema>;

export const getRedeemItemResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		RewardName: t.String({
			description: 'Name of the reward item redeemed',
			example: '15% OFF ZUS handcrafted drinks'
		}),
		ItemId: t.Number({
			description: 'Unique identifier of the redeemed item',
			example: 3725
		}),
		PointsRedeemed: t.Number({
			description: 'Total points used for this redemption',
			example: 0
		}),
		PointsBalance: t.Number({
			description: 'Customer’s remaining points after redemption',
			example: 0
		}),
		Quantity: t.Number({
			description: 'Number of items redeemed',
			example: 1
		}),
		Code: t.String({
			description: 'Voucher or redemption code provided to the user',
			example: 'ZUNIFI15'
		}),
		ExpiryDate: t.String({
			description: 'Expiry date of the redeemed voucher or reward',
			example: '14 August 2025'
		}),
		ItemType: t.String({
			description: 'Type/category of the item redeemed',
			example: 'others'
		})
	})
});

export type GetRedeemItemRes = Static<typeof getRedeemItemResSchema>;
// Schema for redeem-item END here

// Schema for get-membership START here
export const getMembershipReqBodySchema = t.Object({
	ActionFlag: t.Optional(
		t.Enum(
			{
				Pre_register: 'Pre_register',
				query: 'query'
			},
			{
				description:
					'The action flag indicating the operation (e.g., Pre_register or query)',
				example: 'query'
			}
		)
	)
});

export type GetMembershipReqBody = Static<typeof getMembershipReqBodySchema>;

// Tier Info
export const tierInfoModuleSchema = t.Object({
	Name: t.String({ description: 'Tier name', example: 'Gold' }),
	Benefit: t.Array(t.String(), {
		description: 'List of benefits available for this tier',
		example: ['Shopping deals & discounts', 'Cash voucher redemption']
	}),
	BannerUrl: t.String({
		description: 'URL to banner image for this tier',
		example: 'https://rewards.unifi.com.my/static/images/gold.png'
	})
});

// Promo Info
export const promoInfoModuleSchema = t.Object({
	Name: t.String({
		description: 'Promotion name',
		example: 'Ohsem Rewards'
	}),
	BannerUrl: t.String({
		description: 'Banner image for the promotion',
		example: '/static/images/ohsem_rewards_bg.png'
	}),
	RedirectUrl: t.String({
		description: 'URL to redirect user to the promotion',
		example: ''
	})
});

// Final response schema
export const getMembershipResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		Id: t.Number({ description: 'Unique customer ID', example: 2044535 }),
		Name: t.String({
			description: 'Full name of the customer',
			example: 'Voon Khian Jin'
		}),
		Email: t.String({
			description: 'Customer email address',
			example: '<EMAIL>'
		}),
		MemberNo: t.String({
			description: 'Membership number',
			example: '72144754'
		}),
		MemberCategory: t.String({
			description: 'Membership category',
			example: 'Consumer'
		}),
		LoyaltyCardNo: t.String({
			description: 'Customer’s loyalty card number',
			example: '6364020021447664'
		}),
		RegistrationDate: t.String({
			description: 'Date of registration',
			example: '2017-04-20T15:00:12.000000Z'
		}),
		EarnedPoints: t.Number({ description: 'Total earned points', example: 0 }),
		RedeemedPoints: t.Number({
			description: 'Total redeemed points',
			example: 0
		}),
		ExpiredPoints: t.Number({
			description: 'Total expired points',
			example: 0
		}),
		MemberStatus: t.String({
			description: 'Status of the membership',
			example: 'Active'
		}),
		RedirectUrl: t.String({
			description: 'URL to redirect user to login or membership page',
			example: 'https://rewards.unifi.com.my/...'
		}),
		PointsExpiredThisMonth: t.Number({
			description: 'Points expiring this month',
			example: 0
		}),
		AvailablePoints: t.Number({
			description: 'Total available points for redemption',
			example: 0
		}),
		GameUrl: t.String({
			description: 'Link to WERAS game (if any)',
			example: ''
		}),
		BannerUrl: t.String({
			description: 'Banner for user dashboard or game',
			example: ''
		}),
		Game: t.String({ description: 'Game name or code (if any)', example: '' }),
		Tier: t.String({
			description: 'Current tier level of the user',
			example: 'Gold'
		}),
		TierPrevious: t.String({
			description: 'Previous tier level',
			example: 'Gold'
		}),
		TierInfo: t.Array(tierInfoModuleSchema, {
			description: 'Details of available membership tiers'
		}),
		PromoInfo: t.Array(promoInfoModuleSchema, {
			description: 'Active promotional campaigns for user'
		})
	})
});

export type GetMembershipRes = Static<typeof getMembershipResSchema>;
// Schema for get-membership END here

// Schema for get-customer-bills START here
export const getCustomerBillsReqBodySchema = t.Object({
	CustomerId: t.Number({
		description: 'Customer Unique ID',
		minimum: 1,
		example: 4831920
	}),
	RedemptionScope: t.Optional(
		t.Enum(
			{
				Home: 'home',
				Mobile: 'mobile'
			},
			{
				description:
					'Scope of redemption. Allowed values: "home" (default) or "mobile". Use "home" to fetch only unifi home bills and "mobile" to fetch only unifi mobile bills.',
				default: 'home',
				example: 'home'
			}
		)
	)
});

export type GetCustomerBillsReqBody = Static<
	typeof getCustomerBillsReqBodySchema
>;

export const getCustomerBillsResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Array(
		t.Object({
			Id: t.Number({ description: 'Unique bill ID', example: ********* }),
			Source: t.String({
				description: 'Billing source system',
				example: 'NOVABRM'
			}),
			CustomerCpcId: t.String({
				description: 'Customer CPC ID',
				example: '1-1A5-9817'
			}),
			IdNumber: t.String({
				description: 'Identification number of the customer',
				example: '780326-08-7135'
			}),
			IdType: t.String({
				description: 'Type of identification used',
				example: 'NEW_NRIC'
			}),
			AccountNumber: t.String({
				description: 'Account number related to bill',
				example: '**********'
			}),
			BillPeriod: t.Nullable(
				t.String({ description: 'Billing period', example: null })
			),
			BillNumber: t.String({
				description: 'Unique bill number',
				example: '************'
			}),
			BillDate: t.String({ description: 'Bill date', example: '2023-09-16' }),
			OutstandingBalance: t.Number({
				description: 'Total outstanding amount',
				example: 0
			}),
			CurrentMonthCharge: t.Number({
				description: 'Charges for the current month',
				example: 285.03
			}),
			TotalDue: t.Number({ description: 'Total amount due', example: 285.05 }),
			ProcessedAmount: t.Number({
				description: 'Amount already processed',
				example: 0
			}),
			PaymentDueDate: t.String({
				description: 'Due date for payment',
				example: '2023-10-07'
			}),
			CreatedBy: t.Number({
				description: 'User ID who created the record',
				example: 113
			}),
			ModifiedBy: t.Number({
				description: 'User ID who last modified the record',
				example: 113
			}),
			CreatedAt: t.String({
				description: 'Creation timestamp',
				example: '2023-09-18 16:25:04'
			}),
			UpdatedAt: t.String({
				description: 'Last update timestamp',
				example: '2023-09-18 16:25:04'
			}),
			SyncedAt: t.Nullable(
				t.String({ description: 'Timestamp when synced', example: null })
			),
			AccountId: t.Number({
				description: 'Account ID associated with bill',
				example: 2182156
			})
		})
	)
});

export type GetCustomerBillsRes = Static<typeof getCustomerBillsResSchema>;
// Schema for get-customer-bills END here

// Schema for get-promotion-list START here
export const getPromotionListReqBodySchema = t.Object({
	CustomerId: t.Number({
		description: 'Customer Unique ID',
		minimum: 1,
		example: 4831920
	}),
	Take: t.Number({
		description: 'Number of promotions to retrieve',
		minimum: 1,
		example: 5
	})
});

export type GetPromotionListReqBody = Static<
	typeof getPromotionListReqBodySchema
>;

export const getPromotionListResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		Error: t.Nullable(
			t.String({ description: 'Error message if present', example: null })
		),
		Message: t.Nullable(
			t.String({
				description: 'Optional message returned from WERAS',
				example: null
			})
		),
		Response: t.Array(
			t.Object({
				Id: t.Number({ description: 'Unique promotion ID', example: 3971 }),
				Name: t.String({
					description: 'Promotion name',
					example: 'Enjoy 28% OFF for any product AADA Beauty'
				}),
				Description: t.String({
					description: 'Promotion description',
					example: '<p>SapotLokal</p>'
				}),
				Category: t.Number({ description: 'Category ID', example: 1 }),
				Brand: t.Number({ description: 'Brand ID', example: 66 }),
				InventoryChannel: t.String({
					description: 'Inventory channel type',
					example: 'online'
				}),
				RedemptionFlow: t.String({
					description: 'Redemption flow type',
					example: 'download'
				}),
				RedemptionScope: t.String({
					description: 'Redemption scope',
					example: 'home'
				}),
				Flag: t.Number({ description: 'Game flag or status', example: 0 }),
				Status: t.String({
					description: 'Promotion status',
					example: 'active'
				}),
				Segment: t.String({ description: 'Customer segment', example: 'both' }),
				AdditionalDescription: t.Nullable(
					t.String({
						description: 'Additional promotion description',
						example: null
					})
				),
				RmValue: t.Number({
					description: 'RM value if applicable',
					example: 0
				}),
				PointValue: t.Number({
					description: 'Points value if applicable',
					example: 0
				}),
				CodeValue: t.String({
					description: 'Promo code',
					example: '#SENTIASAAADA'
				}),
				StartDate: t.String({
					description: 'Promotion start date',
					example: '2024-12-13 00:00:00'
				}),
				EndDate: t.String({
					description: 'Promotion end date',
					example: '2025-12-31 00:00:00'
				}),
				ExpiryDate: t.String({
					description: 'Voucher expiry date',
					example: '2025-12-31 00:00:00'
				}),
				Image: t.String({
					description: 'Promotion image path',
					example: '/item-images/28-off-aada.jpg'
				}),
				Csvfile: t.Nullable(
					t.String({ description: 'CSV file path (if any)', example: null })
				),
				FastTrack: t.Number({ description: 'Fast track flag', example: 0 }),
				Tnc: t.String({
					description: 'Terms and conditions in HTML format',
					example: '<p>Terms apply</p>'
				}),
				DownloadUrl: t.String({
					description: 'URL to redeem or download the promo',
					example: 'http://www.aadabeauty.com/'
				}),
				Highlighted: t.Number({
					description: 'Highlight status (1 = yes)',
					example: 1
				}),
				Exclude: t.Number({
					description: 'Exclude flag (1 = yes)',
					example: 0
				}),
				Barcode: t.String({
					description: 'Barcode string',
					example: 'No barcode'
				}),
				Qrcode: t.String({
					description: 'QR code string',
					example: 'No QR code'
				}),
				Display: t.String({
					description: 'Display target type',
					example: 'both'
				}),
				CreatedBy: t.Number({
					description: 'User ID who created the promotion',
					example: 1482
				}),
				ModifiedBy: t.Number({
					description: 'User ID who modified the promotion',
					example: 1482
				}),
				DeletedBy: t.Number({
					description: 'User ID who deleted the promotion (if any)',
					example: 0
				}),
				CreatedAt: t.String({
					description: 'Promotion creation timestamp',
					example: '2024-12-13 15:38:54'
				}),
				UpdatedAt: t.String({
					description: 'Promotion update timestamp',
					example: '2024-12-13 17:14:35'
				}),
				DeletedAt: t.Nullable(
					t.String({
						description: 'Promotion deletion timestamp (if any)',
						example: null
					})
				),
				Customer: t.Number({
					description: 'Customer flag or value',
					example: 0
				})
			})
		)
	})
});

export type GetPromotionListRes = Static<typeof getPromotionListResSchema>;
// Schema for get-promotion-list END here

// Schema for get-my-rewards START here
export const getMyRewardsReqBodySchema = t.Object({
	CustomerId: t.Number({
		description: 'Customer Unique ID',
		minimum: 1,
		example: 4831920
	}),
	FulfilStatus: t.String({
		description: 'Fulfilment status of the rewards (e.g., redeemed, pending)',
		minLength: 1,
		example: 'redeemed'
	}),
	Exclude: t.Integer({
		description: 'Filter based on the user assigned in the admin',
		minimum: 0,
		example: 1
	}),
	Page: t.Optional(
		t.Number({
			description: 'Pagination page number, starting from 1.',
			minimum: 1,
			example: 1
		})
	)
});

export type GetMyRewardsReqBody = Static<typeof getMyRewardsReqBodySchema>;

const PaginationDetailsSchema = t.Object({
	CurrentPage: t.Optional(
		t.String({
			description: 'Current page number in the pagination',
			example: '1'
		})
	),
	FirstPageUrl: t.Optional(
		t.String({
			description: 'URL to the first page',
			example:
				'https://unifi-rewards-uat.tm.com.my/api/campaign/v1/weras/get-my-rewards?page=1'
		})
	),
	From: t.Optional(
		t.String({
			description: 'Start index of the data on this page',
			example: '1'
		})
	),
	LastPage: t.Optional(
		t.String({ description: 'Last page number', example: '1' })
	),
	LastPageUrl: t.Optional(
		t.String({
			description: 'URL to the last page',
			example:
				'https://unifi-rewards-uat.tm.com.my/api/campaign/v1/weras/get-my-rewards?page=1'
		})
	),
	NextPageUrl: t.Optional(
		t.String({ description: 'URL to the next page', example: null })
	),
	Path: t.Optional(
		t.String({
			description: 'Base path for pagination',
			example:
				'https://unifi-rewards-uat.tm.com.my/api/campaign/v1/weras/get-my-rewards'
		})
	),
	PerPage: t.Optional(
		t.String({ description: 'Number of items per page', example: '50' })
	),
	PrevPageUrl: t.Optional(
		t.String({ description: 'URL to the previous page', example: null })
	),
	To: t.Optional(
		t.String({
			description: 'End index of the data on this page',
			example: '7'
		})
	),
	Total: t.Optional(
		t.String({ description: 'Total number of items', example: '7' })
	)
});

const RewardItemSchema = t.Object({
	Id: t.Integer({ description: 'Unique ID of the reward item', example: 133 }),
	Name: t.String({
		description: 'Name of the reward item',
		example: 'RM10 Setel E-Voucher'
	}),
	Description: t.Optional(
		t.String({
			description: 'Detailed description of the reward item',
			example:
				'<p>Enjoy seamless fueling with RM10 Setel E-Voucher. Redeem now!</p>'
		})
	),
	Category: t.Optional(
		t.Integer({ description: 'Category ID of the reward', example: 1 })
	),
	Brand: t.Optional(
		t.Integer({
			description: 'Brand ID associated with the reward',
			example: 9
		})
	),
	InventoryChannel: t.Optional(
		t.String({
			description: 'Inventory channel of the reward item',
			example: 'OMG'
		})
	),
	RedemptionFlow: t.Optional(
		t.String({ description: 'Redemption flow type', example: 'QR' })
	),
	Status: t.Optional(
		t.String({
			description: 'Current status of the reward item',
			example: 'redeemed'
		})
	),
	Segment: t.Optional(
		t.String({
			description: 'User segment eligible for the reward',
			example: 'residential'
		})
	),
	AdditionalDescription: t.Optional(
		t.String({ description: 'Additional reward notes or info', example: null })
	),
	RmValue: t.Optional(
		t.Number({ description: 'Value of the reward in RM', example: 10 })
	),
	PointValue: t.Optional(
		t.Integer({
			description: 'Points required to redeem the item',
			example: 1000
		})
	),
	CodeValue: t.Optional(
		t.String({
			description: 'Code value used for voucher/redeem',
			example: 'jxf86u8g2y'
		})
	),
	StartDate: t.Optional(
		t.String({
			description: 'Start date of reward validity',
			example: '2024-02-21 12:00:00'
		})
	),
	EndDate: t.Optional(
		t.String({
			description: 'End date of reward validity',
			example: '2024-12-31 12:00:00'
		})
	),
	Image: t.Optional(
		t.String({
			description: 'URL of the reward image',
			example: '/item-images/rm10-setel-e-voucher-1743217306.jpg'
		})
	),
	Csvfile: t.Optional(
		t.String({ description: 'URL to CSV if applicable', example: null })
	),
	FastTrack: t.Optional(
		t.Integer({
			description: 'Indicates if reward is fast-tracked',
			example: 0
		})
	),
	Tnc: t.Optional(
		t.String({
			description: 'Terms and conditions for reward',
			example:
				'<p>1. This voucher is redeemable at participating outlets only. 2. Voucher is valid until the expiry date stated.</p>'
		})
	),
	DownloadUrl: t.Optional(
		t.String({
			description: 'URL to download related materials',
			example: null
		})
	),
	Highlighted: t.Optional(
		t.Integer({ description: 'Flag to highlight reward item', example: 0 })
	),
	Exclude: t.Optional(
		t.Integer({
			description: 'Flag to exclude from certain listings',
			example: 0
		})
	),
	Barcode: t.Optional(
		t.String({ description: 'Barcode value', example: null })
	),
	Qrcode: t.Optional(t.String({ description: 'QR code value', example: null })),
	CreatedBy: t.Optional(
		t.Integer({ description: 'User who created the record', example: 1 })
	),
	ModifiedBy: t.Optional(
		t.Integer({ description: 'User who last modified the record', example: 1 })
	),
	DeletedBy: t.Optional(
		t.Integer({ description: 'User who deleted the record', example: null })
	),
	CreatedAt: t.Optional(
		t.String({
			description: 'Timestamp when reward was created',
			example: '2024-03-25 15:41:46'
		})
	),
	UpdatedAt: t.Optional(
		t.String({
			description: 'Timestamp when reward was last updated',
			example: '2024-03-28 16:57:38'
		})
	),
	DeletedAt: t.Optional(
		t.String({
			description: 'Timestamp when reward was deleted',
			example: null
		})
	),
	ExpiryDate: t.Optional(
		t.String({
			description: 'Expiry date of the reward',
			example: '2024-12-31 12:00:00'
		})
	),
	RedemptionCreatedAt: t.Optional(
		t.String({
			description: 'Redemption creation timestamp',
			example: '2024-03-30 10:22:42'
		})
	),
	RedemptionId: t.Optional(
		t.Number({
			description: 'Redemption ID associated with the reward',
			example: 506
		})
	),
	UseDate: t.Optional(
		t.String({ description: 'Date when the reward was used', example: null })
	)
});

const ExpiringVoucherSchema = t.Object({
	Id: t.Number({ description: 'Voucher ID', example: 6660425 }),
	ItemId: t.Number({ description: 'Associated reward item ID', example: 3725 }),
	RedemptionId: t.Number({
		description: 'Redemption ID of the expiring voucher',
		example: 5444729
	}),
	ExpiryDate: t.String({
		description: 'Expiry date of the voucher',
		example: '2025-08-14 00:00:00'
	}),
	ItemName: t.String({
		description: 'Name of the reward item',
		example: '15% OFF ZUS handcrafted drinks'
	})
});

export const GetMyRewardsResDataSchema = t.Object({
	PaginationDetails: PaginationDetailsSchema,
	Data: t.Array(RewardItemSchema),
	ExpiringVouchers: t.Array(ExpiringVoucherSchema)
});

export const getMyRewardsResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: GetMyRewardsResDataSchema
});

export type GetMyRewardsRes = Static<typeof getMyRewardsResSchema>;
// Schema for get-my-rewards END here

// Schema for get-transactions START here
export const getTransactionsReqBodySchema = t.Object({
	CustomerId: t.Number({
		description: 'Unique identifier for the customer',
		minimum: 1,
		example: 98317456
	}),
	Page: t.Integer({
		description: 'Used for server-side pagination. Specify the page number.',
		minimum: 1,
		example: 1
	}),
	PerPage: t.Integer({
		description:
			'Used for server-side pagination. Specify the number of records returned per page.',
		minimum: 1,
		example: 50
	}),
	Type: t.Optional(
		t.Enum(
			{
				Earning: 'earning',
				Redeemed: 'redeemed',
				Expired: 'expired',
				All: 'all'
			},
			{
				description:
					'Used to specify the type of points to be retrieved. Options: earning, redeemed, expired, all. Default value: all.',
				example: 'all'
			}
		)
	),
	FromDate: t.Optional(
		t.String({
			description:
				'Starting date of transactions in YYYY-MM-DD format. Default: 6 months from the current date.',
			pattern: '^\\d{4}-\\d{2}-\\d{2}$',
			example: '2024-01-01',
			default: new Date(new Date().setMonth(new Date().getMonth() - 6))
				.toISOString()
				.split('T')[0] // Calculated default value (6 months from today)
		})
	),
	ToDate: t.Optional(
		t.String({
			description:
				'Ending date of transactions in YYYY-MM-DD format. Default: Current date.',
			pattern: '^\\d{4}-\\d{2}-\\d{2}$',
			example: '2024-12-31',
			default: new Date().toISOString().split('T')[0] // Calculated default value (today)
		})
	)
});

export type GetTransactionsReqBody = Static<
	typeof getTransactionsReqBodySchema
>;

export const getTransactionsResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		CurrentPage: t.String({
			description: 'Current page of the transaction list',
			example: '1'
		}),
		FirstPageUrl: t.String({
			description: 'URL of the first page',
			example:
				'https://rewards.unifi.com.my/api/get-transaction/2450246/1/50/redeemed?page=1'
		}),
		From: t.String({
			description: 'Starting record number in this page',
			example: '1'
		}),
		LastPage: t.String({ description: 'Last page number', example: '1' }),
		LastPageUrl: t.String({
			description: 'URL of the last page',
			example:
				'https://rewards.unifi.com.my/api/get-transaction/2450246/1/50/redeemed?page=1'
		}),
		Path: t.String({
			description: 'Base path of the pagination',
			example:
				'https://rewards.unifi.com.my/api/get-transaction/2450246/1/50/redeemed'
		}),
		PerPage: t.String({
			description: 'Number of items per page',
			example: '50'
		}),
		PrevPageUrl: t.Nullable(
			t.String({
				description: 'URL of the previous page',
				example:
					'https://rewards.unifi.com.my/api/get-transaction/2450246/1/50/redeemed?page=1'
			})
		),
		To: t.String({
			description: 'Ending record number in this page',
			example: '1'
		}),
		Total: t.String({ description: 'Total number of records', example: '1' }),
		Data: t.Array(
			t.Object({
				Name: t.String({
					description: 'Transaction item name',
					example: 'RM10 Setel E-Voucher'
				}),
				Description: t.String({
					description: 'Transaction item description',
					example: 'Balik Kampung Raya with Unifi Rewards'
				}),
				FulfilmentId: t.Number({
					description: 'Fulfilment ID for the transaction',
					example: 6688568
				}),
				RedemptionId: t.Number({
					description: 'Redemption ID for the transaction',
					example: 5473502
				}),
				Status: t.String({
					description: 'Transaction status',
					example: 'active'
				}),
				CreatedAt: t.String({
					description: 'Transaction creation date',
					example: '2025-03-29 12:30:00'
				}),
				ExpiryAt: t.String({
					description: 'Transaction expiry date',
					example: '2025-12-31 23:59:59'
				}),
				Payment: t.Nullable(
					t.String({
						description: 'Payment details (if any)',
						example: 'Paid with Points'
					})
				),
				Fulfilment: t.Object({
					Id: t.Number({ example: 6688568 }),
					RedemptionId: t.Number({ example: 5473502 }),
					EventId: t.Nullable(t.String({ example: 'EVT12345' })),
					ItemId: t.Number({ example: 4109 }),
					ItemQuantity: t.Number({ example: 1 }),
					PointsRedeemed: t.Number({ example: 0 }),
					VoucherCodeId: t.Number({ example: 1105325 }),
					Status: t.String({ example: 'redeemed' }),
					Address1: t.Nullable(t.String({ example: '123 Jalan SS2' })),
					Address2: t.Nullable(t.String({ example: 'Taman Bahagia' })),
					Address3: t.Nullable(t.String({ example: 'Block B' })),
					City: t.Nullable(t.String({ example: 'Shah Alam' })),
					State: t.Nullable(t.String({ example: 'Selangor' })),
					Country: t.Nullable(t.String({ example: 'Malaysia' })),
					PostalCode: t.Nullable(t.String({ example: '40000' })),
					ContactNumber: t.Nullable(t.String({ example: '0123456789' })),
					ShippedAt: t.Nullable(t.String({ example: '2025-03-30 09:00:00' })),
					ShippedBy: t.Nullable(t.String({ example: 'J&T Express' })),
					TrackingNumber: t.Nullable(t.String({ example: 'JT123456789' })),
					TrackingUrl: t.Nullable(
						t.String({ example: 'https://tracking.jtexpress.my/JT123456789' })
					),
					CreatedBy: t.Number({ example: 1816 }),
					ModifiedBy: t.Number({ example: 1816 }),
					CreatedAt: t.String({ example: '2025-03-29 12:30:11' }),
					UpdatedAt: t.String({ example: '2025-03-29 12:30:11' }),
					Item: t.Object({
						Id: t.Number({ example: 4109 }),
						Name: t.String({ example: 'RM10 Setel E-Voucher' }),
						Description: t.String({
							example: 'Balik Kampung Raya with Unifi Rewards'
						}),
						Category: t.Number({ example: 52 }),
						Brand: t.Number({ example: 66 }),
						InventoryChannel: t.String({ example: 'online' }),
						RedemptionFlow: t.String({ example: 'default' }),
						RedemptionScope: t.String({ example: 'home' }),
						GameFlag: t.Number({ example: 0 }),
						Status: t.String({ example: 'active' }),
						Segment: t.String({ example: 'both' }),
						AdditionalDescription: t.Nullable(
							t.String({ example: 'Bonus cashback for Raya season' })
						),
						RmValue: t.Nullable(t.Number({ example: 10 })),
						PointValue: t.Nullable(t.Number({ example: 1000 })),
						CodeValue: t.Nullable(t.String({ example: 'SETELRAYA10' })),
						StartDate: t.String({ example: '2025-03-29 00:00:00' }),
						EndDate: t.String({ example: '2025-12-31 00:00:00' }),
						ExpiryDate: t.Nullable(
							t.String({ example: '2025-12-31 00:00:00' })
						),
						Image: t.String({
							example: '/item-images/rm10-setel-e-voucher-1743217306.jpg'
						}),
						Csvfile: t.Nullable(
							t.String({ example: '/csv/rm10-setel-e-voucher.csv' })
						),
						FastTrack: t.Number({ example: 0 }),
						Tnc: t.String({ example: '<p>Terms and Conditions apply</p>' }),
						DownloadUrl: t.Nullable(
							t.String({ example: 'https://setel.my/download' })
						),
						Highlighted: t.Number({ example: 1 }),
						Exclude: t.Number({ example: 0 }),
						Barcode: t.String({ example: 'ABC123' }),
						Qrcode: t.String({ example: 'XYZ456' }),
						Display: t.String({ example: 'both' }),
						CreatedBy: t.Number({ example: 1482 }),
						ModifiedBy: t.Number({ example: 1482 }),
						DeletedBy: t.Nullable(t.Number({ example: 0 })),
						CreatedAt: t.String({ example: '2025-03-29 11:01:46' }),
						UpdatedAt: t.String({ example: '2025-03-29 11:03:32' }),
						DeletedAt: t.Nullable(t.String({ example: null })),
						BarcodeLink: t.Nullable(
							t.String({ example: 'https://barcode.link' })
						),
						QrCodeLink: t.Nullable(t.String({ example: 'https://qrcode.link' }))
					})
				})
			})
		)
	})
});

export type GetTransactionsRes = Static<typeof getTransactionsResSchema>;
// Schema for get-transactions END here

// Schema for get-online-catalogue START here
export const getOnlineCatalogueResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Category: t.Array(
				t.Object({
					Id: t.Number({
						description: 'Unique identifier for the category',
						example: 71
					}),
					Name: t.String({
						description: 'Name of the category',
						example: 'Shopping'
					})
				})
			)
		})
	},
	{
		description: 'Returns a list of available online catalogue categories.'
	}
);

export type GetOnlineCatalogueRes = Static<typeof getOnlineCatalogueResSchema>;
// Schema for get-online-catalogue END here

// Schema for update-rewards-flag START here
export const updateRewardsFlagReqBodySchema = t.Object({
	VoucherFlag: t.Enum(
		{
			Use: 'use',
			Archive: 'archive',
			Delete: 'delete'
		},
		{
			description:
				"Flag to update the voucher status. Options: 'use' (to update this voucher to used) or 'archive' (to remove this voucher from the user's 'My Rewards' screen) or 'delete' is for when voucher is deleted, it is available to be redeemed again.",
			example: 'use'
		}
	),
	RedemptionId: t.Number({
		description: 'Unique identifier for the redemption',
		minimum: 1,
		example: 7459238
	})
});

export type UpdateRewardsFlagReqBody = Static<
	typeof updateRewardsFlagReqBodySchema
>;

export const updateRewardsFlagResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Message: t.String({
				description: 'Response message indicating the result of the update',
				example: 'Success'
			})
		})
	},
	{
		description:
			'Returns confirmation of the successful update of the reward flag.'
	}
);

export type UpdateRewardsFlagRes = Static<typeof updateRewardsFlagResSchema>;
// Schema for update-rewards-flag END here

// Schema for get-report-eligibility START here
export const getReportEligibilityReqBodySchema = t.Object({
	CustomerId: t.Number({
		description: 'Unique identifier for the customer',
		minimum: 1,
		example: 98317456
	})
});

export type GetReportEligibilityReqBody = Static<
	typeof getReportEligibilityReqBodySchema
>;

export const getReportEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Array(
			t.Object({
				Points: t.Object({
					TotalAvailable: t.Number({
						description: 'Total points available',
						example: 244
					}),
					TotalEarned: t.Number({
						description: 'Total points earned',
						example: 2144
					}),
					TotalUsed: t.Number({
						description: 'Total points used',
						example: 1900
					}),
					RedeemedInDateRange: t.Number({
						description: 'Points redeemed within the specified date range',
						example: 1900
					})
				}),
				RedeemedPointsInDateRangeRM: t.Number({
					description:
						'Monetary value (RM) of points redeemed in the specified date range',
					example: 19
				}),
				BillRebatePoints: t.Number({
					description: 'Points used for bill rebates',
					example: 200
				}),
				BillRebateRM: t.Number({
					description: 'Monetary value (RM) of bill rebates',
					example: 2
				}),
				VoucherRedeemedCount: t.Number({
					description: 'Number of vouchers redeemed',
					example: 1
				}),
				VoucherSaveAmountRM: t.Number({
					description: 'Total monetary savings (RM) from redeemed vouchers',
					example: 0
				}),
				DonationRedeemedPoints: t.Number({
					description: 'Points donated',
					example: 0
				}),
				DonationRedeemedRM: t.Number({
					description: 'Monetary value (RM) of donated points',
					example: 0
				})
			})
		)
	},
	{
		description:
			'Returns customer reward eligibility details including available points, redemption history, and bill rebate eligibility.'
	}
);

export type GetReportEligibilityRes = Static<
	typeof getReportEligibilityResSchema
>;
// Schema for get-report-eligibility END here
