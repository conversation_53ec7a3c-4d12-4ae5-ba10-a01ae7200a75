import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const netflixChangePlanReqSchema = t.Object({
	CustName: t.String({
		description: 'Customer Name',
		example: 'John Doe'
	}),
	CustEmail: t.String({
		description: 'Customer Email Address',
		example: 'John<PERSON><EMAIL>'
	}),
	AccountType: t.Enum(
		{
			Broadband: 'Broadband',
			Mobile: 'Mobile'
		},
		{
			description: 'Account type (Broadband or Mobile)',
			example: 'Broadband'
		}
	),
	AccountId: t.String({
		description: 'User account ID',
		example: 'johndoe@unifi'
	}),
	NewTmBundleId: t.String({
		description: 'New Netflix plan bundle ID',
		example: 'NB300B'
	}),
	OttPlanId: t.String({
		description: 'TV Pack Plan ID to identify the user’s plan',
		example: 'P39'
	}),
	IptvId: t.String({
		description: 'IPTV ID associated with the TV Pack Plan',
		example: 'johndoe@iptv'
	})
});

export type NetflixChangePlanReqType = Static<
	typeof netflixChangePlanReqSchema
>;

export const netflixChangePlanResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			OrderRefNo: t.String({
				description:
					'Unique order reference number for tracking the Netflix plan change request.'
			})
		})
	},
	{
		description:
			'Returns an order reference number for the Netflix change plan request.'
	}
);

export type NetflixChangePlanRes = Static<typeof netflixChangePlanResSchema>;
