import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import IdentityHelper from '../../../identity/v1/helpers/IdentityHelper';
import type {
	IdentityRes,
	SelectIdentityApi
} from '../../../identity/v1/schemas/api/identity';
import ProfileHelper from '../../../profile/v1/helpers/ProfileHelper';
import type { ProfileData } from '../../../profile/v1/schemas/api/profile';
//import { getCache, setCache } from '../../../../config/cache';
import type { DeleteSessionRes } from '../schemas/api/session';
import { type SelectSession, sessionDbSchema } from '../schemas/models/session';
import CheckSession from './CheckSession';

class Session {
	private db: NodePgDatabase;
	private integrationId: string;
	private checkSession: CheckSession;
	private profileHelper: ProfileHelper;
	private identityHelper: IdentityHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.checkSession = new CheckSession(integrationId);
		this.profileHelper = new ProfileHelper(integrationId);
		this.identityHelper = new IdentityHelper(integrationId);
	}

	async validateSession(userId: string): Promise<IdentityRes> {
		const profileRes: ProfileData =
			await this.profileHelper.getUserProfileByUserId(userId);

		// const sessionId = bearer.replace(/Bearer\s+/i, '');
		// const cacheName = `getValidateSession-${sessionId}`;
		// const cache = await getCache(cacheName);
		// if (cache) {
		// 	return {
		// 		Success: true,
		// 		Code: StatusCodeEnum.OK,
		// 		IntegrationId: this.integrationId,
		// 		Response: JSON.parse(cache) as SelectIdentityApi
		// 	};
		// }

		const res: SelectIdentityApi =
			await this.identityHelper.getUserInfoDetails(profileRes);

		//* Set cache for 24 hrs
		// await setCache(cacheName, JSON.stringify(res), 86400);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: res
		};
	}

	async deleteSession(sessionId: string): Promise<DeleteSessionRes> {
		const id = sessionId.replace(/Bearer\s+/i, ''); // Because the string still came with Bearer at the front despite seperation
		const session: SelectSession[] = await this.db
			.select()
			.from(sessionDbSchema)
			.where(eq(sessionDbSchema.Id, id))
			.limit(1);

		if (session.length === 0) {
			throw new UE_ERROR('Session Not Found', StatusCodeEnum.NOT_FOUND_ERROR, {
				integrationId: this.integrationId,
				response: null
			});
		}

		try {
			await this.checkSession.invalidateSession(session[0].Id);
		} catch (error) {
			throw new UE_ERROR(
				'Session Deletion Failed',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId, response: error }
			);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			Response: {
				Action: 'DELETE',
				Message: 'Session successfully deleted'
			}
		};
	}
}
export default Session;
