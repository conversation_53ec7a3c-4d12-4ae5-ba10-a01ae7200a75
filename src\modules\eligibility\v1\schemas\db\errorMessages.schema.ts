import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const errorMessagesTableSchema = pgTable('error_messages', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Code: text('code').notNull(),
	Title: text('title').notNull(),
	Message: text('message').notNull(),
	CTAButtonText: text('cta_button_text'),
	CTAUrl: text('cta_url'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectErrorMessages = typeof errorMessagesTableSchema.$inferSelect;
