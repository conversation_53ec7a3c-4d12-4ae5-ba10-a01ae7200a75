import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const alertServiceLovTableSchema = pgTable('alert_service_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Name: text('name').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAlertServiceLov =
	typeof alertServiceLovTableSchema.$inferSelect;
