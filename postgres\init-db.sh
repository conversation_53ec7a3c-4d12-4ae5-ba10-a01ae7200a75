#!/bin/bash
set -e

until pg_isready -h postgres -p 5432 -U "$POSTGRES_USER"; do
  echo "Waiting for PostgreSQL to start..."
  sleep 2
done

# Set the PGPASSWORD environment variable for authentication
export PGPASSWORD="$POSTGRES_PASSWORD"
  
# Check if the database exists
DB_EXISTS=$(psql -h postgres -U "$POSTGRES_USER" -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME'")

if [[ "$DB_EXISTS" != "1" ]]; then
  echo "Database $DB_NAME does not exist. Proceeding with initialization..."
  
  # Check if the user (role) exists
  USER_EXISTS=$(psql -h postgres -U "$POSTGRES_USER" -tAc "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER'")
  
  if [[ "$USER_EXISTS" != "1" ]]; then
    echo "Creating user $DB_USER..."
    psql -h postgres -U "$POSTGRES_USER" -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';"
  else
    echo "User $DB_USER already exists, skipping user creation."
  fi
  
  # Create the database (this must be outside of a transaction)
  psql -h postgres -U "$POSTGRES_USER" -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"

  # Grant privileges
  psql -h postgres -U "$POSTGRES_USER" -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
else
  echo "Database $DB_NAME already exists, skipping initialization."
fi

# Unset the PGPASSWORD after use (optional)
unset PGPASSWORD

# Exit with success status
exit 0
