import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { validateApiKey } from '../auth';
import { UE_ERROR } from '../error';
import {
	identityRoutes,
	publicIdentityRoutes
} from './identity/v1/controllers/identity';
import profileRoutes from './profile/v1/controllers/profile';
import { sessionRoutes } from './session/v1/controllers/session';
import CheckSession from './session/v1/services/CheckSession';
import { smeRoutes } from './sme/v1/controllers/sme';
import { publicTacRoutes, tacRoutes } from './tac/v1/controllers/tac';
import {
	authorizedHeaderSchema,
	xApiKeyHeaderSchema
} from './util/schemas/headers';

const uaidRoutes = new Elysia({ prefix: '/uaid' })
	.guard(
		{
			headers: authorizedHeaderSchema,
			async beforeHandle({ headers: { authorization } }) {
				if (authorization) {
					await new CheckSession(randomUUID()).validateSession(authorization);
				} else {
					throw new UE_ERROR(
						'Invalid token! You are not authorized!',
						StatusCodeEnum.UNAUTHORIZED_ERROR
					);
				}
			}
		},
		app =>
			app
				.use(identityRoutes)
				.use(profileRoutes)
				.use(smeRoutes)
				.use(sessionRoutes)
				.use(tacRoutes)
	)
	.guard(
		{
			headers: xApiKeyHeaderSchema,
			beforeHandle({ headers: { 'x-api-key': xApiKey } }) {
				if (xApiKey) {
					validateApiKey(xApiKey);
				} else {
					throw new UE_ERROR(
						'Invalid key! You are not authorized!',
						StatusCodeEnum.UNAUTHORIZED_ERROR
					);
				}
			}
		},
		app => app.use(publicIdentityRoutes).use(publicTacRoutes)
	);

export default uaidRoutes;
