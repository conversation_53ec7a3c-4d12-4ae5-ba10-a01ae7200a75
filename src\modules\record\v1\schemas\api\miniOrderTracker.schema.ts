import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const validateOrderIdReqSchema = t.Object({
	OrderId: t.String(),
	IdType: t.String(),
	CustomerID: t.String()
});

export type ValidateOrderIdReq = Static<typeof validateOrderIdReqSchema>;

export const validateOrderIdResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		SystemName: t.String({ examples: ['NOVA'] }),
		OrderStatus: t.String({ examples: ['Processing'] }),
		InstallationActStat: t.String({ examples: ['Scheduled'] }),
		InstallationActTime: t.String({ examples: ['12/13/2019 10:00:00'] }),
		SBLOrderID: t.String({ examples: ['1-1D63B42'] }),
		OrderNumber: t.String({ examples: ['1-132075126789'] }),
		OrderType: t.String({ examples: ['New Install'] }),
		OrderPlanName: t.String({ examples: ['Unifi 100Mbps'] }),
		ServiceID: t.String({ examples: ['test@unifi'] }),
		CreatedDate: t.String()
	})
});

export type ValidateOrderIdRes = Static<typeof validateOrderIdResSchema>;
