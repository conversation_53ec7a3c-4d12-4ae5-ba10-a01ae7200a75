import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CheckOpenTransferReq,
	Wso2CheckOpenTransferRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2OpenTransferReq.schema';
import type { Wso2LightWeightBillingDetailsRes } from '../../../../integration/wso2/user/schemas/api/wso2LightweightBillingDetails.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type {
	SRTerminationReq,
	SRTerminationRes
} from '../schemas/api/sr.schema';

class ServiceRequestEligibility {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async getSREligibility(req: SRTerminationReq): Promise<SRTerminationRes> {
		const decyrptedBillAccNo: string = await decrypt(req.EncryptedBillAccNo);
		const res: SRTerminationRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsAllowTermination: true,
				LatestReceivedPaymentDate: '',
				BillAmount: '',
				BillDueDate: '',
				BillNo: ''
			}
		};

		const wso2SAReq: Wso2ServiceAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue,
			BillingAccountNo: decyrptedBillAccNo,
			SystemName: req.SystemName
		};

		// eligibility for nova
		if (req.SystemName === SystemNameEnum.NOVA) {
			const wso2SARes: Wso2ServiceAccountRes =
				(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
					wso2SAReq,
					LightweightFlagEnum.NO
				)) as Wso2ServiceAccountRes;

			let tmServiceInstanceId = '';

			for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
				if (
					sa?.['TmCutAssetMgmt-ServiceMeterIntegration']?.TMServiceInstanceId
				) {
					tmServiceInstanceId =
						sa['TmCutAssetMgmt-ServiceMeterIntegration'].TMServiceInstanceId;
				} else {
					throw new UE_ERROR(
						'TMServiceInstanceId is not found',
						StatusCodeEnum.UNPROCESSABLE_ENTITY
					);
				}
			}

			const wso2OpenTransferReq: Wso2CheckOpenTransferReq = {
				CheckOpenTransferRequest: {
					HSNumber: tmServiceInstanceId
				}
			};
			const wso2OpenTransferRes: Wso2CheckOpenTransferRes =
				await this.mwIntegration.Wso2EligibilityIntegration.getWso2OpenTransferReq(
					wso2OpenTransferReq
				);

			const cot = wso2OpenTransferRes.Response.CheckOpenTransferResponse?.at(0);

			if (
				cot &&
				cot.TerminateUNIFI === 'N' &&
				(cot.Status === 'In Progress' ||
					cot.Status === 'Pending' ||
					cot.Status === 'Approved')
			) {
				res.Response.IsAllowTermination = false;
			}
		}

		// check unpaid balance
		const wso2BARes: Wso2LightWeightBillingDetailsRes =
			(await this.mwIntegration.Wso2UserIntegration.getWso2LightWeightBillingDetails(
				wso2SAReq
			)) as Wso2LightWeightBillingDetailsRes;

		if (wso2BARes?.Response) {
			res.Response.LatestReceivedPaymentDate =
				wso2BARes.Response.PaymentHistory?.at(0)?.TransactionDatetime ?? '';

			res.Response.BillAmount =
				wso2BARes.Response.LatestDueUnbilledUsage?.OutstandingAmount ?? '';

			res.Response.BillDueDate =
				wso2BARes.Response.BillingHistory?.at(0)?.BillDueDate ?? '';

			res.Response.BillNo =
				wso2BARes.Response.BillingHistory?.at(0)?.BillNo ?? '';

			if (Number.parseFloat(res.Response.BillAmount) > 0) {
				res.Response.IsAllowTermination = false;
			}
		} else {
			res.Response.IsAllowTermination = false;
		}

		return res;
	}
}

export default ServiceRequestEligibility;
