import { type Static, t } from 'elysia';

export const omgGetOttActivationAlaCarteUrlReqSchema = t.Object({
	accountType: t.String({
		minLength: 1
	}),
	accountId: t.String({
		minLength: 1
	}),
	ottMerchantId: t.Integer({
		minimum: 1
	}),
	tokenRefNo: t.String({
		minLength: 1
	})
});

export type OmgGetOttActivationAlaCarteUrlReq = Static<
	typeof omgGetOttActivationAlaCarteUrlReqSchema
>;

export const omgGetOttActivationAlaCarteUrlResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String(),
	tokenURL: t.String()
});

export type OmgGetOttActivationAlaCarteUrlRes = Static<
	typeof omgGetOttActivationAlaCarteUrlResSchema
>;

export const omgGetOttActivationBundleUrlReqSchema = t.Object({
	accountType: t.String({
		minLength: 1
	}),
	accountId: t.String({
		minLength: 1
	}),
	ottMerchantId: t.Integer({
		minimum: 1
	}),
	txnRefNo: t.String({
		minLength: 1
	}),
	ottPlanId: t.String({
		minLength: 1
	}),
	ottProductId: t.String({
		minLength: 1
	})
});

export type OmgGetOttActivationBundleUrlReq = Static<
	typeof omgGetOttActivationBundleUrlReqSchema
>;

export const omgGetOttActivationBundleUrlResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String(),
	bundleTokenURL: t.String()
});

export type OmgGetOttActivationBundleUrlRes = Static<
	typeof omgGetOttActivationBundleUrlResSchema
>;
