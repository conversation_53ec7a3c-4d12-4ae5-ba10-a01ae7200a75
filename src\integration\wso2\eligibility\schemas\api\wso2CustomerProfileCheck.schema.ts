import { type Static, t } from 'elysia';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';

const wso2CustomerProfileCheckReqSchema = t.Object({
	CustomerProfileCheckRequest: t.Object({
		SystemName: t.String({ enum: Object.values(SystemNameEnum) }),
		IdType: t.String(),
		IdValue: t.String()
	})
});

export type Wso2CustomerProfileCheckReq = Static<
	typeof wso2CustomerProfileCheckReqSchema
>;

export const wso2CustomerProfileCheckResSchema = t.Object({
	Status: t.Optional(
		t.Object({
			Type: t.MaybeEmpty(t.String()),
			Code: t.MaybeEmpty(t.String()),
			Message: t.MaybeEmpty(t.String())
		})
	),
	Response: t.Object({
		CustomerProfileCheckResponse: t.Nullable(
			t.Object({
				CustomerAccount: t.Nullable(
					t.Object({
						AccountId: t.String(),
						ContactId: t.String(),
						BillingAccounts: t.Object({
							BillingAccountId: t.Array(t.String())
						})
					})
				),
				CustomerCheckOkFlag: t.MaybeEmpty(t.String()),
				DelinquentIndicator: t.MaybeEmpty(t.String()),
				FraudIndicator: t.MaybeEmpty(t.String()),
				CreditRatingDate: t.MaybeEmpty(t.String()),
				CreditRating: t.MaybeEmpty(t.String()),
				BlacklistType: t.MaybeEmpty(t.String()),
				DelinquentAmount: t.MaybeEmpty(t.String()),
				CPBR: t.MaybeEmpty(t.String()),
				TMAssetOrderExistFlag: t.MaybeEmpty(t.String()),
				TMSMARTFlag: t.MaybeEmpty(t.String()),
				TMResellerMatchFlag: t.MaybeEmpty(t.String()),
				TMOpenOrderFlag: t.MaybeEmpty(t.String()),
				TMHSBAAddressFlag: t.MaybeEmpty(t.String()),
				TMAddressOnCustomerViolated: t.MaybeEmpty(t.String()),
				TMCustomerOpenOrderViolated: t.MaybeEmpty(t.String()),
				TMPackageOnAddressViolated: t.MaybeEmpty(t.String()),
				TMMaxConsumerPackagesPurchased: t.MaybeEmpty(t.String()),
				TMMaxSMEPackagesPurchased: t.MaybeEmpty(t.String()),
				TMCustomerOnPackageViolated: t.MaybeEmpty(t.String()),
				AdvancePaymentAmount: t.MaybeEmpty(t.String()),
				HasOutstandingAdvancePayment: t.MaybeEmpty(t.String()),
				AdvancePaymentAccounts: t.Array(
					t.Object({
						BillingAccountNumber: t.MaybeEmpty(t.String()),
						AdvancePaymentFlag: t.MaybeEmpty(t.String())
					})
				)
			})
		)
	})
});

export type Wso2CustomerProfileCheckRes = Static<
	typeof wso2CustomerProfileCheckResSchema
>;
