import { type Static, t } from 'elysia';

// Define the redemption_limit
export const redemptionLimitSchema = t.Object({
	quantity: t.Number(),
	quantity_period: t.String(),
	quantity_used: t.Number(),
	quantity_available: t.Number()
});

// Define each promotion item
export const werasGetPromotionListItemSchema = t.Object({
	id: t.Number(),
	name: t.String(),
	description: t.String(),
	category: t.Number(),
	brand: t.Number(),
	inventory_channel: t.String(),
	redemption_flow: t.String(),
	redemption_scope: t.String(),
	game_flag: t.Number(),
	status: t.String(),
	segment: t.String(),
	additional_description: t.Nullable(t.String()),
	rm_value: t.Number(),
	point_value: t.Number(),
	code_value: t.String(),
	start_date: t.String(),
	end_date: t.String(),
	image: t.String(),
	csvfile: t.Nullable(t.String()),
	fast_track: t.Number(),
	tnc: t.String(),
	download_url: t.String(),
	highlighted: t.Number(),
	exclude: t.Number(),
	barcode: t.String(),
	qrcode: t.String(),
	display: t.String(),
	created_by: t.Number(),
	modified_by: t.Number(),
	deleted_by: t.Number(),
	created_at: t.String(),
	updated_at: t.String(),
	deleted_at: t.Nullable(t.String()),
	expiry_date: t.String(),
	customer: t.Number(),
	redemption_limit: redemptionLimitSchema,
	barcode_link: t.Nullable(t.String()),
	qr_code_link: t.Nullable(t.String())
});

// Response data wrapper
export const werasGetPromotionListDataSchema = t.Object({
	error: t.Nullable(t.String()),
	message: t.Nullable(t.String()),
	response: t.Array(werasGetPromotionListItemSchema)
});

// Final full response schema
export const werasGetPromotionListResSchema = t.Object({
	status: t.Boolean(),
	code: t.Number(),
	data: werasGetPromotionListDataSchema
});

export type WerasGetPromotionListItem = Static<
	typeof werasGetPromotionListItemSchema
>;
export type WerasGetPromotionListRes = Static<
	typeof werasGetPromotionListResSchema
>;
