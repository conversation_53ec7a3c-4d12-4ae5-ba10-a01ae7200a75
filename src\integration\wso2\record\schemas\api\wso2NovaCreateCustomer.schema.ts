import { type Static, t } from 'elysia';

export const wso2NovaCreateCustomerSchema = t.Object({
	ListOfTmEaiCreateCustInfoIo: t.Object({
		TmAccountIntegration: t.Array(
			t.Object({
				AccountRowID: t.String({ examples: [''] }),
				ListOfTmContactIntegration: t.Object({
					TmContactIntegration: t.Array(
						t.Object({
							ContactRowID: t.String({ examples: [''] }),
							ContactMobilePhone: t.String({ examples: ['**********'] }),
							ContactEmailAddress: t.String({ examples: [''] }),
							ContactHomePhone: t.String({ examples: [''] }),
							ContactName: t.String({ examples: ['Ryu <PERSON>'] }),
							ContactPreferredContactMethod: t.String({ examples: ['SMS'] }),
							ContactOfficePhone: t.Optional(t.String())
						})
					)
				})
			})
		)
	})
});

export type Wso2NovaCreateCustomer = Static<
	typeof wso2NovaCreateCustomerSchema
>;
