# Check for merge commits in the current branch
# Get default branch name with trimmed whitespace
DEFAULT_BRANCH=$(git remote show origin | grep 'HEAD branch' | cut -d':' -f2 | tr -d '[:space:]')
echo "Default branch: '$DEFAULT_BRANCH'"

# Count commits with more than one parent
MERGE_COMMITS=$(git log --format="%p" HEAD ^"origin/${DEFAULT_BRANCH}" | awk 'NF>1' | wc -l)
echo "Total merge commits: $MERGE_COMMITS"

if [ "$MERGE_COMMITS" -eq 0 ]; then
    echo "No merge commits detected."
else
    echo "This branch contains $MERGE_COMMITS merge commit(s)."
    exit 1
fi

# Fetch the latest changes from the remote
git fetch origin

CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Check if the current branch is behind the main branch
if git rev-list --count "${CURRENT_BRANCH}..origin/main" | grep -q '^[1-9][0-9]*$'; then
  echo "Your branch is behind 'main'. Please update your branch before committing."
  exit 1 # Prevent the commit
else
  echo "Your branch is up to date with 'main'."
fi

# Clean up, update, and reinstall dependencies
bun precleanup

# Run dependency check
bun depcheck

# Run lint check and auto fix
bun lint:fix

# Add all modified files to the Git staging area
git add .
