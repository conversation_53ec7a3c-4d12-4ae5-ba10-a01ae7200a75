import { type Static, t } from 'elysia';

export const osesPaymentItemsSchema = t.Object({
	billingAccountNoLabel: t.String(),
	billingAccountNo: t.String(),
	amount: t.String()
});

export type OsesPaymentItems = Static<typeof osesPaymentItemsSchema>;

export const osesReceiptEmailTemplateReqSchema = t.Object({
	name: t.String(),
	receiptId: t.String(),
	date: t.String(),
	method: t.String(),
	paymentItems: t.Optional(t.Array(osesPaymentItemsSchema)),
	amount: t.String()
});

export type OsesReceiptEmailTemplateReq = Static<
	typeof osesReceiptEmailTemplateReqSchema
>;
