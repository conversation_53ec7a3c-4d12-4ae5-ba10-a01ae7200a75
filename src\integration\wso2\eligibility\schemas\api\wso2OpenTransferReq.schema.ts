import { type Static, t } from 'elysia';

const wso2OpenTransferReqSchema = t.Object({
	CheckOpenTransferRequest: t.Object({ HSNumber: t.String() })
});

export type Wso2CheckOpenTransferReq = Static<typeof wso2OpenTransferReqSchema>;

export const wso2OpenTransferResSchema = t.Object({
	Status: t.Object({ Type: t.String(), Message: t.String(), Code: t.String() }),
	Response: t.Object({
		CheckOpenTransferResponse: t.Nullable(
			t.Array(
				t.MaybeEmpty(
					t.Object({
						TRId: t.String(),
						HSNumber: t.MaybeEmpty(t.String()),
						Status: t.Maybe<PERSON>mpty(t.String()),
						TRNumber: t.MaybeEmpty(t.String()),
						TeminateHSBA: t.MaybeEmpty(t.String()),
						TerminateUNIFI: t.<PERSON>(t.String())
					})
				)
			)
		)
	})
});

export type Wso2CheckOpenTransferRes = Static<typeof wso2OpenTransferResSchema>;
