import { sql } from 'drizzle-orm';
import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import type { OsesChildTxn } from '../api/osesCreateUrl.schema';

export const osesTxnHistoryTableSchema = pgTable('oses_txn_history', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	MerchantTxnId: text('merchant_txn_id')
		.default(sql`'UNIVERSAL-' || nextval('oses_id_seq')`)
		.notNull(),
	MerchantId: text('merchant_id').notNull(),
	OrderId: text('order_id'),
	PayerEmail: text('payer_email').notNull(),
	PayerName: text('payer_name').notNull(),
	Amount: text('amount').notNull(),
	PaymentType: text('payment_type').notNull(),
	Source: text('source'),
	SourceRedirectUrl: text('source_redirect_url').notNull(),
	AcquirerBank: text('acquirer_bank'),
	AuthId: text('auth_id'),
	BankReference: text('bank_reference'),
	BankResCode: text('bank_res_code'),
	BankResMsg: text('bank_res_msg'),
	BankStatusDesc: text('bank_status_desc'),
	CardNoPartial: text('card_no_partial'),
	CardName: text('card_name'),
	CardType: text('card_type'),
	CurrencyCode: text('currency_code').notNull(),
	ErrCode: text('err_code'),
	ErrDesc: text('err_desc'),
	EUI: text('eui'),
	ExceedHighRisk: text('exceed_high_risk'),
	FraudRiskLevel: text('fraud_risk_level'),
	FraudRiskScore: text('fraud_risk_score'),
	IsBlacklisted: text('is_blacklisted'),
	PaymentMethod: text('payment_method'),
	PortalTxnStatus: text('portal_txn_status').notNull(),
	Description: text('description').notNull(),
	ReturnUrl: text('return_url').notNull(),
	Signature: text('signature').notNull(),
	TotalChildTxn: text('total_child_txn'),
	ChildTxn: json('child_txn').$type<OsesChildTxn>().notNull(),
	TxnDate: text('txn_date'),
	TxnId: text('txn_id'),
	TxnType: text('txn_type'),
	TxnStatus: text('txn_status'),
	UsrCode: text('usr_code'),
	UsrMsg: text('usr_msg'),
	WhitelistCard: text('whitelist_card'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectOsesTxnHistory =
	typeof osesTxnHistoryTableSchema.$inferSelect;

export type InsertOsesTxnHistory =
	typeof osesTxnHistoryTableSchema.$inferInsert;
