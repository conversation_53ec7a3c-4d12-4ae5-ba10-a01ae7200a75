import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import { ProgressStatusEnum } from '../../../../enum/order.enum';
import { LightweightFlagEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	TaaSServiceDetailsReq,
	TaasServiceDetailsRes
} from '../../../../integration/taas/user/schemas/api/taasServiceDetails.schema';
import { taasServiceDetailsTableSchema } from '../../../../integration/taas/user/schemas/db/taasServiceDetails.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getMyTimeZoneDate } from '../../../../shared/common';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';

class BackgroundJob {
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.mwIntegration = new MwIntegration(this.integrationId);
		this.db = getDbInstance();
	}

	async triggerTaasServiceDetailsUpdate(): Promise<BaseResponse> {
		this.getTaasServiceDetails(
			this.idTokenInfo.IdType,
			this.idTokenInfo.IdValue
		);
		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Message:
				'Successfully triggered background job to update service account details from TaaS'
		};
	}

	async getTaasServiceDetails(idType: string, idValue: string): Promise<void> {
		const wso2Res =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				{ idType, idValue },
				LightweightFlagEnum.YES
			);

		const accounts: {
			AccountNo: string;
			BillingAccounts: string[];
		}[] = (wso2Res.Response?.CustomerAccounts ?? [])
			.filter(ca => typeof ca.AccountNo === 'string' && ca.AccountNo)
			.map(ca => ({
				AccountNo: ca.AccountNo as string,
				BillingAccounts: (ca.BillingAccounts ?? [])
					.filter(
						ba => typeof ba.AccountNumber === 'string' && ba.AccountNumber
					)
					.map(ba => ba.AccountNumber as string)
			}));

		for (const { AccountNo, BillingAccounts } of accounts) {
			for (const BillingAccountNo of BillingAccounts) {
				pinoLog.info(
					`Processing account: ${AccountNo}, Billing Account: ${BillingAccountNo}`
				);
				const data = {
					IdType: this.idTokenInfo.IdType,
					IdValue: this.idTokenInfo.IdValue,
					Status: ProgressStatusEnum.INITIAL,
					AccountNo,
					BillingAccountNo,
					Reason: 'Initial Insert'
				};
				await this.db
					.insert(taasServiceDetailsTableSchema)
					.values(data)
					.onConflictDoUpdate({
						target: [
							taasServiceDetailsTableSchema.AccountNo,
							taasServiceDetailsTableSchema.BillingAccountNo
						],
						set: { ...data, UpdatedAt: getMyTimeZoneDate() }
					});
			}
		}

		for (const { AccountNo, BillingAccounts } of accounts) {
			for (const BillingAccountNo of BillingAccounts) {
				pinoLog.info(
					`Processing account: ${AccountNo}, Billing Account: ${BillingAccountNo}`
				);
				const taasReq: TaaSServiceDetailsReq = {
					requestHeader: {
						requestId: 'UE',
						eventName: 'evOTaaSServiceDetailsRetrieveNOVASiebel'
					},
					ListOfTmEaiServiceDetailsRetrieveReq: {
						'TmAssetMgmt-AssetIntegration': {
							AccountNo,
							BillingAccountNumber: BillingAccountNo,
							NetworkElementType: ''
						}
					}
				};

				try {
					const taasRes =
						await this.mwIntegration.TaasUserIntegration.getTaasCustomerServiceAccount(
							taasReq
						);
					await this.updateTaasServiceDetailsInDb(
						AccountNo,
						BillingAccountNo,
						ProgressStatusEnum.COMPLETED,
						'Success',
						taasRes
					);
				} catch (err) {
					await this.updateTaasServiceDetailsInDb(
						AccountNo,
						BillingAccountNo,
						ProgressStatusEnum.FAILED,
						String(err)
					);
				}
			}
		}
	}

	async updateTaasServiceDetailsInDb(
		accountNo: string,
		billingAccountNo: string,
		status: string,
		reason: string,
		resBody?: TaasServiceDetailsRes
	) {
		const hsi = resBody?.ListOfTmEaiServiceDetailsRetrieveReq?.[
			'TmAssetMgmt-AssetIntegration'
		]?.find(item =>
			item.ProductName?.toLowerCase().endsWith('high speed internet')
		);
		const isTcop = resBody ? hsi?.TMTCOPFlag === 'Y' : null;

		return await this.db
			.update(taasServiceDetailsTableSchema)
			.set({
				Status: status,
				Reason: reason,
				IsTcop: isTcop,
				Data: resBody ?? null
			})
			.where(
				and(
					eq(taasServiceDetailsTableSchema.AccountNo, accountNo),
					eq(taasServiceDetailsTableSchema.BillingAccountNo, billingAccountNo)
				)
			)
			.execute();
	}
}

export default BackgroundJob;
