import { type Static, t } from 'elysia';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';

export const submitRebateReqSchema = t.Object({
	SystemName: t.Enum(SystemNameEnum),
	EncryptedBillAccNo: t.String({
		examples: ['231ui6yrcuweyhqgavqbelrik37149hryewairf=-']
	}),
	ServiceID: t.String({ examples: ['test@unifi'] }),
	CTTNumber: t.String({ examples: ['1234567890'] })
});

export type SubmitRebateReq = Static<typeof submitRebateReqSchema>;
