import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';
// Dynamic form Fields
export const fieldsSchema = t.Union([t.Record(t.String(), t.Any()), t.Null()]);
export type Fields = Static<typeof fieldsSchema>;

export const temporalUserTaskTableSchema = pgTable('temporal_user_task', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	OrderId: text('order_id').notNull(),
	WorkflowId: text('workflow_id').notNull(),
	UserTaskId: text('user_task_id').notNull(),
	TaskStatus: text('task_status').notNull(),
	TaskType: text('task_type').notNull(),
	IsOrderable: boolean('is_orderable').notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectTemporalUserTask =
	typeof temporalUserTaskTableSchema.$inferSelect;
