import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const addBankReqSchema = t.Object({
	BankName: t.String({ examples: ['CIMB BANK'] }),
	BankCode: t.Number({ examples: [100] })
});
export type AddBankReq = Static<typeof addBankReqSchema>;

export const addBankResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Action: t.String({ examples: ['ADDED'] }),
			Message: t.String({ examples: ['Bank added successfully'] })
		})
	},
	{ description: 'Bank details successfully added.' }
);
export type AddBankRes = Static<typeof addBankResSchema>;
