import { type Static, t } from 'elysia';

// follow the same structure as the omg api schema
export const omgOttSwapReqSchema = t.Object({
	ottPlanId: t.String(),
	accountType: t.String(),
	accountId: t.String(),
	orderRefNo: t.String(),
	ottTxnId: t.Integer(),
	ottMerchantId: t.Number(),
	ottProductId: t.String(),
	ottOmgId: t.Number(),
	ottLoginType: t.String(),
	ottUserId: t.String()
});

export type OmgOttSwapOrderReq = Static<typeof omgOttSwapReqSchema>;

export const omgOttSwapResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String(),
	orderRefNo: t.String(),
	ottStartDate: t.String()
});

export type OmgOttSwapOrderRes = Static<typeof omgOttSwapResSchema>;
