import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2DeviceBundleReq,
	Wso2DeviceBundleRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2RetrieveBundleDevice.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getLightweightServiceInfo } from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type { LightweightServiceInfo } from '../../../../shared/schemas/api/lightweightServiceInfo.schema';
import AddOnsHelper from '../helpers/addOns.helper';
import type {
	AddOnsEligibilityReq,
	AddOnsEligibilityRes,
	ReportingParams
} from '../schemas/api/addOns.schema';

class AddOnsEligibility {
	private addOnsHelper: AddOnsHelper;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.addOnsHelper = new AddOnsHelper(integrationId, idTokenInfo);
		this.mwIntegration = new MwIntegration(integrationId);
	}
	/**
	 * Get Add-Ons Eligibility
	 * @param req - Add-Ons Eligibility Request
	 * @returns Add-Ons Eligibility Response
	 * @throws UE_ERROR - If multiple products are requested for non-OTT categories
	 * @description Hierarchy for CTA eligibility: service account eligibility > payment records > open order
	 */
	async getAddOnsEligibility(
		req: AddOnsEligibilityReq
	): Promise<AddOnsEligibilityRes> {
		let response: AddOnsEligibilityRes =
			await this.addOnsHelper.createErrorResponse('RETRY');
		const decryptedBillAccNo = await decrypt(req.EncryptedBillAccNo);

		// 1. TCOP flag check
		const tcopResult =
			await this.addOnsHelper.checkTCOPFlag(decryptedBillAccNo);
		if (tcopResult) return tcopResult;

		// 2. Fetch service account details
		const wso2SARes =
			await this.addOnsHelper.fetchServiceAccount(decryptedBillAccNo);

		// 3. Get HSI Moli
		const hsiMoli = this.addOnsHelper.getHighSpeedInternetMoli(wso2SARes);
		if (!hsiMoli)
			return await this.addOnsHelper.createErrorResponse('BROADBAND');

		const serviceInfo: LightweightServiceInfo =
			getLightweightServiceInfo(wso2SARes);

		// 4. OTT Category
		if (req.Category === AddOnsRequestCategoryEnum.OTT) {
			return this.addOnsHelper.getEligibleOtt(req, serviceInfo);
		}

		const reportingParam: ReportingParams = {
			category: req.Category,
			serviceId: serviceInfo.accountId,
			decryptedBillAccNo,
			serviceStartDate: hsiMoli.StartDate ?? null,
			cbpr: null
		};

		// 6. Recent installation order check
		if (this.addOnsHelper.hasRecentInstallationOrder(hsiMoli)) {
			return await this.addOnsHelper.createErrorResponse(
				'OPEN-ORDER-NI',
				reportingParam
			);
		}

		// 7. FTTH platform check for specific categories
		const ftthCategories = [
			AddOnsRequestCategoryEnum.SMART_DEVICE,
			AddOnsRequestCategoryEnum.SME_SMART_DEVICE,
			AddOnsRequestCategoryEnum.SMART_HOME,
			AddOnsRequestCategoryEnum.MESH_WIFI,
			AddOnsRequestCategoryEnum.MESH_WIFI_6,
			AddOnsRequestCategoryEnum.MESH_WIFI_7,
			AddOnsRequestCategoryEnum.UPB
		];
		if (
			!this.addOnsHelper.isFtthPlatform(hsiMoli) &&
			ftthCategories.includes(req.Category)
		) {
			return await this.addOnsHelper.createErrorResponse(
				'PLATFORM',
				reportingParam
			);
		}

		// 8. TV Pack
		if (req.Category === AddOnsRequestCategoryEnum.TV_PACK) {
			response = await this.addOnsHelper.getEligibleTvPack(req, wso2SARes);
		}

		// 9. UPB
		if (req.Category === AddOnsRequestCategoryEnum.UPB) {
			const unifiPlayTvMoli = this.addOnsHelper.getUnifiPlayTvMoli(wso2SARes);
			if (!unifiPlayTvMoli)
				return await this.addOnsHelper.createErrorResponse('UNIFI-TV');
			const deviceQuota = this.addOnsHelper.checkAvailableQuota(
				req.Category,
				unifiPlayTvMoli
			);
			if (deviceQuota.reasonCode)
				return await this.addOnsHelper.createErrorResponse(
					deviceQuota.reasonCode
				);
			response = await this.addOnsHelper.getEligibleUpb(
				deviceQuota.availableQuota,
				hsiMoli
			);
		}

		// 10. Device quota check for other categories
		const deviceQuota = this.addOnsHelper.checkAvailableQuota(
			req.Category,
			hsiMoli
		);
		if (deviceQuota.reasonCode) {
			return await this.addOnsHelper.createErrorResponse(
				deviceQuota.reasonCode,
				reportingParam
			);
		}

		// 11. Product plan eligibility for certain categories
		let wso2DeviceBundleRes: Wso2DeviceBundleRes = null;
		const planEligibilityCategories = [
			AddOnsRequestCategoryEnum.SMART_HOME,
			AddOnsRequestCategoryEnum.SMART_DEVICE,
			AddOnsRequestCategoryEnum.SME_SMART_DEVICE,
			AddOnsRequestCategoryEnum.MESH_WIFI,
			AddOnsRequestCategoryEnum.BLACKNUT
		];
		if (planEligibilityCategories.includes(req.Category)) {
			const planName =
				wso2SARes?.Response?.ServiceAccount?.find(sa => sa.Status === 'Active')
					?.ProductName ?? '';

			const wso2DeviceBundleReq: Wso2DeviceBundleReq = {
				DeviceBundleRetrieveRequest: {
					TmDeviceBundle: {
						TMActiveFlag: 'Y',
						TMBundleName: planName
					}
				}
			};

			wso2DeviceBundleRes =
				await this.mwIntegration.Wso2EligibilityIntegration.getWso2DeviceBundle(
					wso2DeviceBundleReq
				);

			const isEligible =
				wso2DeviceBundleRes?.Response?.DeviceBundleRetrieveResponse?.TmDeviceBundle.some(
					bundle =>
						bundle.TMDeviceName?.toLowerCase() === req.ProductName.toLowerCase()
				) || false;

			if (!isEligible)
				return await this.addOnsHelper.createErrorResponse(
					'GENERAL',
					reportingParam
				);
		}

		// 12. Category-specific eligibility
		switch (req.Category) {
			case AddOnsRequestCategoryEnum.SMART_HOME:
				response = await this.addOnsHelper.getEligibleSmartHome(
					hsiMoli,
					reportingParam
				);
				break;
			case AddOnsRequestCategoryEnum.SMART_DEVICE:
			case AddOnsRequestCategoryEnum.SME_SMART_DEVICE:
				response = await this.addOnsHelper.getEligibleSmartDevice(
					req,
					hsiMoli,
					reportingParam
				);
				break;
			case AddOnsRequestCategoryEnum.BLACKNUT: {
				const isEligibleGamepad =
					wso2DeviceBundleRes?.Response?.DeviceBundleRetrieveResponse?.TmDeviceBundle.some(
						bundle =>
							bundle.TMDeviceName?.toLowerCase().includes(
								'Logitech'.toLowerCase()
							)
					) || false;
				response = await this.addOnsHelper.getEligibleBlacknut(
					decryptedBillAccNo,
					deviceQuota.availableQuota,
					isEligibleGamepad,
					hsiMoli
				);
				break;
			}
			case AddOnsRequestCategoryEnum.MESH_WIFI:
				response = await this.addOnsHelper.getEligibleMeshWifi(hsiMoli);
				break;
			case AddOnsRequestCategoryEnum.MESH_WIFI_6:
			case AddOnsRequestCategoryEnum.MESH_WIFI_7:
				response = await this.addOnsHelper.getEligibleMeshWifiCombo(
					req.Category,
					hsiMoli
				);
				break;
		}

		// 13. Payment record check
		const paymentRecord = await this.addOnsHelper.hasGoodPaymentRecord(
			serviceInfo.accountId,
			decryptedBillAccNo,
			req.Category
		);
		if (paymentRecord?.reasonCode) {
			return await this.addOnsHelper.createErrorResponse(
				paymentRecord.reasonCode,
				{
					category: req.Category,
					serviceId: serviceInfo.accountId,
					decryptedBillAccNo,
					serviceStartDate: hsiMoli.StartDate ?? null,
					cbpr: paymentRecord.cbpr
				}
			);
		}

		// 14. Open order checks
		const openOrderSiebel =
			await this.addOnsHelper.checkOpenOrderInSiebel(decryptedBillAccNo);
		if (openOrderSiebel) return openOrderSiebel;

		const openOrderDb =
			await this.addOnsHelper.checkOpenOrderInDb(decryptedBillAccNo);
		if (openOrderDb) return openOrderDb;

		// 15. Return final response
		return response;
	}
}

export default AddOnsEligibility;
