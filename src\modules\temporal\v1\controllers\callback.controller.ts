import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import { baseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type UpdateOrderRes,
	updateOrderReqSchema,
	updateOrderResSchema
} from '../../../order/v1/schemas/api/ottCallback.schema';
import { OttCallbackService } from '../services/callback.service';

export const temporalCallbackV1Routes = new Elysia({ prefix: '/callback/ott' })
	.resolve(() => {
		return {
			TemporalOttCallback: new OttCallbackService(randomUUID())
		};
	})
	.post(
		'/order-update',
		async (ctx): Promise<UpdateOrderRes> => {
			return await ctx.TemporalOttCallback.updateOttOrder(
				ctx.headers.source,
				ctx.body
			);
		},
		{
			headers: baseHeaderSchema,
			body: updateOrderReqSchema,
			response: {
				201: updateOrderResSchema,
				500: baseResponseSchema
			},
			detail: {
				description:
					'Update OTT activation status and continue workflow. Triggered by OMG Enterprise System.',
				tags: ['Temporal', 'Callback']
			}
		}
	);
