import {
	boolean,
	integer,
	json,
	pgTable,
	text,
	timestamp,
	uniqueIndex
} from 'drizzle-orm/pg-core';
import type { TaasServiceDetailsRes } from '../api/taasServiceDetails.schema';

export const taasServiceDetailsTableSchema = pgTable(
	'taas_service_details',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		IdType: text('id_type').notNull(),
		IdValue: text('id_value').notNull(),
		Status: text('status').notNull(),
		AccountNo: text('account_no').notNull(),
		BillingAccountNo: text('billing_account_no').notNull(),
		Reason: text('reason'),
		IsTcop: boolean('is_tcop'),
		Data: json('data').$type<TaasServiceDetailsRes>(),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	},
	table => [
		uniqueIndex('taasServiceDetailsUniqueIndex').on(
			table.AccountNo,
			table.BillingAccountNo
		)
	]
);

export type SelectTaasServiceDetails =
	typeof taasServiceDetailsTableSchema.$inferSelect;
