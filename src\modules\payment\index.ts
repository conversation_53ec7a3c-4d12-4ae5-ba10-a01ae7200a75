import Elysia from 'elysia';
import autopayV1Routes from './v1/controllers/autopay.controller';
import { osesV1Routes } from './v1/controllers/oses.controller';
import { osesCallbackV1Routes } from './v1/controllers/osesCallback.controller';
import { osesPreLoginV1Routes } from './v1/controllers/osesPreLogin.controller';

const v1Prefix = '/v1/payment';

export const privatePaymentV1Routes = new Elysia({ prefix: v1Prefix })
	.use(autopayV1Routes)
	.use(osesV1Routes);

export const protectedPaymentV1Routes = new Elysia({ prefix: v1Prefix }).use(
	osesPreLoginV1Routes
);

export const publicPaymentV1Routes = new Elysia({
	prefix: v1Prefix
}).use(osesCallbackV1Routes);
