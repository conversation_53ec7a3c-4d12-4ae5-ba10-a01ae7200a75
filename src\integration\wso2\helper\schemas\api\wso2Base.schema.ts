import { type Static, t } from 'elysia';

export const wso2ReqSchema = t.Object({
	method: t.String(),
	headers: t.Object({
		Accept: t.Optional(t.String()),
		'Content-Type': t.String(),
		Authorization: t.String(),
		apimToken: t.Optional(t.String()),
		'x-bpid': t.String(),
		'x-boid': t.String(),
		'x-calling-application': t.String(),
		LightWeightFlag: t.Optional(t.String()),
		'x-version': t.Optional(t.String()),
		'x-type': t.Optional(t.String()),
		'Cache-Control': t.Optional(t.String())
	}),
	body: t.Optional(t.String())
});

export type Wso2BodyReq = Static<typeof wso2ReqSchema>;

export const wso2BaseResponseSchema = t.Optional(
	t.Object({
		Status: t.Object({
			Type: t.MaybeEmpty(t.String()),
			Code: t.MaybeEmpty(t.String()),
			Message: t.String()
		})
	})
);

export type Wso2BaseResponse = Static<typeof wso2BaseResponseSchema>;
