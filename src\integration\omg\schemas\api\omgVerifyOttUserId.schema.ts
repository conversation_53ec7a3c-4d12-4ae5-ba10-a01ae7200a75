import { type Static, t } from 'elysia';

export const omgOttUserVerifyReqSchema = t.Object({
	accountType: t.String({
		minLength: 1
	}),
	accountId: t.String({
		minLength: 1
	}),
	ottMerchantId: t.Integer({
		minimum: 1
	}),
	ottUserId: t.String({
		minLength: 1
	})
});

export type OmgOttUserVerifyReq = Static<typeof omgOttUserVerifyReqSchema>;

export const omgOttUserVerifyResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String()
});

export type OmgOttUserVerifyRes = Static<typeof omgOttUserVerifyResSchema>;
