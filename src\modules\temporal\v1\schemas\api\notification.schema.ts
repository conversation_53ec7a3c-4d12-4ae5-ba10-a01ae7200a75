import { type Static, t } from 'elysia';

export const temporalNotificationMediumParamSchema = t.Object({
	channel: t.Union([t.Literal('email'), t.Literal('activity')], {
		description:
			'Channel to trigger user communication via Netcore.\n' +
			`- 'email': Sends transactional email directly via Netcore Email API.\n` +
			`- 'activity': Sends event to Netcore CE; email is sent by Netcore based on campaign rules or automation flows.`,
		example: 'email'
	})
});

export type TemporalNotificationMediumParam = Static<
	typeof temporalNotificationMediumParamSchema
>;

export const temporalNotificationRequestSchema = t.Object({
	OrderId: t.String({
		description: 'Unique identifier of the order to notify',
		example: 'UFNH-123456'
	})
});

export type TemporalNotificationRequest = Static<
	typeof temporalNotificationRequestSchema
>;
