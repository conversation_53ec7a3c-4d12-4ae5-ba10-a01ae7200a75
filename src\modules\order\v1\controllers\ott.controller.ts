import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type DisneyChangeMobileRes,
	disneyChangeMobileRequestSchema,
	disneyChangeMobileReschema
} from '../schemas/api/disneyChangeMobile.schema';
import {
	type NetflixAccRecoveryResType,
	netflixAccRecoveryReqSchema,
	netflixAccRecoveryResSchema
} from '../schemas/api/netflixAccRecovery.schema';
import { netflixCancelPlanReqSchema } from '../schemas/api/netflixCancelPlan.schema';
import {
	type NetflixChangePlanRes,
	netflixChangePlanReqSchema,
	netflixChangePlanResSchema
} from '../schemas/api/netflixChangePlan.schema';
import {
	type NetflixCurrentPlanRes,
	netflixCurrentPlanReqSchema,
	netflixCurrentPlanResSchema
} from '../schemas/api/netflixCurrentPlan.schema';
import {
	ottActivateReqSchema,
	ottActivateResSchema
} from '../schemas/api/ottActivate.schema';
import {
	ottActivationUrlReqSchema,
	ottActivationUrlResSchema
} from '../schemas/api/ottActivationUrl.schema';
import {
	type OttEntitlementRes,
	ottEntitlementReqSchema,
	ottEntitlementResSchema
} from '../schemas/api/ottEntitlement.schema';
import {
	ottSwapReqSchema,
	ottSwapResSchema
} from '../schemas/api/ottSwap.schema';
import {
	ottVerifyUserReqSchema,
	ottVerifyUserResSchema
} from '../schemas/api/ottVerifyUser.schema';
import Ott from '../services/ott.service';

const prefix = '/add-ons/ott';

export const ottV1Routes = new Elysia({ prefix })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			Ott: new Ott(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/entitlement',
		async (ctx): Promise<OttEntitlementRes> => {
			return await ctx.Ott.getOttEntitlement(ctx.query);
		},
		{
			detail: {
				description:
					"Get customer's OTT entitlement for activation. <br><br> <b>Backend System:</b> OMG",
				tags: ['Order']
			},
			query: ottEntitlementReqSchema,
			response: {
				200: ottEntitlementResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/verify-login-id',
		async ctx => {
			return await ctx.Ott.userVerify(ctx.body);
		},
		{
			detail: {
				description:
					'Verify the login ID for OTT activation.<br><br><b>Backend:</b> OMG',
				tags: ['Order']
			},
			body: ottVerifyUserReqSchema,
			response: {
				200: ottVerifyUserResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/activate',
		async ctx => {
			return await ctx.Ott.activateOtt(ctx.headers, ctx.body);
		},
		{
			headers: baseHeaderSchema,
			body: ottActivateReqSchema,
			response: {
				201: ottActivateResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Submit OTT activate order to OMG.<br><br><b>Table:</b> orderable_txn_history <br><b>Backend:</b> OMG',
				tags: ['Order']
			}
		}
	)
	.post(
		'/swap',
		async ctx => {
			return await ctx.Ott.swapOtt(ctx.headers, ctx.body);
		},
		{
			headers: baseHeaderSchema,
			body: ottSwapReqSchema,
			response: {
				201: ottSwapResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Submit OTT swap order to OMG.<br><br><b>Table:</b> orderable_txn_history <br><b>Backend:</b> OMG',
				tags: ['Order']
			}
		}
	)
	.get(
		'/netflix/current-plan',
		async (ctx): Promise<NetflixCurrentPlanRes> => {
			return await ctx.Ott.getCurrentNetflixPlan(ctx.query);
		},
		{
			detail: {
				description:
					'Get the current Netflix subscription plan for a user.<br><br><b>Backend:</b> OMG, Netflix',
				tags: ['Order']
			},
			query: netflixCurrentPlanReqSchema,
			response: {
				200: netflixCurrentPlanResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/netflix/change-plan',
		async (ctx): Promise<NetflixChangePlanRes> => {
			const res = await ctx.Ott.changeNetflixPlanService(
				ctx.body,
				ctx.headers.source
			);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Processes a Netflix subscription plan change for a user. <br><br><b>Backend:</b> OMG, Netflix',
				tags: ['Order']
			},
			headers: baseHeaderSchema,
			body: netflixChangePlanReqSchema,
			response: {
				200: netflixChangePlanResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/disney-check-change-mobile',
		async (ctx): Promise<DisneyChangeMobileRes> => {
			const res = await ctx.Ott.getCheckChangeDisneyMobile(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			headers: baseHeaderSchema,
			body: disneyChangeMobileRequestSchema,
			response: {
				201: disneyChangeMobileReschema,
				403: errorBaseResponseSchema,
				503: errorBaseResponseSchema
			},
			detail: {
				description: 'OTT Order Update request. <br><br><b>Backend:</b> OMG',
				tags: ['Order']
			}
		}
	)
	.post(
		'/netflix/cancel-plan',
		async (ctx): Promise<BaseResponse> => {
			const res = await ctx.Ott.cancelNetflixPlan(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Processes a Netflix cancel plan for a user. <br><br><b>Backend:</b> OMG, Netflix',
				tags: ['Order']
			},
			body: netflixCancelPlanReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/netflix/account-recovery-url',
		async (ctx): Promise<NetflixAccRecoveryResType> => {
			return await ctx.Ott.getNetflixAccRecoveryUrl(
				ctx.body,
				ctx.headers.source
			);
		},
		{
			detail: {
				description:
					'Retrieves Netflix account recovery URL from OMG.<br><br><b>Backend:</b> OMG',
				tags: ['Order']
			},
			headers: baseHeaderSchema,
			body: netflixAccRecoveryReqSchema,
			response: {
				200: netflixAccRecoveryResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/activation-url',
		async ctx => {
			const res = await ctx.Ott.getOttActivationUrl(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Get Activation URL.<br><br><b>Backend:</b> OMG - HBO AND NETFLIX',
				tags: ['Order']
			},
			body: ottActivationUrlReqSchema,
			response: {
				201: ottActivationUrlResSchema,
				500: errorBaseResponseSchema
			}
		}
	);
