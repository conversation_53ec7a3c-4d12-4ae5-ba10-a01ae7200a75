/**
 * This enum file holds all official HTTP status code descriptions from Netcore API documentation.
 *
 * ⚠️ IMPORTANT:
 * - These messages are NOT meant for frontend or end-user display.
 * - They are intended for backend observability, Temporal Workflow diagnostics, and internal logging across UE.
 * - The enum values exactly match the descriptions provided by Netcore documentation.
 */

/**
 * NetcoreStatusMessageEnum
 * For success-related responses from Netcore Email API.
 */
export enum NetcoreStatusMessageEnum {
	SUCCESS_200 = 'Success. Everything went smooth. In case of GET requests.',
	CREATED_201 = 'Created. The request has been fulfilled and resulted in a new resource being created. Example; In case of create a template request, you will receive this response.',
	ACCEPTED_202 = 'Accepted. The request has been accepted for processing but the processing has not been completed. Example: you may receive this response Send Email request in case your daily limit is reached.'
}

/**
 * NetcoreActivityStatusMessageEnum
 * Specific errors returned by Netcore Add Activity API.
 */
export enum NetcoreActivityStatusMessageEnum {
	BAD_REQUEST_400 = 'Bad Request | Missing Parameter - Activity Name'
}

/**
 * NetcoreCommonStatusMessageEnum
 * Shared/common HTTP error responses across Netcore APIs.
 */
export enum NetcoreCommonStatusMessageEnum {
	UNAUTHORIZED_401 = 'Unauthorized | Invalid API Key',
	METHOD_NOT_ALLOWED_405 = 'Method Not Allowed',
	REQUEST_TIMEOUT_408 = 'Request Time Out',
	INTERNAL_SERVER_ERROR_500 = 'Internal Server Error',
	DEFAULT = 'Unexpected response from Netcore API'
}

/**
 * NetcoreStatusMessage
 * Union type used for mapping Netcore status codes across all integration methods.
 */
export type NetcoreStatusMessage =
	| NetcoreStatusMessageEnum
	| NetcoreActivityStatusMessageEnum
	| NetcoreCommonStatusMessageEnum;
