import { format } from 'date-fns';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';

import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getMyTimeZoneDate } from '../../../../shared/common';
import AppointmentRecordHelper from '../helpers/appointment.helper';
import type {
	AddOnsAppointmentSlotReq,
	AddOnsAppointmentSlotRes
} from '../schemas/api/addOns.schema';

class AddOns {
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private appointmentHelper: AppointmentRecordHelper;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.appointmentHelper = new AppointmentRecordHelper(
			integrationId,
			this.idTokenInfo
		);
	}

	// for mesh wifi 6
	async getAppointmentSlots(
		req: AddOnsAppointmentSlotReq,
		segment: string
	): Promise<AddOnsAppointmentSlotRes> {
		const { earliestStartDate, latestStartDate } =
			this.getEarliestAndLatestStartDate();

		const resultAppointmentHelper =
			await this.appointmentHelper.appointmentSlotRecordHelper(
				req,
				segment,
				earliestStartDate,
				latestStartDate
			);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: resultAppointmentHelper
		};
	}
	private getEarliestAndLatestStartDate(): {
		earliestStartDate: string;
		latestStartDate: string;
	} {
		const dateFormat: string = 'MM/dd/yyyy HH:mm:ss';
		const currentDate: Date = getMyTimeZoneDate();

		// Create new Date objects for the earliest and latest dates
		const tomorrow = new Date(currentDate);
		tomorrow.setDate(currentDate.getDate() + 1);

		const twoWeeksLater = new Date(currentDate);
		twoWeeksLater.setDate(currentDate.getDate() + 14);

		const earliestStartDate: string = format(tomorrow, dateFormat);
		const latestStartDate: string = format(twoWeeksLater, dateFormat);

		return {
			earliestStartDate,
			latestStartDate
		};
	}
}

export default AddOns;
