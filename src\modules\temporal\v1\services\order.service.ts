import { and, eq } from 'drizzle-orm'; // Query helpers
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import {
	type OrderableProgress,
	type SelectCustomerOrder,
	type SelectOrderablePlan,
	type SelectOrderableTxnHistory,
	customerOrderTableSchema,
	orderablePlanTableSchema,
	orderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/orderable.schema.ts';

import { getDbInstance } from '../../../../config/db.config.ts';
import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum.ts';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum.ts';
import {
	TemporalNamespaces,
	TemporalOrderStatus
} from '../../../../enum/temporal.enum.ts';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum.ts';
import { MwIntegration } from '../../../../integration/mw.integration.ts';
import type { TemporalTriggerWorkflowRes } from '../../../../integration/temporal/schemas/api/temporal.schema.ts';
import type { Wso2OrderMonitoringReq } from '../../../../integration/wso2/order/schemas/api/wso2OrderMonitoring.schema.ts';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema.ts';
import { UE_ERROR } from '../../../../middleware/error.ts';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema.ts';
import { getMyTimeZoneDate } from '../../../../shared/common.ts';
import type { BaseHeader } from '../../../../shared/schemas/api/headers.schema.ts';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema.ts';
import AddOnsOrderHelper from '../../../order/v1/helpers/addOnsOrder.helper.ts';
import type {
	AddOnsOrderReq,
	AddOnsOrderRes
} from '../../../order/v1/schemas/api/addOnsOrder.schema.ts';
import {
	type SelectNonOrderableTxnHistory,
	nonOrderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/nonOrderableTxnHistory.schema.ts';
import Orderable from '../../../order/v1/services/orderable.service.ts';
import type { TemporalAddOnsOrderReq } from '../schemas/api/addons.schema.ts';
import type {
	GetNovaStatusReq,
	GetNovaStatusRes,
	OrderDetailsBodyReq,
	OrderUpdateDetailsReq,
	OrderUpdateReq
} from '../schemas/api/order.schema.ts';

class Order {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo | undefined;
	private addOnsOrderHelper: AddOnsOrderHelper;

	constructor(integrationId: string, idTokenInfo?: IdTokenInfo) {
		this.integrationId = integrationId;
		this.idTokenInfo = idTokenInfo;
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
		this.addOnsOrderHelper = new AddOnsOrderHelper(this.integrationId);
	}

	async getAllOrderableHistory(): Promise<SelectOrderableTxnHistory[]> {
		return await this.db.select().from(orderableTxnHistoryTableSchema);
	}

	async updateOrderableTxn(
		orderTxn: SelectOrderableTxnHistory
	): Promise<SelectOrderableTxnHistory> {
		const result: SelectOrderableTxnHistory[] = await this.db
			.update(orderableTxnHistoryTableSchema)
			.set({
				...orderTxn,
				UpdatedAt: getMyTimeZoneDate()
			})
			.where(and(eq(orderableTxnHistoryTableSchema.OrderId, orderTxn.OrderId)))
			.returning();

		if (result.length === 0) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.BAD_REQUEST_ERROR, {
				integrationId: this.integrationId
			});
		}

		return result[0];
	}

	async updateNonOrderableTxn(
		orderTxn: SelectNonOrderableTxnHistory
	): Promise<SelectNonOrderableTxnHistory> {
		const result: SelectNonOrderableTxnHistory[] = await this.db
			.update(nonOrderableTxnHistoryTableSchema)
			.set({
				...orderTxn,
				UpdatedAt: getMyTimeZoneDate()
			})
			.where(
				and(eq(nonOrderableTxnHistoryTableSchema.OrderId, orderTxn.OrderId))
			)
			.returning();

		if (result.length === 0) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.BAD_REQUEST_ERROR, {
				integrationId: this.integrationId
			});
		}

		return result[0];
	}

	async updateOrderStatus(
		orderStatusData: OrderUpdateReq
	): Promise<BaseResponse> {
		const checkOrderExist: SelectOrderableTxnHistory[] =
			await this.checkOrderableExistByOrderId(orderStatusData.OrderId);

		if (checkOrderExist.length > 0) {
			const orderProgress: OrderableProgress =
				checkOrderExist[0].OrderProgress ?? [];
			orderProgress.push({
				Status: orderStatusData.OrderProgress,
				Timestamp: getMyTimeZoneDate().toISOString()
			});

			const result: SelectOrderableTxnHistory[] = await this.db
				.update(orderableTxnHistoryTableSchema)
				.set({
					OrderStatus: orderStatusData.OrderStatus,
					OrderProgress: orderProgress,
					UpdatedAt: getMyTimeZoneDate()
				})
				.where(
					eq(orderableTxnHistoryTableSchema.OrderId, orderStatusData.OrderId)
				)
				.returning();

			return {
				Success: result.length > 0,
				Code: StatusCodeEnum.ACCEPTED,
				IntegrationId: this.integrationId
			};
		}

		throw new UE_ERROR('Order not found', StatusCodeEnum.NOT_FOUND_ERROR, {
			integrationId: this.integrationId
		});
	}

	async updateOrderDetails(data: OrderUpdateDetailsReq): Promise<BaseResponse> {
		const checkOrderExist: SelectOrderableTxnHistory[] =
			await this.checkOrderableExistByOrderId(data.OrderId);

		if (checkOrderExist.length > 0) {
			if (data.Type === 'OrderStatus') {
				const orderDetailsByStatus: OrderUpdateReq = {
					OrderId: data.OrderId,
					OrderStatus: data.Details.OrderStatus as string,
					OrderProgress: data.Details.OrderProgress as string
				};

				await this.updateOrderStatus(orderDetailsByStatus);

				return {
					Success: true,
					Code: StatusCodeEnum.ACCEPTED,
					IntegrationId: this.integrationId
				};
			}

			if (data.Type === 'OrderData') {
				const [orderTxn]:
					| SelectOrderableTxnHistory[]
					| SelectNonOrderableTxnHistory[] = data.IsOrderable
					? await this.getOrderableTxnByOrderId(data.OrderId)
					: await this.getNonOrderableTxnByOrderId(data.OrderId);

				if (!orderTxn) {
					throw new UE_ERROR(
						'Order not found',
						StatusCodeEnum.BAD_REQUEST_ERROR,
						{ integrationId: this.integrationId }
					);
				}

				for (const [key, value] of Object.entries(data.Details)) {
					switch (key) {
						case 'AddressNotFoundKCICount':
							orderTxn.OrderData.AddressNotFoundKCICount = value;
							break;

						case 'CustomerAgreeToProceed':
							orderTxn.OrderData.CustomerAgreeToProceed = value;
							break;

						case 'PFLKCICount':
							orderTxn.OrderData.PFLKCICount = value;
							break;

						case 'RfsUpdateFlag':
							orderTxn.OrderData.RfsUpdateFlag = value;
							break;

						case 'DemandUnifiAir':
							orderTxn.OrderData.DemandDetails.UnifiAir = value;
							break;

						case 'DemandStatus':
							orderTxn.OrderData.DemandDetails.DemandStatus = value;
							break;

						default:
							throw new UE_ERROR(
								'Column Not Found',
								StatusCodeEnum.NOT_FOUND_ERROR
							);
					}
				}

				data.IsOrderable
					? await this.updateOrderableTxn(orderTxn as SelectOrderableTxnHistory)
					: await this.updateNonOrderableTxn(
							orderTxn as SelectNonOrderableTxnHistory
						);

				return {
					Success: true,
					Code: StatusCodeEnum.ACCEPTED,
					IntegrationId: this.integrationId
				};
			}
		}

		throw new UE_ERROR('Order not found', StatusCodeEnum.NOT_FOUND_ERROR);
	}

	async checkOrderableExistByOrderId(
		order_id: string
	): Promise<SelectOrderableTxnHistory[]> {
		return await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(and(eq(orderableTxnHistoryTableSchema.OrderId, order_id)));
	}

	async getOrderableTxnByOrderId(
		order_id: string
	): Promise<SelectOrderableTxnHistory[]> {
		return await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(eq(orderableTxnHistoryTableSchema.OrderId, order_id));
	}

	async getNonOrderableTxnByOrderId(
		order_id: string
	): Promise<SelectNonOrderableTxnHistory[]> {
		return await this.db
			.select()
			.from(nonOrderableTxnHistoryTableSchema)
			.where(
				eq(nonOrderableTxnHistoryTableSchema.OrderId, Number.parseInt(order_id))
			);
	}

	async getOrderCustByCustId(
		customer_id: string
	): Promise<SelectCustomerOrder[]> {
		return await this.db
			.select()
			.from(customerOrderTableSchema)
			.where(eq(customerOrderTableSchema.CustomerId, customer_id));
	}

	async getPlanDetailsByPlanId(
		plan_id: string
	): Promise<SelectOrderablePlan[]> {
		return await this.db
			.select()
			.from(orderablePlanTableSchema)
			.where(eq(orderablePlanTableSchema.PlanId, plan_id));
	}

	private async getOrderAndCustomerDetails(
		orderId: string,
		isOrderable: boolean
	): Promise<{
		orderTxn: SelectOrderableTxnHistory | SelectNonOrderableTxnHistory;
		customerDetails: SelectCustomerOrder;
	} | null> {
		// Fetch order transaction by order ID
		const orderTxn:
			| SelectOrderableTxnHistory[]
			| SelectNonOrderableTxnHistory[] = isOrderable
			? await this.getOrderableTxnByOrderId(orderId)
			: await this.getNonOrderableTxnByOrderId(orderId);
		if (orderTxn.length === 0) {
			return null;
		}

		// Fetch customer details using the CustomerId from order transaction
		if (orderTxn[0] && 'CustomerId' in orderTxn[0]) {
			const customerDetails: SelectCustomerOrder[] =
				await this.getOrderCustByCustId(orderTxn[0].CustomerId ?? '');
			return { orderTxn: orderTxn[0], customerDetails: customerDetails[0] };
		}
		return {
			orderTxn: orderTxn[0],
			customerDetails: {} as SelectCustomerOrder
		};
	}
	async getOrderDetails({ OrderId, Params, IsOrderable }: OrderDetailsBodyReq) {
		const result: Record<string, string> = {};

		const orderDetails = await this.getOrderAndCustomerDetails(
			OrderId,
			IsOrderable
		);
		if (!orderDetails) {
			throw new UE_ERROR('Order ID not found', StatusCodeEnum.NOT_FOUND_ERROR);
		}

		const { orderTxn, customerDetails } = orderDetails;
		const orderData = orderTxn.OrderData ?? {};

		let planDetails: SelectOrderablePlan | null = null;
		if (IsOrderable && 'PlanId' in orderTxn && orderTxn.PlanId) {
			const plans = await this.getPlanDetailsByPlanId(orderTxn.PlanId);
			planDetails = plans[0] ?? null;
		}

		const toStringOrEmpty = (value: unknown) =>
			value === null || value === undefined ? '' : String(value);

		const paramHandlers: Record<string, () => void> = {
			Status: () => {
				result.OrderStatus = toStringOrEmpty(orderTxn.OrderStatus);
				if ('OrderProgress' in orderTxn) {
					const progress = orderTxn.OrderProgress;
					if (progress?.length) {
						result.OrderProgress = toStringOrEmpty(
							progress[progress.length - 1].Status
						);
					}
				}
			},
			IdType: () => {
				const val = IsOrderable
					? customerDetails.IdType
					: 'IdType' in orderTxn
						? orderTxn.IdType
						: null;
				result.IdType = toStringOrEmpty(val);
			},
			SmartDevice: () => {
				const hasDevices =
					(orderData.Addons?.Devices?.SmartDevices?.length ?? 0) > 0;
				result.SmartDevice = hasDevices ? 'true' : 'false';
			},
			TroikaTicketId: () => {
				result.TroikaTicketId = toStringOrEmpty(
					orderData.DemandDetails?.demandTicketId
				);
			},
			GraniteAddressId: () => {
				const val = 'Address' in orderTxn ? orderTxn.Address?.AddressId : '';
				result.GraniteAddressId = toStringOrEmpty(val);
			},
			AddressNotFoundKCI: () => {
				result.AddressNotFoundKCICount = toStringOrEmpty(
					orderData.AddressNotFoundKCICount
				);
			},
			CustomerAgreeToProceed: () => {
				result.CustomerAgreeToProceed = toStringOrEmpty(
					orderData.CustomerAgreeToProceed
				);
			},
			IsOrderable: () => {
				const val = planDetails?.PlanTechnical?.orderable ?? false;
				result.IsOrderable = val ? 'true' : 'false';
			},
			StatusEnterprise: () => {
				result.StatusEnterprise = toStringOrEmpty(orderData.StatusEnterprise);
			},
			OrderBucket: () => {
				result.OrderBucket = toStringOrEmpty(
					planDetails?.PlanTechnical?.dbs_bucket
				);
			},
			MMAG: () => {
				const products = orderData.Products;
				const isMMAG = products?.some(
					(product: { DeliveryPartner: string }) =>
						product.DeliveryPartner === 'MMAG'
				);
				result.MMAG = isMMAG ? 'true' : 'false';
			},
			Category: () => {
				const val =
					'Category' in orderTxn ? orderTxn.Category : orderData.Category;
				result.Category = toStringOrEmpty(val);
			},
			OttType: () => {
				result.OttType = toStringOrEmpty(orderData.SubCategory);
			}
		};

		for (const param of Params) {
			paramHandlers[param]?.();
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			Response: result,
			IntegrationId: this.integrationId
		};
	}

	async getOmgNovaOrderStatusByOrderId(
		requestData: GetNovaStatusReq
	): Promise<GetNovaStatusRes> {
		const monitoringRequest: Wso2OrderMonitoringReq = {
			OrderMonitoringRequest: {
				OrderNumber: requestData.OrderId
			}
		};

		const res =
			await this.mwIntegration.Wso2OrderIntegration.getWso2OrderMonitoringStatus(
				monitoringRequest
			);

		const orderData = res?.Response?.OrderMonitoringResponse;

		if (orderData) {
			return {
				Success: true,
				Code: StatusCodeEnum.ACCEPTED,
				IntegrationId: this.integrationId,
				Response: orderData
			};
		}

		throw new UE_ERROR(
			'Empty or invalid response from WSO2 Order Monitoring',
			StatusCodeEnum.WSO2_ERROR,
			{ integrationId: this.integrationId, response: res }
		);
	}

	async saveAddOnOrder(
		headers: BaseHeader,
		req: AddOnsOrderReq
	): Promise<AddOnsOrderRes> {
		if (!this.idTokenInfo) {
			throw new UE_ERROR(
				'idTokenInfo is required',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		const orderable = new Orderable(this.integrationId, this.idTokenInfo);

		const { orderId } = await orderable.prepareAddonsOrder(headers, req);

		const triggerRes: TemporalTriggerWorkflowRes =
			await this.mwIntegration.TemporalIntegration.triggerWorkflow({
				OrderId: orderId,
				IsOrderable: true,
				Namespace: TemporalNamespaces.IRENEW
			});

		if (!triggerRes.Success) {
			throw new UE_ERROR(
				'Temporal trigger workflow failed',
				StatusCodeEnum.TEMPORAL_ERROR,
				{ integrationId: this.integrationId, response: triggerRes }
			);
		}

		const res: AddOnsOrderRes = {
			Success: true,
			Code: 201,
			IntegrationId: this.integrationId,
			Response: {
				Status: TemporalOrderStatus.SUBMITTED,
				OrderNo: orderId
			}
		};

		return res;
	}

	async submitNova(body: TemporalAddOnsOrderReq): Promise<BaseResponse> {
		const [orderableTxn] = await this.getOrderableTxnByOrderId(body.OrderId);

		if (!orderableTxn) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.NOT_FOUND_ERROR, {
				integrationId: this.integrationId
			});
		}

		if (
			orderableTxn.BillingAccountNo === null ||
			orderableTxn.BillingAccountNo === ''
		) {
			throw new UE_ERROR(
				'Billing Account Number is missing in db table',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		if (orderableTxn.CustomerId === null || orderableTxn.CustomerId === '') {
			throw new UE_ERROR(
				'Customer ID is required',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const [orderableCust] = await this.getOrderCustByCustId(
			orderableTxn.CustomerId
		);

		const wso2SAReq: Wso2ServiceAccountReq = {
			idType: orderableCust.IdType,
			idValue: orderableCust.IdValue,
			BillingAccountNo: orderableTxn.BillingAccountNo,
			SystemName: SystemNameEnum.NOVA
		};

		const wso2SARes: Wso2ServiceAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2SAReq,
				LightweightFlagEnum.NO,
				true,
				false
			);

		let res: AddOnsOrderRes;

		if (
			orderableTxn.OrderData?.Category === AddOnsRequestCategoryEnum.TV_PACK
		) {
			res = await this.addOnsOrderHelper.submitOrderTvPack(
				orderableTxn.OrderId,
				orderableTxn.OrderData,
				wso2SARes
			);
		} else {
			res = await this.addOnsOrderHelper.submitOrderDevice({
				orderId: orderableTxn.OrderId,
				req: orderableTxn.OrderData,
				wso2SARes,
				reservationNo: orderableTxn.OrderData?.ReservationNo,
				activityId: orderableTxn.OrderData?.ActivityId
			});
		}

		if (res.Success) {
			return {
				Success: true,
				Code: StatusCodeEnum.ACCEPTED,
				IntegrationId: this.integrationId
			};
		}

		throw new UE_ERROR(
			'Failed to submit order',
			StatusCodeEnum.UE_INTERNAL_SERVER,
			{ integrationId: this.integrationId }
		);
	}
}

export default Order;
