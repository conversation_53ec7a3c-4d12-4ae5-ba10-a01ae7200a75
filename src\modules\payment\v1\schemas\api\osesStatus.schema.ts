import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const osesStatusReqSchema = t.Object({
	MerchantTxnId: t.String({ examples: [100000] }),
	EncryptedHash: t.Optional(t.String())
});

export type OsesStatusReq = Static<typeof osesStatusReqSchema>;

export const osesStatusResObjSchema = t.Object({
	BillingAccounts: t.Array(
		t.Optional(
			t.Object({
				BillingAccountNo: t.String({ examples: ['**********'] }),
				GrossAmount: t.String({ examples: ['1.00'] }),
				IsLinkedAccount: t.Optional(
					t.<PERSON>an({
						examples: [true],
						description:
							'true if the billing account has been linked to the customer account. This flag is valid for PFA payment type only.'
					})
				)
			})
		)
	),
	MerchantTxnId: t.String({ examples: [100000] }),
	MerchantId: t.String({ examples: ['**********'] }),
	PayerEmail: t.String({ format: 'email', examples: ['<EMAIL>'] }),
	PayerName: t.String({ examples: ['John Doe'] }),
	BankReference: t.Nullable(t.String()),
	Source: t.Nullable(t.String({ examples: ['UNIFI APP'] })),
	TotalAmount: t.String({ examples: ['1.00'] }),
	PaymentType: t.String({ examples: ['PFA'] }),
	PaymentMethod: t.Nullable(t.String({ examples: ['1'] })),
	TxnDate: t.Nullable(t.String({ examples: ['24-06-2024 22:07:12'] })),
	TxnStatus: t.Nullable(t.String({ examples: ['SUCCESSFUL'] })),
	TxnId: t.Nullable(t.String({ examples: ['100000'] }))
});

export type OsesStatusResObj = Static<typeof osesStatusResObjSchema>;

export const osesStatusResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			TransactionStatus: osesStatusResObjSchema
		})
	},
	{
		description:
			'OSES payment status based on merchant txn id successfully retrieved.'
	}
);

export type OsesStatusRes = Static<typeof osesStatusResSchema>;
