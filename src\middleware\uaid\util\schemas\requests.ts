import { type Static, t } from 'elysia';

export const wso2ReqSchema = t.Object({
	method: t.String(),
	headers: t.Object({
		'Content-Type': t.String(),
		Authorization: t.String(),
		'x-bpid': t.String(),
		'x-boid': t.String(),
		'x-calling-application': t.String(),
		LightWeightFlag: t.Optional(t.String()),
		'x-version': t.Optional(t.String())
	}),
	body: t.Optional(t.String())
});

export type Wso2BodyReq = Static<typeof wso2ReqSchema>;
