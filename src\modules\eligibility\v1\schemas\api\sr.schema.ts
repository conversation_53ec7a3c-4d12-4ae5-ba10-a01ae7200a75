import { type Static, t } from 'elysia';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const srTerminationReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		examples: ['fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-']
	}),
	SystemName: t.Enum(SystemNameEnum)
});

export type SRTerminationReq = Static<typeof srTerminationReqSchema>;

export const srTerminationResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsAllowTermination: t.<PERSON>an({
				description:
					'Eligibility for termination. The value is false if the user is not passed for Nova transfer request or has outstanding balance.'
			}),
			LatestReceivedPaymentDate: t.String({ examples: ['01-01-2024'] }),
			BillAmount: t.String({ examples: ['RM 999.99'] }),
			BillDueDate: t.String({ examples: ['01-01-2024'] }),
			BillNo: t.String({ examples: ['12345'] })
		})
	},
	{ description: "Customer's eligibility successfully retrieved." }
);

export type SRTerminationRes = Static<typeof srTerminationResSchema>;
