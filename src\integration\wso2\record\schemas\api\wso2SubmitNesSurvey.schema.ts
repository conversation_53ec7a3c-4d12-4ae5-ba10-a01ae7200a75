import { type Static, t } from 'elysia';

export const wso2SubmitNesSurveyReqSchema = t.Object({
	SurveyDetailsInsertRequest: t.Object({
		Context: t.String(),
		Journey: t.String(),
		Activity: t.String(),
		Rating: t.String(),
		Question: t.String(),
		Reason: t.String(),
		Comments: t.String(),
		AccountNo: t.String(),
		Name: t.String(),
		Mobile: t.String(),
		Email: t.String(),
		DatetimeCreated: t.String(),
		ServiceID: t.String(),
		ReferenceID: t.String()
	})
});

export type Wso2SubmitNesSurveyReq = Static<
	typeof wso2SubmitNesSurveyReqSchema
>;

export const wso2SubmitNesSurveyResSchema = t.Object({
	SurveyDetailsInsertResponse: t.Object({
		Status: t.Object({
			Type: t.String(),
			Code: t.String(),
			Message: t.String()
		})
	})
});

export type Wso2SubmitNesSurveyRes = Static<
	typeof wso2SubmitNesSurveyResSchema
>;
