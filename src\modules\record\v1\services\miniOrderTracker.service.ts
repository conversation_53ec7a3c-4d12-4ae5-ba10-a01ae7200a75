import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2OrderTrackingReq,
	Wso2OrderTrackingRes
} from '../../../../integration/wso2/record/schemas/api/wso2OrderTracking.schema';
import type { ValidateOrderIdRes } from '../schemas/api/orderTracker.schema';

class MiniOrderTracker {
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async validateOrderId(
		orderId: string,
		idType: string,
		customerId: string
	): Promise<ValidateOrderIdRes> {
		const wso2Req: Wso2OrderTrackingReq = {
			OrderTracking: {
				SystemName: 'GOER',
				Orderdetails: {
					OrderNo: orderId,
					IdType: idType,
					CustomerID: customerId
				}
			}
		};

		const wso2Res: Wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				wso2Req
			);

		const orderDetail =
			wso2Res.Response.OrderDetails?.ViewOrderDetailResponse?.find(
				order => order.SBLOrderID === orderId
			);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				SystemName: orderDetail?.System ?? '',
				OrderStatus: orderDetail?.OrderStatus ?? '',
				InstallationActStat: orderDetail?.InstallationActStat ?? '',
				InstallationActTime: orderDetail?.InstallationActTime ?? '',
				SBLOrderID: orderDetail?.SBLOrderID ?? '',
				OrderNumber: orderDetail?.OrderNumber ?? '',
				OrderType: orderDetail?.OrderType ?? '',
				OrderPlanName: orderDetail?.OrderPlanName ?? '',
				ServiceID: orderDetail?.ServiceID ?? '',
				CreatedDate: orderDetail?.CreatedDate ?? ''
			}
		};
	}
}

export default MiniOrderTracker;
