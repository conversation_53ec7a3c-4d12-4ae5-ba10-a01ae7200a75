import { type Static, t } from 'elysia';

export const taasServiceDetailsReqSchema = t.Object({
	requestHeader: t.Object({
		requestId: t.String({
			example: 'UE'
		}),
		eventName: t.String({
			example: 'evOTaaSServiceDetailsRetrieveNOVASiebel'
		})
	}),
	ListOfTmEaiServiceDetailsRetrieveReq: t.Object({
		'TmAssetMgmt-AssetIntegration': t.Object({
			AccountNo: t.String({
				example: '1-ABCDEFG'
			}),
			BillingAccountNumber: t.String({
				example: '**********'
			}),
			NetworkElementType: t.String({
				example: ''
			})
		})
	})
});

export type TaaSServiceDetailsReq = Static<typeof taasServiceDetailsReqSchema>;

const tmAssetMgmtAssetIntegrationSchema = t.Object({
	TMBillableFlag: t.Nullable(t.String()),
	TMDiscountDuration: t.Nullable(t.String()),
	TMDiscountEndDate: t.Nullable(t.String()),
	TMDiscountPercentage: t.Nullable(t.String()),
	TMDiscountPromotion: t.Nullable(t.String()),
	TMSmartDeviceFlag: t.Nullable(t.String()),
	Id: t.Nullable(t.String()),
	IntegrationId: t.Nullable(t.String()),
	Status: t.Nullable(t.String()),
	AccountNo: t.Nullable(t.String()),
	BillingAccountNumber: t.Nullable(t.Number()),
	NetworkElementType: t.Nullable(t.String()),
	CustomerAccountName: t.Nullable(t.String()),
	BillingAccountName: t.Nullable(t.String()),
	BillingAccountId: t.Nullable(t.String()),
	PrimaryBillingProfileId: t.Nullable(t.String()),
	CompoundProductNumber: t.Nullable(t.String()),
	TMRootPromotionInstanceId: t.Nullable(t.String()),
	InstallDate: t.Nullable(t.String()),
	ParentAssetId: t.Nullable(t.String()),
	ParentAssetName: t.Nullable(t.String()),
	TMIPV6LANAddress: t.Nullable(t.String()),
	TMIPV6ReservationStatus: t.Nullable(t.String()),
	TMIPV6WANAddress: t.Nullable(t.String()),
	ProdPromInstanceId: t.Nullable(t.String()),
	ProdPromName: t.Nullable(t.String()),
	ProductDescription: t.Nullable(t.String()),
	ProductId: t.Nullable(t.String()),
	ProductName: t.Nullable(t.String()),
	ProductPartNumber: t.Nullable(t.String()),
	ProductDisplayName: t.Nullable(t.String()),
	ProductType: t.Nullable(t.String()),
	Quantity: t.Nullable(t.Number()),
	RootAssetId: t.Nullable(t.String()),
	RootIntegrationId: t.Nullable(t.String()),
	RootProductId: t.Nullable(t.String()),
	SerialNumber: t.Nullable(t.String()),
	ServicePointId: t.Nullable(t.String()),
	StartDate: t.Nullable(t.String()),
	ContractStartDate: t.Nullable(t.String()),
	Type: t.Nullable(t.String()),
	TMUPECPEName: t.Nullable(t.String()),
	TMPrimaryUPEId: t.Nullable(t.String()),
	TMBackupUPEId: t.Nullable(t.String()),
	TMVLANId: t.Nullable(t.String()),
	TMServiceInstanceId: t.Nullable(t.String()),
	TMNetworkDescription: t.Nullable(t.String()),
	TMHSBAServiceType: t.Nullable(t.String()),
	TMPrimaryUPEName: t.Nullable(t.String()),
	TMBackupUPEName: t.Nullable(t.String()),
	TMHSBANetworkId: t.Nullable(t.String()),
	TMVLANBandwidth: t.Nullable(t.String()),
	TMWaivePSTNFlag: t.Nullable(t.String()),
	TMVLANCommercialProductName: t.Nullable(t.String()),
	TMHSBANetworkServiceId: t.Nullable(t.String()),
	TMServingExchange: t.Nullable(t.String()),
	TMPortalPromotionName: t.Nullable(t.String()),
	TMPortalPromotionStartDate: t.Nullable(t.String()),
	TMPortalPromotionEndDate: t.Nullable(t.String()),
	TMIPTVBillingStartDate: t.Nullable(t.String()),
	TMVLANUpBandwidth: t.Nullable(t.String()),
	TMServiceTaxFlag: t.Nullable(t.String()),
	TMSecondaryServiceId: t.Nullable(t.String()),
	TMRGFlag: t.Nullable(t.String()),
	WebHostingDomain: t.Nullable(t.String()),
	TMRGVersion: t.Nullable(t.String()),
	TMEquipmentModel: t.Nullable(t.String()),
	TMEquipmentVendor: t.Nullable(t.String()),
	TMMeterialCode: t.Nullable(t.String()),
	TMEquipmentTechnology: t.Nullable(t.String()),
	TMRGMode: t.Nullable(t.String()),
	TMTCOPFlag: t.Nullable(t.String()),
	taasOrderId: t.Nullable(t.Number()),
	loginEmail: t.Nullable(t.String()),
	'ListOfTmAssetMgmt-AssetXaIntegration': t.Array(t.Object({})),
	'ListOfTmCutAssetMgmt-ServiceMeterIntegration': t.Array(
		t.Object({
			TMServiceTaxFlag: t.Nullable(t.String()),
			TMReservationId: t.Nullable(t.String()),
			ServicePointId: t.Nullable(t.String()),
			TMServiceInstanceId: t.Nullable(t.String()),
			ProductName: t.Nullable(t.String()),
			TMExchangeName: t.Nullable(t.String()),
			TMPremiseType: t.Nullable(t.String()),
			TMBTUPortNumber: t.Nullable(t.Number()),
			TMCPEVendor: t.Nullable(t.String()),
			TMAccessTechnology: t.Nullable(t.String()),
			TMCPEType: t.Nullable(t.String()),
			TMDPLocation: t.Nullable(t.String()),
			TMPremiseClass: t.Nullable(t.String()),
			TMBTUMaterialNumber: t.Nullable(t.Number()),
			TMSerialnumber: t.Nullable(t.String()),
			TMBTUTagging: t.Nullable(t.String()),
			TMMACAddress: t.Nullable(t.String()),
			ListOfTmCutAddressIntegration: t.Array(
				t.Object({
					Id: t.Nullable(t.String()),
					ApartmentNumber: t.Nullable(t.String()),
					City: t.Nullable(t.String()),
					Country: t.Nullable(t.String()),
					IntegrationId: t.Nullable(t.Number()),
					PostalCode: t.Nullable(t.Number()),
					State: t.Nullable(t.String()),
					StreetName: t.Nullable(t.String()),
					Section: t.Nullable(t.String()),
					TMAddressType: t.Nullable(t.String()),
					TMBuildingName: t.Nullable(t.String()),
					TMFloorNo: t.Nullable(t.String()),
					TMForeignAddrFlag: t.Nullable(t.String()),
					TMForeignCountry: t.Nullable(t.String()),
					TMForeignState: t.Nullable(t.String()),
					StreetType: t.Nullable(t.String()),
					TMPremiseType: t.Nullable(t.String())
				})
			)
		})
	),
	'ListOfTmCutAssetMgmt-ServiceMeterIntegrationAta': t.Array(t.Object({}))
});

export const taasServiceDetailsResSchema = t.Object({
	responseHeader: t.Object({
		rqUuid: t.Nullable(t.String()),
		requestId: t.Nullable(t.String()),
		status: t.Nullable(t.String()),
		statusCode: t.Nullable(t.Number()),
		errorCode: t.Nullable(t.Number()),
		errorMessage: t.Nullable(t.String()),
		errorDetail: t.Nullable(t.String()),
		errorPayload: t.Nullable(t.String())
	}),
	ListOfTmEaiServiceDetailsRetrieveReq: t.Object({
		'TmAssetMgmt-AssetIntegration': t.Array(tmAssetMgmtAssetIntegrationSchema)
	})
});

export type TaasServiceDetailsRes = Static<typeof taasServiceDetailsResSchema>;
