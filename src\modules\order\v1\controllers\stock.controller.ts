import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type CheckStockByListRes,
	checkStockByListReqSchema,
	checkStockByListResSchema
} from '../schemas/api/checkStockByList.schema';
import StockOrder from '../services/stock.service';

const stockV1Routes = new Elysia({ prefix: '/stock' })
	.resolve(() => {
		return {
			StockOrder: new StockOrder(randomUUID())
		};
	})
	.post(
		'/check',
		async (ctx): Promise<CheckStockByListRes> => {
			return await ctx.StockOrder.getDeviceStockStatusByList(ctx.body);
		},
		{
			detail: {
				description:
					'Check stock availability by SKU item number from MMAG. This API checks the stock status of a given item.<br><br><b>Backend:</b> MMAG',
				tags: ['Order']
			},
			body: checkStockByListReqSchema,
			response: {
				200: checkStockByListResSchema,
				400: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/reserve',
		async (ctx): Promise<CheckStockByListRes> => {
			return await ctx.StockOrder.getDeviceStockStatusByList(ctx.body);
		},
		{
			detail: {
				description:
					'Check stock availability by SKU item number from MMAG. This API checks the stock status of a given item.<br><br><b>Backend:</b> MMAG',
				tags: ['Order']
			},
			body: checkStockByListReqSchema,
			response: {
				200: checkStockByListResSchema,
				400: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default stockV1Routes;
