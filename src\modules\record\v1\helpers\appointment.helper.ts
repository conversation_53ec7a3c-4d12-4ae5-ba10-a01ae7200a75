import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2OrderReviewReq,
	Wso2OrderReviewRes
} from '../../../../integration/wso2/order/schemas/api/wso2OrderReview.schema';
import type { Wso2ServiceAccountRes } from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getAddOnsOrderId } from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type {
	AddOnsAppointmentSlotReq,
	AppointmentSlotHelperRes
} from '../schemas/api/addOns.schema';

class AppointmentRecordHelper {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(this.integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async appointmentSlotRecordHelper(
		req: AddOnsAppointmentSlotReq,
		segment: string,
		earliestStartDate: string,
		latestStartDate: string,
		orderId?: string
	): Promise<AppointmentSlotHelperRes> {
		const decryptedBillAccNo = await decrypt(req.EncryptedBillAccNo);

		let tmExchangeName = '';
		let tmPremiseType = '';
		let tmDPLocation = '';
		let tmAccessTechnology = '';
		let servicePointId = '';

		const wso2NonLightweightServiceAccountRes: Wso2ServiceAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				{
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue,
					SystemName: SystemNameEnum.NOVA,
					BillingAccountNo: decryptedBillAccNo
				},
				LightweightFlagEnum.NO
			);

		if (
			wso2NonLightweightServiceAccountRes?.Response?.ServiceAccount &&
			wso2NonLightweightServiceAccountRes?.Response.ServiceAccount.length > 0
		) {
			for (const sa of wso2NonLightweightServiceAccountRes.Response
				.ServiceAccount ?? []) {
				for (const moli of sa.ServiceAccountMoli ?? []) {
					servicePointId = sa.ServicePointId ?? '';
					tmExchangeName =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']?.TMExchangeName ??
						'';
					tmPremiseType =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']?.TMPremiseType ??
						'';
					tmDPLocation =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']?.TMDPLocation ??
						'';
					tmAccessTechnology =
						moli?.['TmCutAssetMgmt-ServiceMeterIntegration']
							?.TMAccessTechnology ?? '';
					break;
				}
			}
		}

		const actualOrderId: string = orderId ?? (await getAddOnsOrderId('MW6'));

		const wso2Req: Wso2OrderReviewReq = {
			OrderReviewRequest: {
				AccountId: '',
				OrderType: 'Modify',
				SegmentGroup: segment,
				TransactionId: actualOrderId,
				OrderId: actualOrderId,
				QueryAppointmentSlotsFlag: 'Y',
				ReservationId: '',
				SystemName: SystemNameEnum.NOVA,
				ExchangeName: tmExchangeName,
				CustomerAccount: {
					AccountId: req.AccountNo,
					ContactId: req.ContactId
				},
				QueryAppointmentSlots: {
					ServicePointID: servicePointId,
					TechnologyType: tmAccessTechnology,
					DPLocation: tmDPLocation,
					PremiseType: tmPremiseType,
					EarliestStartDate: earliestStartDate,
					LatestStartDate: latestStartDate,
					AppointmentProducts: {
						AppointmentProduct: [
							{
								Name: req.DeviceName,
								PartNumber: req.PartNumber,
								Action: 'add'
							}
						]
					}
				}
			}
		};

		const wso2Res: Wso2OrderReviewRes =
			await this.mwIntegration.Wso2OrderIntegration.getWso2OrderReview(wso2Req);

		return {
			OrderId: actualOrderId,
			RNORegion:
				wso2Res.Response.OrderReviewResponse.GetAppointmentSlots.RNORegion,
			AppointmentSlots:
				wso2Res.Response.OrderReviewResponse.GetAppointmentSlots.AppointmentSlot.map(
					slot => {
						return {
							AppointmentId: slot.AppointmentId,
							SlotStart: slot.SlotStart,
							SlotEnd: slot.SlotEnd
						};
					}
				)
		};
	}
}

export default AppointmentRecordHelper;
