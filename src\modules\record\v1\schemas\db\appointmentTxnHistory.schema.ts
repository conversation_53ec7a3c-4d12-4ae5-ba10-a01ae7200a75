import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const appointmentTxnHistoryTableSchema = pgTable(
	'appointment_txn_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		OrderId: text('order_id').notNull(),
		Action: text('action').notNull(),
		Status: text('status').notNull(),
		ReserveId: text('reserve_id'),
		Reason: text('reason'),
		CurrentApptDate: text('current_appt_date'),
		UpdatedApptDate: text('updated_appt_date'),
		Source: text('source'),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
	}
);
