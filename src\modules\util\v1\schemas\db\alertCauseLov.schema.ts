import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const alertCauseLovTableSchema = pgTable('alert_cause_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Name: text('name').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAlertCauseLov = typeof alertCauseLovTableSchema.$inferSelect;
