import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const tmForceStatusLovTableSchema = pgTable('tm_force_status_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Category: text('category').notNull(),
	ActivityType: text('activity_type'),
	Vertical: text('vertical').notNull(),
	Description: text('description').notNull(),
	HorizontalValue: text('horizontal_value').notNull(),
	HorizontalDescription: text('horizontal_description').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectTmForceStatusLov =
	typeof tmForceStatusLovTableSchema.$inferSelect;
