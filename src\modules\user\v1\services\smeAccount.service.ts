import randomString from 'random-string-gen';
import { envConfig } from '../../../../config/env.config';
import { StateEnum } from '../../../../enum/address.enum';
import { EmailEnum } from '../../../../enum/notification.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type {
	BizCCRegistrationReq,
	BizCCRegistrationRes
} from '../../../../integration/biz/schemas/api/cloudConnect.schema';
import type {
	BizECommerceRegistrationReq,
	BizECommerceRegistrationRes
} from '../../../../integration/biz/schemas/api/eCommerce.schema';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import type {
	Wso2DMSReq,
	Wso2DMSRes
} from '../../../../integration/wso2/user/schemas/api/wso2DMS.schema';
import { UE_ERROR } from '../../../../middleware/error';
import { BRNPatterns, getEnumKeyByValue } from '../../../../shared/common';
import type {
	CloudConnectRegistrationReq,
	CloudConnectRegistrationRes,
	CreditPurchase,
	CreditUtilization,
	DmsCreditScoreRes,
	ECommerceRegistrationReq,
	ECommerceRegistrationRes,
	SmeEmail
} from '../schemas/api/smeAccount.schema';

class SmeAccount {
	private mwIntegration: MwIntegration;
	private integrationId: string;

	constructor(integrationId: string) {
		this.mwIntegration = new MwIntegration(integrationId);
		this.integrationId = integrationId;
	}

	async registerCloudConnect(
		req: CloudConnectRegistrationReq
	): Promise<CloudConnectRegistrationRes> {
		const getBizUserIntegration = this.mwIntegration.BizUserIntegration;
		const auth = await getBizUserIntegration.getCloudConnectAccessToken();

		//Check BRN format and type
		let brnFormat = 'BRN Other';
		let brnType = 'Others';

		const matchedPattern = BRNPatterns.find(p =>
			p.pattern.test(req.TenantRegistrationNumber)
		);

		if (matchedPattern) {
			brnFormat = matchedPattern.format;
			brnType = matchedPattern.type;
		}

		//Set order number
		//const orderNumber: string = 'UCSB-***********'; //UCSB-yyyymmddordercounter
		const orderNumber: string = `UCSB-${randomString(9)}`;

		//Register the tenant
		const bodyRequest: BizCCRegistrationReq = {
			packageName: req.PackageName,
			planName: req.PlanName,
			monthlyFee: req.MonthlyFee,
			tenantName: req.TenantName,
			contactIdNumber: req.ContactIdNumber ?? null,
			tenantPhoneNumber: req.TenantPhoneNumber,
			adminEmail: req.AdminEmail,
			firstName: req.FirstName,
			lastName: req.LastName,
			companyOwnerName: req.CompanyOwnerName,
			tenantRegistrationNumber: req.TenantRegistrationNumber,
			tenantTierName: req.TenantTierName,
			domainName: req.DomainName,
			customerIDType: req.CustomerIDType,
			segmentGroup: req.SegmentGroup,
			segmentSubGroup: req.SegmentSubGroup,
			industryCode: req.IndustryCode,
			brnFormat: brnFormat,
			brnType: brnType,
			relationshipCode: req.RelationshipCode,
			contractingParty: req.ContractingParty,
			foreignTelco: req.ForeignTelco,
			preferredLanguage: req.PreferredLanguage,
			billEmailAddress: req.BillEmailAddress,
			addressType: req.AddressType,
			unitNumber: req.UnitNumber,
			floorNumber: req.FloorNumber,
			block: req.Block,
			buildingName: req.BuildingName,
			streetName: req.StreetName,
			streetType: req.StreetType,
			section: req.Section,
			postCode: req.PostCode,
			city: req.City,
			state: req.State.toString(),
			country: req.Country,
			serviceId: req.ServiceId,
			commissionToId: req.CommissionToId,
			commissionToName: req.CommissionToName,
			orderNumber: orderNumber
		};

		if (auth.accessToken == null || !auth.accessToken) {
			throw new UE_ERROR(
				'Access Token is not found',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{ integrationId: this.integrationId, response: null }
			);
		}

		const cloudConnRes: BizCCRegistrationRes =
			await getBizUserIntegration.cloudConnectRegistration(
				bodyRequest,
				auth.accessToken
			);

		if (
			cloudConnRes.errors ||
			cloudConnRes.errors === undefined ||
			cloudConnRes.errors == null
		) {
			const emailRequest: SmeEmail = {
				RefNumber: bodyRequest.orderNumber,
				PlanName: bodyRequest.planName,
				MonthlyFee: Number.parseFloat(
					bodyRequest.monthlyFee.toString()
				).toFixed(2),
				CompanyName: bodyRequest.tenantName,
				OwnerName: bodyRequest.companyOwnerName,
				PicName: `${bodyRequest.firstName} ${bodyRequest.lastName}`,
				PicIdNumber: bodyRequest.contactIdNumber ?? '',
				SubDomain: bodyRequest.domainName,
				ContactNumber: bodyRequest.tenantPhoneNumber,
				IdNumber: bodyRequest.tenantRegistrationNumber,
				IdType: bodyRequest.customerIDType,
				EmailAddress: bodyRequest.adminEmail,
				UnitNo: bodyRequest.unitNumber,
				FloorNo: bodyRequest.floorNumber,
				Block: bodyRequest.block,
				Building: bodyRequest.buildingName,
				StreetType: bodyRequest.streetType,
				StreetName: bodyRequest.streetName,
				Section: bodyRequest.section,
				PostCode: bodyRequest.postCode ?? '',
				City: bodyRequest.city,
				State: bodyRequest.state,
				Country: bodyRequest.country
			};

			//Send email
			const emailBody: string =
				await this.getSMEEmailBodyTemplate(emailRequest);
			const wso2EmailReq: Wso2EmailReq = {
				to: req.AdminEmail,
				from: EmailEnum.FROM_NOREPLY,
				subject: `${EmailEnum.CLOUD_CONNECT_SUBJECT}[${bodyRequest.orderNumber}]`,
				body: emailBody
			};

			await this.mwIntegration
				.getWso2NotificationIntegration()
				.getWso2SendEmail(
					wso2EmailReq,
					bodyRequest.tenantRegistrationNumber,
					'CLOUD_CONNECT'
				);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: cloudConnRes
		};
	}

	async registerECommerce(
		req: ECommerceRegistrationReq
	): Promise<ECommerceRegistrationRes> {
		const getBizUserIntegration = this.mwIntegration.BizUserIntegration;
		const auth = await getBizUserIntegration.eCommerceSSOLogin();

		//Check BRN format and type
		let brnFormat = 5;
		let brnType = 7;

		const matchedPattern = BRNPatterns.find(p =>
			p.pattern.test(req.CustomerIdNumber)
		);

		if (matchedPattern) {
			brnFormat = matchedPattern.integerFormat;
			brnType = matchedPattern.integerType;
		}

		//Set order number
		//const orderNumber: string = 'UPCB-20240702010'; // UPCB-yyyymmddordercounter
		const orderNumber: string = `UPCB-${randomString(9)}`;

		// Get state by number
		const stateNumber: number = Number(getEnumKeyByValue(StateEnum, req.State));

		//Register the tenant
		const bodyRequest: BizECommerceRegistrationReq = {
			company_name: req.CompanyName,
			customer_id_type: req.CustomerIdType,
			segment_group: 'SME',
			owner_name: req.OwnerName ?? null,
			block: req.Block ?? null,
			email_street_type: req.EmailStreetType ?? null,
			segment_sub_group: req.SegmentSubGroup,
			industry_code: req.IndustryCode,
			brn_format: brnFormat,
			brn_type: brnType,
			relationship_code: req.RelationshipCode,
			contracting_party: req.ContractingParty,
			foreign_telco: req.ForeignTelco,
			customer_id_number: req.CustomerIdNumber,
			first_name: req.FirstName,
			last_name: req.LastName,
			contact_id_number: req.ContactIdNumber ?? null,
			preferred_language: req.PreferredLanguage,
			mobile_phone: req.MobilePhone,
			bill_email_address: req.BillEmailAddress,
			address_type: req.AddressType,
			unit_number: req.UnitNumber,
			floor_number: req.FloorNumber,
			building_name: req.BuildingName,
			street_type: req.StreetType,
			street_name: req.StreetName,
			section: req.Section,
			post_code: req.PostCode,
			city: req.City,
			state: stateNumber,
			country: 1,
			training_plan: req.TrainingPlan,
			existing_tm_customer: req.ExistingTmCustomer,
			waiver: req.Waiver,
			commission_to_id: req.CommissionToId ?? null,
			commission_to_name: req.CommissionToName ?? null,
			pack_code: req.PackCode,
			business_model: req.BusinessModel,
			service_id: req.ServiceId,
			email: req.Email,
			telephone: req.Telephone,
			order_number: orderNumber,
			PlanName: req.PlanName,
			MonthlyFee: req.MonthlyFee
		};
		const ecommerceRes: BizECommerceRegistrationRes =
			await getBizUserIntegration.eCommerceRegistration(
				bodyRequest,
				auth?.data?.access_token ?? ''
			);

		if (ecommerceRes?.invalidFields == null) {
			const emailRequest: SmeEmail = {
				RefNumber: bodyRequest.order_number,
				PlanName: bodyRequest.PlanName,
				MonthlyFee: Number.parseFloat(
					bodyRequest.MonthlyFee.toString()
				).toFixed(2),
				CompanyName: bodyRequest.company_name,
				OwnerName: bodyRequest.owner_name ?? '',
				PicName: `${bodyRequest.first_name} ${bodyRequest.last_name}`,
				PicIdNumber: bodyRequest.contact_id_number ?? '',
				SubDomain: '',
				ContactNumber: bodyRequest.mobile_phone,
				IdNumber: bodyRequest.customer_id_number,
				IdType: bodyRequest.customer_id_type,
				EmailAddress: bodyRequest.bill_email_address,
				UnitNo: bodyRequest.unit_number,
				FloorNo: bodyRequest.floor_number,
				Block: bodyRequest.block ?? '',
				Building: bodyRequest.building_name,
				StreetType: bodyRequest.email_street_type ?? '',
				StreetName: bodyRequest.street_name,
				Section: bodyRequest.section,
				PostCode: bodyRequest.post_code ?? '',
				City: bodyRequest.city,
				State: bodyRequest.state.toString(),
				Country: 'MALAYSIA'
			};
			//Send email
			const emailBody: string =
				await this.getSMEEmailBodyTemplate(emailRequest);
			const wso2EmailReq: Wso2EmailReq = {
				to: req.BillEmailAddress,
				from: EmailEnum.FROM_NOREPLY,
				subject: `${EmailEnum.ECOMMERCE_SUBJECT}[${bodyRequest.order_number}]`,
				body: emailBody
			};
			await this.mwIntegration
				.getWso2NotificationIntegration()
				.getWso2SendEmail(
					wso2EmailReq,
					bodyRequest.customer_id_number,
					'ECOMMERCE'
				);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: ecommerceRes
		};
	}

	private async getSMEEmailBodyTemplate(body: SmeEmail): Promise<string> {
		const baseUrl: string = envConfig().CLOUD_CONNECT_ECOMMERCE_TEMPLATE;
		const params = new URLSearchParams(body);
		const queryString: string = params.toString();
		const emailUrl: string = `${baseUrl}?${queryString}`;
		const emailBody: string =
			await this.mwIntegration.EmailTemplateIntegration.getEmailBodyTemplate(
				emailUrl
			);
		return emailBody;
	}

	async getDmsCreditScore(serviceId: string): Promise<DmsCreditScoreRes> {
		const wso2DMSCreditScoreReq: Wso2DMSReq = {
			requestHeader: {
				requestId: randomString(14),
				eventName: 'evOXEeDMSCreditUtilizationSiebelNOVADB'
			},
			service: {
				serviceId: serviceId
			}
		};

		const wso2DMSCreditScoreRes: Wso2DMSRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2DmsCreditScore(
				wso2DMSCreditScoreReq
			);

		let productName = '';
		let creditUtilization: CreditUtilization[] = [];
		let creditPurchase: CreditPurchase[] = [];

		if (
			wso2DMSCreditScoreRes.service?.adCreditUtilization?.[0]?.utilId &&
			wso2DMSCreditScoreRes.service?.adCreditUtilization?.[0]?.item
		) {
			creditUtilization = wso2DMSCreditScoreRes.service?.adCreditUtilization
				?.map(item => {
					return {
						UtilId: item?.utilId,
						Item: item?.item,
						AdCreditUtilized: item?.adCreditUtilized,
						FreeAdCreditUsed: item?.freeAdCreditUsed,
						PaidAdCreditUsed: item?.paidAdCreditUsed,
						Date: item?.date ?? '',
						UtilizationStatus: item?.UtilizationStatus
					};
				})
				.sort((a, b) => b?.Date.localeCompare(a?.Date));

			if (wso2DMSCreditScoreRes.service?.adCreditPurchase) {
				creditPurchase = wso2DMSCreditScoreRes.service?.adCreditPurchase
					.map(item => {
						return {
							Product: item?.product,
							PurchaseId: item?.purchId,
							AdCreditAmount: item?.adCreditAmount,
							PurchaseDate: item?.purchaseDate ?? '',
							ExpiryDate: item?.expiryDate,
							Balance: item?.balance,
							Type: item?.type,
							OrderNumber: item?.orderNumber
						};
					})
					.sort((a, b) => b?.PurchaseDate.localeCompare(a?.PurchaseDate));

				productName =
					creditPurchase.filter(
						item =>
							item?.Type === 'Pack' ||
							item?.Type === 'Free' ||
							item?.Type === 'Paid'
					)[0].Product ?? '';
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				ProductName: productName,
				Service: {
					DmsServiceId: wso2DMSCreditScoreRes.service?.dmsServiceId ?? '',
					ServiceStatus: wso2DMSCreditScoreRes.service?.serviceStatus ?? '',
					ServiceStartDate:
						wso2DMSCreditScoreRes.service?.serviceStartDate ?? '',
					ServiceEndDate: wso2DMSCreditScoreRes.service?.serviceEndDate ?? '',
					CampaignManagerProfile: {
						FirstName:
							wso2DMSCreditScoreRes.service?.cmpgnMgrProfile?.firstName ?? '',
						LastName:
							wso2DMSCreditScoreRes.service?.cmpgnMgrProfile?.lastName ?? '',
						Email: wso2DMSCreditScoreRes.service?.cmpgnMgrProfile?.email ?? ''
					},
					AdCreditUtilization: creditUtilization,
					AdCreditPurchase: creditPurchase
				}
			}
		};
	}
}

export default SmeAccount;
