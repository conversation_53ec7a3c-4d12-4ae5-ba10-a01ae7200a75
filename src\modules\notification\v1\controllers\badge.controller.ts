import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type BadgeByBillingAccountRes,
	badgeByBillingAccountResSchema
} from '../schemas/api/badge.schema';
import BadgeNotification from '../services/badge.service';

const badgeV1Routes = new Elysia({ prefix: '/badge' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			BadgeNotification: new BadgeNotification(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/billing-accounts',
		async (ctx): Promise<BadgeByBillingAccountRes> => {
			return await ctx.BadgeNotification.getBadgeNotificationsByBillingAccounts();
		},
		{
			response: {
				200: badgeByBillingAccountResSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Get the list of badge notifications for each billing account',
				tags: ['Notification']
			}
		}
	);

export default badgeV1Routes;
