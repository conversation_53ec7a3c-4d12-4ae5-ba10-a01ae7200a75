import { type Static, t } from 'elysia';
import {
	SmeIdTypeEnum,
	SmeUserRoleEnum
} from '../../../../../../enum/user.enum';
import { baseResponseSchema } from '../../../../../../shared/schemas/api/responses.schema';
import { actionResponseSchema } from '../../../../util/schemas/responses';

export const getBrnIdResSchema = t.Nullable(
	t.Object({
		IdType: t.Nullable(t.String({ enum: Object.values(SmeIdTypeEnum) })),
		IdValueMasked: t.Nullable(t.String({ examples: ['202********01'] })),
		IdToken: t.Nullable(
			t.String({
				examples: [
					'60a5a12d15c904248bb3c1da15c337e3ea965e05c59828764f5fa29c2bb70a99'
				]
			})
		),
		Role: t.Nullable(t.String({ examples: ['Super Admin'] })),
		IsIdVerified: t.Nullable(t.<PERSON>())
	})
);

export type BrnIdSchema = Static<typeof getBrnIdResSchema>;

// body request & response for endpoint: /sme/add-user
export const addUserSmeProfileReqSchema = t.Object({
	BrnId: t.String({
		minLength: 64,
		examples: [
			'60a5a12d15c904248bb3c1da15c337e3ea965e05c59828764f5fa29c2bb70a99'
		]
	}),
	CredentialId: t.String({ minLength: 15, examples: ['4wrpatxxazk6ih5'] })
});

export type AddUserSmeProfileReq = Static<typeof addUserSmeProfileReqSchema>;

export const addUserSmeProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: actionResponseSchema
	},
	{ description: 'New User was successfully added into selected SME Profile' }
);

export type AddUserSmeProfileRes = Static<typeof addUserSmeProfileResSchema>;

// body request & response for endpoint: /sme/create-profile
export const createSmeProfileReqSchema = t.Object({
	IdType: t.Enum(SmeIdTypeEnum),
	IdValue: t.String({
		minLength: 5,
		examples: ['PG0344816-D', '1-6RY3907', '201901000005']
	}),
	Role: t.Enum(SmeUserRoleEnum)
});

export type CreateSmeProfileReq = Static<typeof createSmeProfileReqSchema>;

export const createSmeProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: actionResponseSchema
	},
	{ description: 'SME Profile was successfully created' }
);

export type CreateSmeProfileRes = Static<typeof createSmeProfileResSchema>;

// body request & response for endpoint: /sme/delete-brn-id
export const deleteBrnIdReqSchema = t.Object({
	BrnId: t.String({
		minLength: 64,
		examples: [
			'60a5a12d15c904248bb3c1da15c337e3ea965e05c59828764f5fa29c2bb70a99'
		]
	})
});

export type DeleteBrnIdReqSchema = Static<typeof deleteBrnIdReqSchema>;

export const deleteBrnIdResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: actionResponseSchema
	},
	{ description: 'Selected BRN Id was successfully deleted from the profile' }
);

export type DeleteBrnIdResSchema = Static<typeof deleteBrnIdResSchema>;

// body request & response for endpoint: /sme/update-profile
export const updateSmeProfileReqSchema = t.Object({
	BrnId: t.String({
		minLength: 64,
		examples: [
			'60a5a12d15c904248bb3c1da15c337e3ea965e05c59828764f5fa29c2bb70a99'
		]
	}),
	Role: t.Enum(SmeUserRoleEnum),
	Email: t.String({ minLength: 10, examples: ['<EMAIL>'] }),
	MobileNumber: t.String({ minLength: 10, examples: ['0123456789'] }),
	DeptId: t.Optional(t.Integer({ examples: [1] }))
});

export type UpdateSmeProfileReq = Static<typeof updateSmeProfileReqSchema>;

export const updateSmeProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: actionResponseSchema
	},
	{ description: 'SME Profile was successfully updated' }
);

export type UpdateSmeProfileRes = Static<typeof updateSmeProfileResSchema>;

// body request & response for endpoint: add-dept
export const addSmeDeptReqSchema = t.Object({
	BrnId: t.String({
		minLength: 64,
		examples: [
			'60a5a12d15c904248bb3c1da15c337e3ea965e05c59828764f5fa29c2bb70a99'
		]
	}),
	DeptName: t.String({ minLength: 2, examples: ['Finance', 'HR', 'Admin'] })
});

export type AddSmeDeptReq = Static<typeof addSmeDeptReqSchema>;

export const addSmeDeptResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: actionResponseSchema
	},
	{ description: 'New SME Department was successfully added' }
);

export type AddSmeDeptRes = Static<typeof addSmeDeptResSchema>;

// body response for endpoint: list-dept
const selectSmeDeptSchema = t.Array(
	t.Nullable(
		t.Object({
			DeptId: t.Nullable(t.Integer({ examples: [1] })),
			DeptName: t.Nullable(t.String({ examples: ['Finance', 'HR', 'Admin'] }))
		})
	)
);

export type SelectSmeDeptApi = Static<typeof selectSmeDeptSchema>;

export const smeDeptResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: selectSmeDeptSchema
	},
	{ description: 'SME Department list successfully retrieved' }
);

export type SmeDeptRes = Static<typeof smeDeptResSchema>;

// body request for endpoint: list-dept, list-user
export const smeProfileReqSchema = t.Object({
	BrnId: t.String({
		minLength: 64,
		examples: [
			'60a5a12d15c904248bb3c1da15c337e3ea965e05c59828764f5fa29c2bb70a99'
		]
	})
});

export type SmeProfileReq = Static<typeof smeProfileReqSchema>;
