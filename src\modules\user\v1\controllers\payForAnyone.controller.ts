import { randomUUID } from 'node:crypto';
import { Elysia, t } from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type PfaBillingAccountDetailsRes,
	pfaBillingAccountDetailsResSchema
} from '../schemas/api/payForAnyone.schema';
import PayForAnyone from '../services/payForAnyone.service';

const pfaBillingAccountV1Routes = new Elysia({ prefix: '/pay-for-anyone' })
	.resolve(() => {
		return {
			PayForAnyone: new PayForAnyone(randomUUID())
		};
	})
	.get(
		'/billing-details',
		async (ctx): Promise<PfaBillingAccountDetailsRes> => {
			return await ctx.PayForAnyone.getPfaBillingAccountDetails(
				ctx.query.BillingAccountNo
			);
		},
		{
			query: t.Object({
				BillingAccountNo: t.String()
			}),
			response: {
				200: pfaBillingAccountDetailsResSchema,
				403: errorBaseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					"Retrieve a customer's billing accounts details from WSO2 without any authentication.<br><br><b>Backend System:</b> NOVA SIEBEL",
				tags: ['User']
			}
		}
	);

export default pfaBillingAccountV1Routes;
