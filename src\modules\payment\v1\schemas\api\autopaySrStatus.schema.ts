import { type Static, t } from 'elysia';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const checkSrStatusReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: 'fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-',
		minLength: 1
	}),
	SystemName: t.Enum(SystemNameEnum)
});

export type CheckSrStatusReq = Static<typeof checkSrStatusReqSchema>;

export const checkSrStatusResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			SRNumber: t.Nullable(
				t.String({
					examples: ['SR123456'],
					description: 'Null means there is open SR'
				})
			)
		})
	},
	{ description: 'SR number successfully retrieved.' }
);

export type CheckSrStatusRes = Static<typeof checkSrStatusResSchema>;
