import { type Static, t } from 'elysia';
import { AppointmentActionEnum } from '../../../../../enum/tracker.enum';
import { SystemNameEnum } from '../../../../../enum/wso2.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const orderInformationSchema = t.Object({
	OrderId: t.String({ examples: ['1-A253WST4'] }),
	OrderPlanName: t.String({ examples: ['Unifi 100Mbps'] }),
	CreatedDate: t.String({ examples: ['23 December 2024'] }),
	CreatedTime: t.String({ examples: ['08:20 AM'] }),
	Source: t.String({ examples: ['Subscription Channel: Retails'] })
});

export type OrderInformation = Static<typeof orderInformationSchema>;

export const serviceInformationSchema = t.Nullable(
	t.Object({
		//* Remove optional when confirm order address structure
		Unit: t.Optional(t.String({ examples: ['1'] })),
		Floor: t.Optional(t.String({ examples: ['1'] })),
		BuildingName: t.Optional(t.String({ examples: ['Anggun'] })),
		StreetType: t.Optional(t.String({ examples: ['JALAN'] })),
		StreetName: t.Optional(t.String({ examples: ['Samudera'] })),
		Section: t.Optional(t.String({ examples: ['Seksyen 4'] })),
		City: t.Optional(t.String({ examples: ['Shah Alam'] })),
		State: t.Optional(t.String({ examples: ['MELAKA'] })),
		Postcode: t.Optional(t.String({ examples: ['12345'] })),
		Country: t.Optional(t.String({ examples: ['Malaysia'] })),
		RNORegion: t.Optional(t.String({ examples: ['ZONE RAWANG'] })),
		ServiceID: t.Optional(t.String({ examples: ['test@unifi'] })),
		TermAndCondition: t.Optional(
			t.String({ examples: ['https://test.com.my/tnc.pdf'] })
		)
	})
);

export type ServiceInformation = Static<typeof serviceInformationSchema>;

const orderDetailsServiceInfo = t.Object({
	serviceInformation: t.Nullable(serviceInformationSchema),
	orderInformation: t.Nullable(orderInformationSchema)
});

export type OrderDetailsServiceInfo = Static<typeof orderDetailsServiceInfo>;

const orderObjSchema = t.Object({
	Title: t.String(),
	Category: t.String(),
	SubCategory: t.String(),
	Status: t.String({ examples: ['Submitted'] }),
	SiebelOrderStatus: t.Nullable(t.String({ examples: ['Submitted'] })),
	CreatedDate: t.String({ examples: ['12/13/2019 10:00:00'] }),
	OrderId: t.Nullable(t.String({ examples: ['UFNH-23A4231RD'] })),
	OrderNumber: t.Nullable(t.String({ examples: [''] })),
	DiceOrderId: t.Nullable(t.String()),
	EncryptedBillAccNo: t.Nullable(t.String({ examples: [''] })),
	SystemName: t.String({ examples: ['NOVA'] }),
	OrderType: t.String({ examples: ['New Install'] }),
	SubOrderType: t.String({ examples: [''] }),
	IsTruckRoll: t.Boolean(),
	Product: t.Nullable(
		t.Object({
			Name: t.String({ examples: ['Unifi 100Mbps'] }),
			Category: t.String({ examples: ['High Speed Internet'] })
		})
	),
	ServiceInfo: t.Optional(serviceInformationSchema),
	OrderCategory: t.Optional(
		t.String({
			examples: ['TV'],
			description:
				'Categorized value derived from DiceOrderId prefix. Defaults to "Other" if not match.'
		})
	)
});

export type OrderObj = Static<typeof orderObjSchema>;

export const orderListObjSchema = t.Array(orderObjSchema);

export type OrderListObj = Static<typeof orderListObjSchema>;

export const orderListResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			OrderList: orderListObjSchema
		})
	},
	{
		description: 'Order list successfully retrieved.'
	}
);

export type OrderListRes = Static<typeof orderListResSchema>;

export const orderDetailsReqSchema = t.Object({
	SystemName: t.Enum(SystemNameEnum),
	IsTruckRoll: t.Boolean(),
	ReferenceStatus: t.String({
		example: 'Submitted',
		minLength: 1
	}),
	OrderId: t.Optional(
		t.String({
			example: '1-1AKDHEMPUY',
			description: 'Siebel Order ID',
			minLength: 1
		})
	),
	OrderNumber: t.Optional(
		t.String({
			example: '1-234546456',
			description: 'Siebel Order Number'
		})
	),
	DiceOrderId: t.Optional(
		t.String({
			example: '1-234546456',
			description: 'Digital Order ID'
		})
	)
});

export type OrderDetailsReq = Static<typeof orderDetailsReqSchema>;

const caseDetails = t.Object({
	AppointmentDate: t.Optional(
		t.String({
			examples: ['10 October 2024']
		})
	),
	AppointmentTime: t.Optional(t.String({ examples: ['11:00 AM'] })),
	AppointmentEndDate: t.Optional(
		t.String({
			examples: ['10 October 2024']
		})
	),
	AppointmentEndTime: t.Optional(
		t.String({
			examples: ['11:00 AM']
		})
	),
	DefferedAppointmentDate: t.Optional(
		t.String({
			examples: ['10 October 2024']
		})
	),
	DefferedAppointmentTime: t.Optional(
		t.String({
			examples: ['11:00 AM']
		})
	)
});

export type CaseDetails = Static<typeof caseDetails>;

const technicianDetailsSchema = t.Optional(
	t.Nullable(
		t.Object({
			ProfilePicture: t.Optional(
				t.Nullable(
					t.String({
						examples: ['/q4/ASG9Hasjdkgl/asg4e2q34'],
						description: 'A blob data'
					})
				)
			),
			TechnicianName: t.Optional(
				t.Nullable(
					t.String({
						examples: ['Test Technician']
					})
				)
			),
			PhoneNumber: t.Optional(
				t.Nullable(
					t.String({
						examples: ['0123456789']
					})
				)
			),
			Latitude: t.Optional(t.Nullable(t.String({ examples: ['3.1243461'] }))),
			Longitude: t.Optional(
				t.Nullable(
					t.String({
						examples: ['101.82375534']
					})
				)
			),
			ETTA: t.Optional(
				t.Nullable(
					t.String({
						examples: ['10/10/2024 10:30:00']
					})
				)
			)
		})
	)
);

export type TechnicianDetails = Static<typeof technicianDetailsSchema>;

const reportItem = t.Object({
	OperationFlag: t.Optional(t.Nullable(t.Boolean())),
	Title: t.String({
		examples: ['On Site'],
		description: 'Ticket Status'
	}),
	Description: t.String({
		examples: [
			'Our technician is on site and your case is in progress of being resolved'
		],
		description: 'Ticket Status Description'
	}),
	Timestamp: t.String({
		examples: ['10 October 2024 , 10:28 AM']
	})
});

export type ReportItem = Static<typeof reportItem>;

const reportItemList = t.Array(reportItem);

export type ReportItemList = Static<typeof reportItemList>;

export const orderDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			TicketStatusDescription: t.String({
				examples: ['Your case is currently being addressed']
			}),
			Status: t.String({ examples: ['Scheduled'] }),
			CaseDetails: caseDetails,
			OrderDetails: t.Nullable(orderInformationSchema),
			ServiceInformation: t.Nullable(serviceInformationSchema),
			TechnicianDetails: t.Nullable(technicianDetailsSchema),
			ReportStatus: reportItemList
		})
	},
	{
		description: 'Order details successfully retrieved.'
	}
);

export type OrderDetailsRes = Static<typeof orderDetailsResSchema>;

export const tmForceAcceptanceFormReqSchema = t.Object({
	OrderNumber: t.String({ example: 'UFNH-23A4231RD', minLength: 1 })
});

export type TmForceAcceptanceFormReq = Static<
	typeof tmForceAcceptanceFormReqSchema
>;

export const appointmentSlotReqSchema = t.Object({
	SystemName: t.Enum(SystemNameEnum),
	OrderNumber: t.String({ example: ['1-1234556789'] }),
	RNORegion: t.String({ example: ['ZONE BATU PAHAT'] })
});

export type AppointmentSlotReq = Static<typeof appointmentSlotReqSchema>;

export const appointmentSlotResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			AppointmentSlots: t.Array(
				t.Object({
					AppointmentId: t.Nullable(
						t.String({ examples: ['S2A-191212E9E554'] })
					),
					SlotStart: t.Nullable(
						t.String({ examples: ['12/13/2019 10:00:00'] })
					),
					SlotEnd: t.Nullable(t.String({ examples: ['12/13/2019 10:00:00'] }))
				})
			)
		})
	},
	{
		description: 'Appointment slot successfully retrieved.'
	}
);

export type AppointmentSlotRes = Static<typeof appointmentSlotResSchema>;

export const validateOrderIdReqSchema = t.Object({
	OrderId: t.String({ example: '1-1D63B42', minLength: 1 })
});

export type ValidateOrderIdReq = Static<typeof validateOrderIdReqSchema>;

export const validateOrderIdResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		SystemName: t.String({ examples: ['NOVA'] }),
		OrderStatus: t.String({ examples: ['Processing'] }),
		InstallationActStat: t.String({ examples: ['Scheduled'] }),
		InstallationActTime: t.String({ examples: ['12/13/2019 10:00:00'] }),
		SBLOrderID: t.String({ examples: ['1-1D63B42'] }),
		OrderNumber: t.String({ examples: ['1-132075126789'] }),
		OrderType: t.String({ examples: ['New Install'] }),
		OrderPlanName: t.String({ examples: ['Unifi 100Mbps'] }),
		ServiceID: t.String({ examples: ['test@unifi'] }),
		CreatedDate: t.String()
	})
});

export type ValidateOrderIdRes = Static<typeof validateOrderIdResSchema>;

export const appointmentUpdateReqSchema = t.Object({
	OrderId: t.String({ examples: ['1-1D63B42'] }),
	OrderNumber: t.String({ examples: ['1-132075126789'] }),
	SystemName: t.Enum(SystemNameEnum),
	CurrentAppointmentDate: t.Optional(
		t.String({ example: '12/13/2019 10:00:00' })
	),
	Action: t.Enum(AppointmentActionEnum),
	UpdateAppointmentInfo: t.Optional(
		t.Object({
			Reason: t.String({
				examples: ['UNIFI-APP operation: Change Appointment']
			}),
			Remark: t.String({
				examples: [
					'UNIFI-APP operation: Change Appointment to ${dateSelectedFromCalender}'
				]
			}),
			NewAppointmentId: t.String({ examples: ['S2A-191212E9E554'] }),
			NewSlotStart: t.String({ examples: ['12/13/2019 10:00:00'] }),
			NewSlotEnd: t.String({ examples: ['12/13/2019 10:00:00'] })
		})
	)
});

export type AppointmentUpdateReq = Static<typeof appointmentUpdateReqSchema>;
