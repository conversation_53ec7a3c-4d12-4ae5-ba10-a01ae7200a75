import { type Static, t } from 'elysia';

export const omgDisneyChangeMobileReqSchema = t.Object({
	accountType: t.String(),
	accountId: t.String(),
	ottTxnId: t.Integer(),
	mode: t.String(),
	oldMobileNo: t.String(),
	newMobileNo: t.String()
});

export type OmgDisneyChangeMobileReq = Static<
	typeof omgDisneyChangeMobileReqSchema
>;

export const omgDisneyChangeMobileResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String(),
	nextAllowDate: t.String()
});

export type OmgDisneyChangeMobileRes = Static<
	typeof omgDisneyChangeMobileResSchema
>;
