import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { SmeIdTypeEnum } from '../../../../../enum/user.enum';
import { UE_ERROR } from '../../../../error';
import {
	type SelectIdentification,
	identificationDbSchema
} from '../../../identity/v1/schemas/models/identity';
import type { SmeProfileReq } from '../schemas/api/sme';
import {
	type SelectSmeProfile,
	smeProfileDbSchema
} from '../schemas/models/sme';

class SmeHelper {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async getSmeProfileInfo(
		userId: string,
		req: SmeProfileReq
	): Promise<SelectSmeProfile[]> {
		await this.validateBrnId(req.BrnId);
		const smeProfileRes: SelectSmeProfile[] = await this.db
			.select()
			.from(smeProfileDbSchema)
			.where(
				and(
					eq(smeProfileDbSchema.UserId, userId),
					eq(smeProfileDbSchema.IdKey, req.BrnId)
				)
			)
			.limit(1);

		if (!smeProfileRes)
			throw new UE_ERROR(
				'Invalid SME Profile. The User not exist for this BrnId',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{ integrationId: this.integrationId, response: null }
			);

		return smeProfileRes;
	}

	// async getSmeDeptList(brnId: string): Promise<SelectSmeDeptApi> {
	// 	// validate BrnId
	// 	await this.isValidSmeIdentification(brnId);

	// 	const smeDeptRes = await this.db
	// 		.select({
	// 			DeptId: smeDeptDbSchema.Id,
	// 			DeptName: smeDeptDbSchema.DeptName
	// 		})
	// 		.from(smeDeptDbSchema)
	// 		.where(eq(smeDeptDbSchema.IdKey, brnId));

	// 	// if not found/exist, just return an empty array
	// 	return smeDeptRes;
	// }

	async validateBrnId(brnId: string): Promise<void> {
		const brnRes: SelectIdentification[] = await this.db
			.select()
			.from(identificationDbSchema)
			.where(eq(identificationDbSchema.IdKey, brnId))
			.limit(1);

		if (!(brnRes[0]?.IdType ?? '' in SmeIdTypeEnum))
			throw new UE_ERROR(
				'Invalid BrnId.',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
	}
}

export default SmeHelper;
