import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';
import { ProgressStatusEnum } from '../../../../../enum/order.enum';

export const planPricingSchema = t.Any();

export type PlanPricing = Static<typeof planPricingSchema>;

export const planMarketingSchema = t.Any();

export type PlanMarketing = Static<typeof planMarketingSchema>;

export const planTechnicalSchema = t.Any();

export type PlanTechnical = Static<typeof planTechnicalSchema>;

export const orderAppointmentSchema = t.Any();

export type OrderAppointment = Static<typeof orderAppointmentSchema>;

export const orderableAddressSchema = t.Object({
	AddressId: t.Optional(
		t.String({ examples: [''], description: 'Used to set Granite ID' })
	),
	UnitNo: t.String({ examples: ['No.7', '5'] }),
	FloorNo: t.Optional(
		t.String({ examples: ['1'], description: 'Required for High-Rise' })
	),
	BuildingName: t.Optional(
		t.String({
			examples: ['Bangunan Anggun'],
			description: 'Required for High-Rise'
		})
	),
	StreetType: t.String({ examples: ['JALAN'] }),
	StreetName: t.String({ examples: ['Anggun 3/5'] }),
	Section: t.String({ examples: ['Seksyen 3'] }),
	City: t.String({ examples: ['Shah Alam'] }),
	Postcode: t.String({ examples: ['40000'] }),
	State: t.String({ examples: ['Selangor'] }),
	Country: t.String({ examples: ['Malaysia'] }),
	Platform: t.String({ examples: ['FTTH', 'VDSL'] }),
	Latitude: t.Optional(
		t.String({
			examples: ['1.438109613'],
			description: 'Commonly found when user set from a map'
		})
	),
	Longitude: t.Optional(
		t.String({
			examples: ['1.109434836'],
			description: 'Commonly found when user set from a map'
		})
	),
	PremiseCategory: t.Optional(t.String({ examples: ['High-Rise', 'Landed'] }))
});

export type OrderableAddress = Static<typeof orderableAddressSchema>;

export const ijoinInstallationAddressSchema = t.Object({
	AddressId: t.Optional(t.String()),
	Unit: t.Optional(t.String({ examples: ['51'] })),
	Floor: t.Optional(t.String()),
	Block: t.Optional(t.String()),
	Building: t.Optional(t.String()),
	StreetType: t.String({ examples: ['JALAN'] }),
	StreetName: t.String({ examples: ['ASAM KUMBANG'] }),
	Street: t.Optional(t.String()),
	Section: t.String({ examples: ['TAMAN KEPONG BARU'] }),
	City: t.String({ examples: ['KUALA LUMPUR'] }),
	PostCode: t.String({ examples: ['52100'] }),
	State: t.String({ examples: ['WILAYAH PERSEKUTUAN'] }),
	Country: t.Optional(t.String()),
	Area: t.Optional(t.String()),
	AddressSource: t.Optional(t.String()),
	Platform: t.Optional(t.String()),
	PremiseCategory: t.Optional(t.String())
});

export type IjoinInstallationAddress = Static<
	typeof ijoinInstallationAddressSchema
>;

export const orderableProgressSchema = t.Array(
	t.Object({
		Status: t.String({ enum: Object.values(ProgressStatusEnum) }),
		Timestamp: t.String()
	})
);

export type OrderableProgress = Static<typeof orderableProgressSchema>;

export const orderableJSONDataSchema = t.Any();

export type OrderableJSONData = Static<typeof orderableJSONDataSchema>;

export const customerOrderTableSchema = pgTable('customer_order', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	CustomerId: text('customer_id').unique().notNull(),
	//BillingAccountNo: text('BillingAccountNo').notNull(),
	IdType: text('id_type').notNull(),
	IdValue: text('id_value').notNull(),
	FullName: text('full_name').notNull(),
	Email: text('email').notNull(),
	MobileNo: text('mobile_no').notNull(),
	//ServiceId: text('ServiceId').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectCustomerOrder = typeof customerOrderTableSchema.$inferSelect;

export const orderablePlanTableSchema = pgTable('orderable_plan', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	PlanId: text('plan_id').unique(),
	PlanCategory: text('plan_category').notNull(),
	PlanName: text('plan_name').notNull(),
	PlanPricing: json('plan_pricing').$type<PlanPricing>(),
	PlanMarketing: json('plan_marketing').$type<PlanMarketing>(),
	PlanTechnical: json('plan_technical').$type<PlanTechnical>(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectOrderablePlan = typeof orderablePlanTableSchema.$inferSelect;

export const orderableTxnHistoryTableSchema = pgTable('orderable_txn_history', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	OrderId: text('order_id').notNull(),
	CustomerId: text('customer_id')
		.references(() => customerOrderTableSchema.CustomerId, {
			onDelete: 'cascade'
		})
		.notNull(),
	TargetSystem: text('target_system').notNull(),
	SourceSystem: text('source_system').notNull(),
	OrderSegment: text('order_segment').notNull(),
	OrderType: text('order_type').notNull(),
	OrderCategory: text('order_category').notNull(),
	OrderStatus: text('order_status').notNull(),
	BillingAccountNo: text('billing_account_no'),
	PlanId: text('plan_id'),
	ProductName: text('product_name').notNull(),
	OrderProgress: json('order_progress').$type<OrderableProgress>(),
	SiebelOrderStatus: text('siebel_order_status'),
	OrderData: json('order_data').$type<OrderableJSONData>(),
	SubOrderData: json('sub_order_data').$type<OrderableJSONData>(),
	PromoCode: text('promo_code'),
	VoucherCode: text('voucher_code'),
	ReferralCode: text('referral_code'),
	Address: json('address').$type<OrderableAddress>(),
	Appointment: json('appointment').$type<OrderAppointment>(),
	ErrorMessage: text('error_message'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectOrderableTxnHistory =
	typeof orderableTxnHistoryTableSchema.$inferSelect;

export type InsertOrderableTxnHistory =
	typeof orderableTxnHistoryTableSchema.$inferInsert;

export const ijoinCustomerDetailsSchema = t.Object({
	idType: t.String(),
	idValue: t.String(),
	fullName: t.String(),
	email: t.String(),
	mobile: t.String()
});

export type IjoinCustomerDetails = Static<typeof ijoinCustomerDetailsSchema>;
