import { type Static, t } from 'elysia';

export const wso2CreateSRNovaReqSchema = t.Object({
	ListOfTmEaiSttCreateSr: t.Object({
		TmServiceRequestIntegration: t.Object({
			Id: t.String({ examples: ['1'] }),
			CustomerRowID: t.String({ examples: ['1-1F32AJK23'] }),
			ServiceRowID: t.String({ examples: ['1-1F32AJK23'] }),
			BillingAccountRowID: t.String({ examples: ['1-1F32AJK23'] }),
			BillingAccountNo: t.String({ examples: ['**********'] }),
			ClosureCategory: t.String({ examples: [''] }),
			CallBack: t.String({ examples: ['Yes'] }),
			ContactDetailRowID: t.String({ examples: ['1-1F32AJK23'] }),
			DetailedDescription: t.String({
				examples: ['This is a test description']
			}),
			Owner: t.String({ examples: ['WIDER_TMUC_TECH_SOC2'] }),
			Area: t.String({ examples: ['Service Failure'] }),
			Type: t.String({ examples: ['Fault'] }),
			SubArea: t.String({ examples: ['All Services Down'] }),
			Source: t.String({ examples: ['EASY FIX'] }),
			Status: t.String({ examples: ['Open'] }),
			ClosureReason: t.String({ examples: [''] }),
			CaseCategory: t.String({ examples: ['Line Disconnect'] }),
			ContactDetailReported: t.String({ examples: ['1-1F32AJK23'] }),
			ListOfTmFinServiceRequestNotesIntegration: t.Object({
				TMFINServiceRequestNotesIntegration: t.Array(
					t.Object({
						CreatedBy: t.String({ examples: ['EAI'] }),
						NoteId: t.String({ examples: ['1'] }),
						NoteType: t.String({ examples: ['Note'] }),
						CreatedByPosition: t.String({ examples: ['EAI'] }),
						NoteDescription: t.String({ examples: ['This is a test note'] })
					})
				)
			})
		})
	})
});

export type Wso2CreateSRNovaReq = Static<typeof wso2CreateSRNovaReqSchema>;

export const wso2CreateSRNovaResSchema = t.MaybeEmpty(
	t.Object({
		ListOfTmEaiSttCreateSr: t.MaybeEmpty(
			t.Object({
				TmServiceRequestIntegration: t.MaybeEmpty(
					t.Object({
						Id: t.MaybeEmpty(t.String()),
						DateCreated: t.MaybeEmpty(t.String()),
						CTTNumber: t.MaybeEmpty(t.String()),
						SRNumber: t.MaybeEmpty(t.String()),
						SRNumberFault: t.MaybeEmpty(t.String()),
						CustomerRowID: t.MaybeEmpty(t.String()),
						ServiceRowID: t.MaybeEmpty(t.String()),
						BillingAccountRowID: t.MaybeEmpty(t.String()),
						BillingAccountNo: t.MaybeEmpty(t.String()),
						Group: t.MaybeEmpty(t.String()),
						ClosureCategory: t.MaybeEmpty(t.String()),
						ClosureRemarks: t.MaybeEmpty(t.String()),
						CallBack: t.MaybeEmpty(t.String()),
						CallBackDate: t.MaybeEmpty(t.String()),
						CallBackTime: t.MaybeEmpty(t.String()),
						ContactDetailRowID: t.MaybeEmpty(t.String()),
						DetailedDescription: t.MaybeEmpty(t.String()),
						Area: t.MaybeEmpty(t.String()),
						Type: t.MaybeEmpty(t.String()),
						SubArea: t.MaybeEmpty(t.String()),
						Owner: t.MaybeEmpty(t.String()),
						Priority: t.MaybeEmpty(t.String()),
						SRRowID: t.MaybeEmpty(t.String()),
						Severity: t.MaybeEmpty(t.String()),
						Source: t.MaybeEmpty(t.String()),
						Status: t.MaybeEmpty(t.String()),
						ClosureReason: t.MaybeEmpty(t.String()),
						CaseCategory: t.MaybeEmpty(t.String()),
						PreferredAcknowledgement: t.MaybeEmpty(t.String()),
						ContactDetailReported: t.MaybeEmpty(t.String()),
						ListOfTmFinServiceRequestNotesIntegration: t.MaybeEmpty(
							t.Object({
								TMFINServiceRequestNotesIntegration: t.MaybeEmpty(
									t.Array(
										t.Optional(
											t.Object({
												CreatedBy: t.MaybeEmpty(t.String()),
												NoteId: t.MaybeEmpty(t.String()),
												NoteType: t.MaybeEmpty(t.String()),
												CreatedByPosition: t.MaybeEmpty(t.String()),
												NoteDescription: t.MaybeEmpty(t.String())
											})
										)
									)
								)
							})
						)
					})
				)
			})
		)
	})
);

export type Wso2CreateSRNovaRes = Static<typeof wso2CreateSRNovaResSchema>;
