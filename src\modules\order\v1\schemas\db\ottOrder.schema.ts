import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

export const ottOrderJSONDataSchema = t.String();

export type ottOrderJSONData = Static<typeof ottOrderJSONDataSchema>;

export const ottSubscribedJSONDataSchema = t.String();

export type ottSubscribedJSONData = Static<typeof ottSubscribedJSONDataSchema>;

export const ottOrderTableSchema = pgTable('ott_order', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity({
		startWith: 1000
	}),
	OttPlanId: text('ott_plan_id').notNull(),
	AccountType: text('account_type').notNull(),
	AccountId: text('account_id').notNull(),
	OrderRefNo: text('order_ref_no').notNull(),
	OttOrder: json('ott_order').$type<ottOrderJSONData>(),
	Status: text('status').notNull(),
	OttSubscribed: json('ott_subscribed').$type<ottSubscribedJSONData>(),
	CustName: text('cust_name').notNull(),
	CustEmail: text('cust_email').notNull(),
	TvPackName: text('tv_pack_name').notNull(),
	IptvId: text('iptv_id').notNull(),
	Category: text('category').notNull(),
	Source: text('source').notNull(),
	CreatedAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
	UpdatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow()
});

export type SelectOttOrder = typeof ottOrderTableSchema.$inferSelect;
export type InsertOttOrder = typeof ottOrderTableSchema.$inferInsert;
