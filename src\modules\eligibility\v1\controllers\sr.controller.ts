import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type SRTerminationRes,
	srTerminationReqSchema,
	srTerminationResSchema
} from '../schemas/api/sr.schema';
import ServiceRequestEligibility from '../services/sr.service';

const srV1Routes = new Elysia({ prefix: '/sr' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			ServiceRequestEligibility: new ServiceRequestEligibility(
				randomUUID(),
				idTokenInfo
			)
		};
	})
	.post(
		'/termination',
		async (ctx): Promise<SRTerminationRes> => {
			return await ctx.ServiceRequestEligibility.getSREligibility(ctx.body);
		},
		{
			detail: {
				description:
					"SR means Service Request. Check customer's eligibility for SR application. This API is used to determine whether the customer is eligible for termination request. <br><br> <b>Backend System:</b> NOVA SIEBEL",
				tags: ['Eligibility']
			},
			body: srTerminationReqSchema,
			response: {
				200: srTerminationResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default srV1Routes;
