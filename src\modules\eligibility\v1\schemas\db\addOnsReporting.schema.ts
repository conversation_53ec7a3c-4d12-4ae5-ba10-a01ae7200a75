import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

export const addOnsReportingInfoDataSchema = t.Object({
	IdType: t.String(),
	IdValue: t.String(),
	BillingName: t.String(),
	BillingAccountNo: t.String(),
	BillingEmail: t.String(),
	BillingContactNo: t.String(),
	LoginId: t.String(),
	ServiceStartDate: t.Nullable(t.String()),
	CBPR: t.Nullable(t.String())
});

export type AddOnsReportingInfoData = Static<
	typeof addOnsReportingInfoDataSchema
>;

export const addOnsReportingTableSchema = pgTable('addons_reporting', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	ReportType: text('report_type').notNull().default('Reporting - Addon'),
	ReportDesc: text('report_desc')
		.notNull()
		.default('Report for Interest on Addon in Digital'),
	IdType: text('id_type').notNull(),
	IdValue: text('id_value').notNull(),
	OrderStatus: text('order_status').notNull(),
	Category: text('category').notNull(),
	Remarks: text('remarks').notNull(),
	InfoData: json('info_data').$type<AddOnsReportingInfoData>(),
	ErrorMessage: text('error_message'),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});
