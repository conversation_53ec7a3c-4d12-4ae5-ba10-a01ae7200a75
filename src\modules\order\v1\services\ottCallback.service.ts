import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { OttMerchantIdEnum } from '../../../../enum/addOns.enum';
import type { SourceEnum } from '../../../../enum/header.enum';
import { EmailEnum } from '../../../../enum/notification.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { TemporalOrderStatus } from '../../../../enum/temporal.enum';
import type {
	AppList,
	OttNotificationEmailRequest
} from '../../../../integration/emailTemplate/schemas/api/ottEmailTemplate.schema';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate } from '../../../../shared/common';
import {
	type SelectOttCatalogueView,
	ottCatalogueViewSchema
} from '../../../catalogue/v1/schemas/db/ottCatalogueView.schema';
import type {
	OttSubscribed,
	UpdateOrderReq,
	UpdateOrderRes
} from '../schemas/api/ottCallback.schema';
import {
	customerOrderTableSchema,
	orderableTxnHistoryTableSchema
} from '../schemas/db/orderable.schema';

class OttCallback {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
	}

	// Group 1: DB update only
	async handleOrderUpdateDb(req: UpdateOrderReq) {
		const [txn] = await this.db
			.select({
				order: orderableTxnHistoryTableSchema,
				customer: customerOrderTableSchema
			})
			.from(orderableTxnHistoryTableSchema)
			.innerJoin(
				customerOrderTableSchema,
				eq(
					orderableTxnHistoryTableSchema.CustomerId,
					customerOrderTableSchema.CustomerId
				)
			)
			.where(eq(orderableTxnHistoryTableSchema.OrderId, req.orderRefNo))
			.execute();

		if (!txn) {
			throw new UE_ERROR(
				'Ott order not found',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId, response: {} }
			);
		}

		const { order, customer } = txn;
		const currentDate = getMyTimeZoneDate();

		let orderStatus: TemporalOrderStatus =
			TemporalOrderStatus.PARTIALLY_ACTIVATED;

		if (req.ottSubscribed.length > 0) {
			const statuses = req.ottSubscribed.map(sub =>
				sub.ottStatus.toLowerCase()
			);

			if (statuses.every(status => status === 'active')) {
				orderStatus = TemporalOrderStatus.ACTIVATED;
			}
		}

		await this.db
			.update(orderableTxnHistoryTableSchema)
			.set({
				OrderStatus: orderStatus,
				OrderProgress: [
					...(order.OrderProgress ?? []),
					{
						Status: orderStatus,
						Timestamp: currentDate.toISOString()
					}
				],
				SubOrderData: {
					ottSubscribed: req.ottSubscribed
				}
			})
			.where(eq(orderableTxnHistoryTableSchema.OrderId, req.orderRefNo))
			.execute();

		return {
			order,
			customer,
			orderStatus
		};
	}

	// Group 2: Email notification only
	async handleOttEmailNotification(
		req: UpdateOrderReq,
		customerEmail: string,
		tvPackName: string,
		ottPlanId: string,
		customerName: string,
		category: string,
		orderStatus: string,
		accountId: string
	) {
		await this.sendOttEmailNotification(
			req,
			customerEmail,
			tvPackName,
			ottPlanId,
			customerName,
			category,
			orderStatus,
			accountId
		);
	}

	async updateOttOrder(
		_source: SourceEnum,
		req: UpdateOrderReq
	): Promise<UpdateOrderRes> {
		const { order, customer, orderStatus } =
			await this.handleOrderUpdateDb(req);

		await this.handleOttEmailNotification(
			req,
			customer.Email,
			order.ProductName,
			order.PlanId || '',
			customer.FullName,
			order.OrderCategory,
			orderStatus,
			order.BillingAccountNo || ''
		);

		return {
			responseCode: '200',
			responseMsg: 'Success',
			orderRefNo: req.orderRefNo
		};
	}

	private async sendOttEmailNotification(
		req: UpdateOrderReq,
		custEmail: string,
		tvPackName: string,
		ottPlanId: string,
		customerName: string,
		category: string,
		orderStatus: string,
		accountId: string
	) {
		let url = null;
		let emailSubject = null;

		const dateFormatter = new Intl.DateTimeFormat('en-GB', {
			day: '2-digit',
			month: 'long',
			year: 'numeric'
		});

		if (category.toLowerCase() === 'activation') {
			if (
				tvPackName.includes('Varnam') ||
				tvPackName.includes('Aneka') ||
				tvPackName.includes('Ruby') ||
				tvPackName.includes('Movies') ||
				tvPackName.includes('Kids') ||
				tvPackName.includes('Sports') ||
				tvPackName.includes('Family') ||
				tvPackName.includes('Staff')
			) {
				url = envConfig().OTT_VAR_NOTIFICATION_EMAIL_URL;
			} else if (tvPackName.toLowerCase() === 'new ultimate pack') {
				url = envConfig().OTT_ULTIMATE_NOTIFICATION_EMAIL_URL;
			} else if (tvPackName.includes('Ultimate Plus')) {
				url = envConfig().OTT_ULTIMATE_PLUS_NOTIFICATION_EMAIL_URL;
			} else {
				url = envConfig().OTT_ULTIMATE_MAX_NOTIFICATION_EMAIL_URL;
			}

			emailSubject = EmailEnum.OTT_EMAIL_SUBJECT;
		} else if (category.toLowerCase() === 'swapping') {
			if (
				orderStatus.toLowerCase() === 'activated' ||
				orderStatus.toLowerCase() === 'activated*'
			) {
				url = envConfig().OTT_SWAPPING_NOTIFICATION_EMAIL_URL;
				emailSubject = EmailEnum.OTT_SUCCESSFUL_SWITCH_EMAIL_SUBJECT;
			} else if (
				req.ottSubscribed.every(
					req => req.ottStatus.toLowerCase() === 'processing_active'
				)
			) {
				url = envConfig().OTT_SWAPPING_NOTIFICATION_EMAIL_URL;
				emailSubject = EmailEnum.OTT_SUCCESSFUL_SWITCH_EMAIL_SUBJECT;
			} else {
				url = envConfig().OTT_SWAPPING_FAILED_NOTIFICATION_EMAIL_URL;
				emailSubject = EmailEnum.OTT_FAILED_SWITCH_EMAIL_SUBJECT;
			}
		} else {
			url = envConfig().OTT_ALACARTE_NOTIFICATION_EMAIL_URL;
			emailSubject = EmailEnum.OTT_SUCCESSFUL_ALACARTE_EMAIL_SUBJECT;
		}

		//get catalog by plan id and Plan id
		const [ottPlanCatalog]: SelectOttCatalogueView[] = await this.db
			.select()
			.from(ottCatalogueViewSchema)
			.where(and(eq(ottCatalogueViewSchema.PlanId, ottPlanId)))
			.execute();

		const ottSubs: OttSubscribed[] = req.ottSubscribed;

		const appListArray: AppList[] = [];

		for (const ottSub of ottSubs) {
			if (ottPlanCatalog.OttOmgId === ottSub.ottOmgId) {
				const app: AppList = {
					appImgUrl: '', // Default to an empty string
					userId: '',
					ottUniversalLink: '',
					ottLoginInstruction: '',
					isMobile: false, // Default to false for Booleans
					isEmail: false,
					isUnifiId: false,
					price: '0.00', // Default to "0.00" or any placeholder
					startDate: '', // Leave date as an empty string
					isSuccess: false,
					tvAppName: '',
					isNetflix: false,
					isActivate: false
				};

				app.appImgUrl = ottPlanCatalog.OttIconPath;
				if (ottSub.ottUserId.toLowerCase() === 'n/a') {
					app.userId = 'email address';
				} else {
					app.userId = ottSub.ottUserId;
				}
				// Special case for HBO Go activation via email
				if (
					ottSub.ottMerchantId === OttMerchantIdEnum.HBO &&
					ottSub.ottOmgId === 144
				) {
					//const selfCarePortal = `${envConfig().SELF_CARE_PORTAL_URL}/service/tvapps/my-entertainment`;
					//app.ottUniversalLink = selfCarePortal;
					app.ottLoginInstruction = ottPlanCatalog.OttLoginInstruction ?? '';
					app.isActivate = true;
				} else if (ottSub.ottMerchantId === OttMerchantIdEnum.CMGO) {
					//const selfCarePortal = `${envConfig().SELF_CARE_PORTAL_URL}/service/tvapps/my-entertainment`;
					//app.ottUniversalLink = selfCarePortal;
					app.ottLoginInstruction = ottPlanCatalog.OttLoginInstruction ?? '';
					app.isActivate = true;
				} else {
					app.ottUniversalLink = ottPlanCatalog.OttUniversalLink;
					app.ottLoginInstruction = ottPlanCatalog.OttLoginInstruction ?? '';
					app.isActivate = false;
				}

				//Ala Carte
				if (
					ottSub.ottMerchantId === 38 &&
					ottSub.ottOmgId === 70 &&
					ottPlanCatalog.PlanId === 'P8'
				) {
					app.price = `RM ${ottPlanCatalog.OttPrice.toFixed(2)} for 3 months`;
				} else {
					app.price = `RM ${ottPlanCatalog.OttPrice.toFixed(2)} /monthly`;
				}

				app.tvAppName = ottPlanCatalog.OttName;

				if (ottSub.ottStartDate) {
					app.startDate = dateFormatter.format(new Date(ottSub.ottStartDate));
				} else {
					app.startDate = 'Date reflected after successfully active';
				}

				app.isMobile = ottPlanCatalog.OttLoginType === 'mobile';
				app.isEmail = ottPlanCatalog.OttLoginType === 'email';
				app.isUnifiId = !(app.isMobile || app.isEmail); // If not mobile or email, it's UnifiId

				app.isSuccess =
					ottSub.ottStatus === 'Active' ||
					ottSub.ottStatus === 'Processing_Active';
				app.isNetflix = ottSub.ottName === 'Netflix';

				appListArray.fill(app);
			}
		}

		const ottNotificationEmailReq: OttNotificationEmailRequest = {
			customerName: customerName,
			appList: appListArray,
			unifiId: accountId
		};

		const emailTemplateUrl = `${url}?customerName=${customerName}&appList=${appListArray}&unifiId=${accountId}`;

		const emailBody: string =
			await this.mwIntegration.EmailTemplateIntegration.getEmailBodyTemplateWithBodyRequest(
				emailTemplateUrl,
				ottNotificationEmailReq
			);

		const wso2EmailReq: Wso2EmailReq = {
			to: custEmail,
			cc: '',
			from: EmailEnum.FROM_NOREPLY,
			subject: emailSubject,
			body: emailBody
		};

		this.mwIntegration
			.getWso2NotificationIntegration()
			.getWso2SendEmail(wso2EmailReq, req.orderRefNo, 'OTT order status');
	}
}

export default OttCallback;
