import { and, eq } from 'drizzle-orm';
import randomString from 'random-string-gen';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../middleware/error';
import {
	type IjoinCustomerDetails,
	type SelectCustomerOrder,
	customerOrderTableSchema
} from '../schemas/db/orderable.schema';

class CustomerHelper {
	private db = getDbInstance();

	constructor(private integrationId: string) {}

	async getOrCreateCustomer(
		details: IjoinCustomerDetails
	): Promise<SelectCustomerOrder> {
		let customer = await this.db
			.select()
			.from(customerOrderTableSchema)
			.where(
				and(
					eq(customerOrderTableSchema.IdType, details.idType),
					eq(customerOrderTableSchema.IdValue, details.idValue)
				)
			);

		if (customer.length === 0) {
			customer = await this.db
				.insert(customerOrderTableSchema)
				.values({
					CustomerId: `CUST-${randomString(9).toUpperCase()}`,
					IdType: details.idType,
					IdValue: details.idValue,
					FullName: details.fullName,
					Email: details.email,
					MobileNo: details.mobile
				})
				.returning()
				.catch(err => {
					throw new UE_ERROR(
						`Failed to insert customer record: ${String(err)}`,
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId }
					);
				});
		}

		return customer[0];
	}
}

export default CustomerHelper;
