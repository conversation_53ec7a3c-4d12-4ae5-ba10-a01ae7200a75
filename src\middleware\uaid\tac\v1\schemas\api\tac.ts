import { type Static, t } from 'elysia';
import { TacProcessEnum } from '../../../../../../enum/notification.enum';
import { CredentialTypeEnum } from '../../../../../../enum/user.enum';
import { baseResponseSchema } from '../../../../../../shared/schemas/api/responses.schema';
import {
	signUpReqSchema,
	updateProfileReqSchema
} from '../../../../identity/v1/schemas/api/identity';

export const sendTacReqSchema = t.Object({
	Process: t.Optional(t.Enum(TacProcessEnum)),
	CredentialType: t.Enum(CredentialTypeEnum),
	CredentialValue: t.String({ minLength: 10 })
});

export type SendTacReq = Static<typeof sendTacReqSchema>;

export const sendTacByIdReqSchema = t.Object({
	Id: t.String({
		minLength: 64,
		examples: [
			'6480d9c50338d9d4d92568be7a11aa4de53226d3bb4e5bf63df9652be1eeda0f'
		]
	}),
	Process: t.Enum(TacProcessEnum)
});

export type SendTacByIdReq = Static<typeof sendTacByIdReqSchema>;

export const sendTacResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			TacNumber: t.String({ examples: ['****'] }),
			ExpiredAt: t.String({ examples: ['2025-03-01 09:00:52'] }),
			MaskedValue: t.String({ examples: ['r*******<EMAIL>'] })
		})
	},
	{ description: 'TAC successfully requested.' }
);

export type SendTacRes = Static<typeof sendTacResSchema>;

export const verifyTacReqSchema = t.Object({
	CredentialType: t.Enum(CredentialTypeEnum),
	CredentialValue: t.String({
		minLength: 10,
		examples: ['0123456789', '<EMAIL>']
	}),
	TacNumber: t.String({ minLength: 4, examples: ['8888'] })
});

export type VerifyTacReq = Static<typeof verifyTacReqSchema>;

export const verifyTacReqByIdSchema = t.Object({
	Id: t.String({
		minLength: 64,
		examples: [
			'6480d9c50338d9d4d92568be7a11aa4de53226d3bb4e5bf63df9652be1eeda0f'
		]
	}),
	TacNumber: t.String({ minLength: 4, examples: ['8888'] })
});

export type VerifyTacByIdReq = Static<typeof verifyTacReqByIdSchema>;

export const signUpVerificationReqSchema = t.Object({
	...signUpReqSchema.properties,
	CredentialType: t.Enum(CredentialTypeEnum),
	TacNumber: t.String({ minLength: 4, examples: ['8888'] })
});

export type SignUpVerificationReq = Static<typeof signUpVerificationReqSchema>;

export const updateVerificationReqSchema = t.Object({
	...updateProfileReqSchema.properties,
	Id: t.Optional(
		t.String({
			minLength: 64,
			examples: [
				'6480d9c50338d9d4d92568be7a11aa4de53226d3bb4e5bf63df9652be1eeda0f'
			]
		})
	),
	IdNumber: t.Optional(
		t.String({ minLength: 5, examples: ['570831550001', 'A00000000'] })
	),
	CredentialType: t.Optional(t.Enum(CredentialTypeEnum)),
	TacNumber: t.String({ minLength: 4, examples: ['8888'] })
});

export type UpdateVerificationReq = Static<typeof updateVerificationReqSchema>;
