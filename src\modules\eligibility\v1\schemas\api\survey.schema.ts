import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const vocEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsParticipant: t.String({ examples: ['Y'] }),
			SurveyUrl: t.Nullable(
				t.String({ format: 'uri', examples: ['https://survey.com'] })
			),
			Email: t.Nullable(
				t.String({ format: 'email', examples: ['<EMAIL>'] })
			),
			Eligibility: t.String({ examples: ['Y'] })
		})
	},
	{
		description: "Customer's TNPS survey eligibility successfully retrieved."
	}
);

export type VocEligibilityRes = Static<typeof vocEligibilityResSchema>;
