import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const wso2EmailTxnHistoryTableSchema = pgTable(
	'wso2_email_txn_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		TxnId: text('txn_id').notNull(),
		TxnType: text('txn_type').notNull(),
		RecipientEmail: text('recipient_email').notNull(),
		XBPID: text('xbpid').notNull(),
		XBOID: text('xboid').notNull(),
		RequestBody: text('request_body').notNull(),
		IsEmailSent: boolean('is_email_sent').notNull(),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	}
);
