import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../config/db.config';
import { envConfig } from '../../config/env.config';
import { fetchApi } from '../helper/fetchApi.helper';

import {
	NetcoreActivityStatusMessageEnum,
	NetcoreCommonStatusMessageEnum,
	type NetcoreStatusMessage,
	NetcoreStatusMessageEnum
} from '../../enum/netcoreStatusMessage.enum';

import type { NetcoreAddActivityReq } from './schemas/api/netcoreAddActivity.schema';
import type { NetcoreBaseResponse } from './schemas/api/netcoreBaseResponse.schema';
import type { NetcoreSendEmailReq } from './schemas/api/netcoreSendEmail.schema';

import { netcoreActivityTxnHistoryTableSchema } from './schemas/db/netcoreActivityTxnHistory.schema';
import { netcoreEmailTxnHistoryTableSchema } from './schemas/db/netcoreEmailTxnHistory.schema';

class NetcoreIntegration {
	private integrationId: string;
	private db: NodePgDatabase;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
	}

	private readonly statusMessages: Record<number, NetcoreStatusMessage> = {
		200: NetcoreStatusMessageEnum.SUCCESS_200,
		201: NetcoreStatusMessageEnum.CREATED_201,
		202: NetcoreStatusMessageEnum.ACCEPTED_202,
		400: NetcoreActivityStatusMessageEnum.BAD_REQUEST_400,
		401: NetcoreCommonStatusMessageEnum.UNAUTHORIZED_401,
		405: NetcoreCommonStatusMessageEnum.METHOD_NOT_ALLOWED_405,
		408: NetcoreCommonStatusMessageEnum.REQUEST_TIMEOUT_408,
		500: NetcoreCommonStatusMessageEnum.INTERNAL_SERVER_ERROR_500
	};

	/**
	 * Send transactional email via Netcore Email API
	 */
	async sendEmail(
		bodyRequest: NetcoreSendEmailReq,
		txnId: string,
		txnType: string
	): Promise<NetcoreBaseResponse> {
		const url = envConfig().NETCORE_SEND_EMAIL_URL;

		const res = await fetchApi(this.integrationId, url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Api-Key': `${process.env.NETCORE_API_KEY}`
			},
			body: JSON.stringify(bodyRequest)
		});

		const responseText = await res.text();
		const status = res.status;
		const success = status >= 200 && status < 300;

		const message =
			this.statusMessages[status] ?? NetcoreCommonStatusMessageEnum.DEFAULT;

		await this.saveEmailToDb(
			txnId,
			txnType,
			bodyRequest,
			responseText,
			success
		);

		return {
			success,
			statusCode: success ? undefined : status,
			message
		};
	}

	/**
	 * Upload bulk activity logs to Netcore CE using Add Activity API
	 */
	async addActivity(
		payload: NetcoreAddActivityReq,
		txnId: string,
		txnType: string
	): Promise<NetcoreBaseResponse> {
		const url = envConfig().NETCORE_ACTIVITY_URL;

		const res = await fetchApi(this.integrationId, url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Api-Key': `${process.env.NETCORE_API_KEY}`
			},
			body: JSON.stringify(payload)
		});

		const responseText = await res.text();
		const status = res.status;
		const success = status >= 200 && status < 300;

		const message =
			this.statusMessages[status] ?? NetcoreCommonStatusMessageEnum.DEFAULT;

		await this.saveActivityToDb(
			txnId,
			txnType,
			JSON.stringify(payload),
			responseText,
			success
		);

		return {
			success,
			statusCode: success ? undefined : status,
			message
		};
	}

	private async saveEmailToDb(
		txnId: string,
		txnType: string,
		bodyRequest: NetcoreSendEmailReq,
		responseBody: string,
		isEmailSent: boolean
	) {
		await this.db.insert(netcoreEmailTxnHistoryTableSchema).values({
			TxnId: txnId,
			TxnType: txnType,
			RecipientEmail: JSON.stringify(
				bodyRequest.personalizations.flatMap(p => p.to.map(t => t.email))
			),
			RequestBody: JSON.stringify(bodyRequest),
			ResponseBody: responseBody,
			IsEmailSent: isEmailSent
		});
	}

	private async saveActivityToDb(
		txnId: string,
		txnType: string,
		requestBody: string,
		responseBody: string,
		isSuccess: boolean
	) {
		await this.db.insert(netcoreActivityTxnHistoryTableSchema).values({
			TxnId: txnId,
			TxnType: txnType,
			RequestBody: requestBody,
			ResponseBody: responseBody,
			IsSuccess: isSuccess
		});
	}
}

export default NetcoreIntegration;
