import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { js2xml } from 'xml-js';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import { SourceEnum } from '../../../../enum/header.enum';
import { EmailEnum } from '../../../../enum/notification.enum';
import type {
	OsesPaymentItems,
	OsesReceiptEmailTemplateReq
} from '../../../../integration/emailTemplate/schemas/api/osesTemplate.schema';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import type {
	Wso2OutstandingAmountReq,
	Wso2OutstandingAmountRes
} from '../../../../integration/wso2/user/schemas/api/wso2OutstandingAmount.schema';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import { hashHex } from '../../../../shared/encryption/hash';
import type {
	OsesChildTxn,
	PgOsesChildTxn
} from '../schemas/api/osesCreateUrl.schema';
import { billPaymentTxnHistoryTableSchema } from '../schemas/db/billPaymentTxnHistory.schema';

class OsesHelper {
	private db: NodePgDatabase;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
	}

	getOsesMerchantId(
		isKciPayment: boolean,
		isBillReadiness: boolean,
		source: string
	): string {
		const env = envConfig();
		if (source === SourceEnum.UNIFI_PORTAL) {
			if (isKciPayment) {
				if (isBillReadiness) {
					return env.OSES_SMS_MERCHANT_ID;
				}
				return env.OSES_CMC_SMS_MERCHANT_ID;
			}
			return env.OSES_PORTAL_BILL_MERCHANT_ID;
		}

		return env.OSES_APP_MERCHANT_ID;
	}

	getOsesTxnPass(merchantId: string): string {
		const env = envConfig();
		switch (merchantId) {
			case env.OSES_PORTAL_BILL_MERCHANT_ID:
				return `${process.env.OSES_PORTAL_BILL_TXN_PASS}`;
			case env.OSES_CMC_SMS_MERCHANT_ID:
				return `${process.env.OSES_CMC_SMS_TXN_PASS}`;
			case env.OSES_SMS_MERCHANT_ID:
				return `${process.env.OSES_SMS_TXN_PASS}`;
			case env.OSES_APP_MERCHANT_ID:
				return `${process.env.OSES_APP_TXN_PASS}`;
			default:
				return `${process.env.OSES_DEFAULT_TXN_PASS}`;
		}
	}

	getOsesSignature = (
		merchantId: string,
		merchantTxnId: string,
		amount: string,
		txnStatus: string
	): string => {
		const txnId: number = 0;
		const signature: string =
			txnStatus === ''
				? `##${merchantId}##${this.getOsesTxnPass(
						merchantId
					)}##${merchantTxnId}##${amount}##${txnId}##`
						.trim()
						.toUpperCase()
				: `##${merchantId}##${this.getOsesTxnPass(
						merchantId
					)}##${merchantTxnId}##${amount}##${txnStatus}##`
						.trim()
						.toUpperCase();
		return hashHex(signature);
	};

	async getOsesChildTxnInXML(
		merchantTxnId: string,
		merchantId: string,
		osesChildTxn: OsesChildTxn
	): Promise<string> {
		const xmlOptions = {
			compact: true,
			ignoreComment: true,
			spaces: 4
		};

		const pgOsesChildTxn: PgOsesChildTxn = {
			txn: {
				child_txn: []
			}
		};

		for (const childTxn of osesChildTxn) {
			if (childTxn)
				pgOsesChildTxn.txn.child_txn.push({
					sub_merchant_id: merchantId,
					sub_order_id: String(merchantTxnId),
					gross_amount: childTxn.GrossAmount,
					gbt_amount: childTxn.GrossAmount,
					nett_amount: childTxn.NettAmount,
					misc_amount: childTxn.MiscAmount,
					account_no: childTxn.BillingAccountNo,
					revenue_code: childTxn.RevenueCode
				});
		}

		const xmlResult = js2xml(pgOsesChildTxn, xmlOptions)
			.replaceAll('\n', '')
			.trim();

		return xmlResult;
	}

	async saveOutstandingAmount(
		osesChildTxn: OsesChildTxn,
		source: string,
		merchantTxnId: string
	): Promise<void> {
		for (const childTxn of osesChildTxn) {
			if (childTxn) {
				const wso2OutstandingAmountReq: Wso2OutstandingAmountReq = {
					RetrieveBillingAmountRequest: {
						BillingAccountNo: childTxn.BillingAccountNo,
						SystemName: childTxn.SystemName
					}
				};
				const wso2OutstandingAmountRes: Wso2OutstandingAmountRes =
					await this.mwIntegration.Wso2UserIntegration.getWso2OutstandingAmount(
						wso2OutstandingAmountReq
					);
				const totalOutstandingAmt: string =
					wso2OutstandingAmountRes.Response.RetrieveBillingAmountResponse.RetrieveOutStandingAmount.TotalOutStandingAmount?.replaceAll(
						',',
						''
					) ?? '0.00';

				await this.db
					.insert(billPaymentTxnHistoryTableSchema)
					.values({
						MerchantTxnId: merchantTxnId,
						BillingAccountNo: childTxn.BillingAccountNo,
						BillNo: childTxn.BillNo,
						Email: childTxn.Email,
						UserEnteredAmt: childTxn.GrossAmount,
						OutstandingAmt: totalOutstandingAmt,
						Source: source
					})
					.returning()
					.catch(error => {
						pinoLog.error(error);
						return [];
					});
			}
		}
	}

	async sendPaymentReceiptEmail(
		osesPaymentItems: OsesPaymentItems[],
		transactionId: string,
		paymentMethod: string,
		email: string,
		name: string,
		amount: string,
		formattedDate: string
	): Promise<void> {
		const emailUrl: string = envConfig().OSES_PAYMENT_RECEIPT_TEMPLATE;

		const osesReceiptEmailTemplateReq: OsesReceiptEmailTemplateReq = {
			name: name,
			receiptId: transactionId,
			date: formattedDate,
			method: paymentMethod,
			paymentItems: osesPaymentItems,
			amount: Number.parseFloat(amount).toFixed(2)
		};

		const emailBody: string =
			await this.mwIntegration.EmailTemplateIntegration.getEmailBodyTemplateWithBodyRequest(
				emailUrl,
				osesReceiptEmailTemplateReq
			);

		const wso2EmailReq: Wso2EmailReq = {
			to: email,
			from: EmailEnum.FROM_NOREPLY,
			subject: 'Payment Receipt',
			body: emailBody
		};

		await this.mwIntegration
			.getWso2NotificationIntegration()
			.getWso2SendEmail(wso2EmailReq, transactionId, 'OSES Transaction');
	}

	async getRedirectUrlWithHashAndMerchantTranId(
		sourceRedirectUrl: string,
		signature: string,
		merchantTxnId: string
	): Promise<string> {
		const hashedSignature: string = await encrypt(signature);
		const redirectBaseUrl: string = sourceRedirectUrl;
		const queryObj = {
			MERCHANT_TRANID: String(merchantTxnId),
			HASH: hashedSignature
		};
		const params = new URLSearchParams(queryObj);
		const queryString: string = params.toString();
		const redirectUrl: string = `${redirectBaseUrl}?${queryString}`;
		return redirectUrl;
	}

	getOsesPaymentMethod(paymentMethod: string): string {
		switch (paymentMethod) {
			case '1':
				return 'Credit Card';
			case '3':
				return 'FPX';
			case '18':
				return 'TNG eWallet';
			default:
				return paymentMethod;
		}
	}

	getOsesTxnStatus(txnStatus: string | null): string {
		if (!txnStatus) return 'PENDING';

		const status = txnStatus.toUpperCase();
		if (
			status === 'N' ||
			status === 'RC' ||
			status === 'A' ||
			status === 'CRC' ||
			status === 'I'
		) {
			return 'PENDING';
		}

		if (status === 'S' || status === 'C') {
			return 'SUCCESSFUL';
		}

		return 'UNSUCCESSFUL';
	}
}

export default OsesHelper;
