export enum TacRequestTypeEnum {
	SMS = 'sms',
	EMAIL = 'email'
}

export enum TacStatusEnum {
	ACTIVE = 'Active',
	DEACTIVATED = 'Deactivated'
}

export enum TacTextEnum {
	TAC_VERIFIED = 'Tac verified',
	TAC_NOT_VERIFIED = 'Tac not verified',
	TAC_NOT_FOUND = 'Tac not found',
	TAC_ALREADY_USED = 'Tac already used...!',
	TAC_SEND_SUCCESS = 'Tac send success',
	TAC_SEND_FAILED = 'Tac send failed',
	DB_SAVE_FAILED = 'Database save failed',
	TAC_NOT_EXPIRED = 'Tac is within expiry date time'
}

export enum TacProcessEnum {
	SIGN_IN = 'sign-in',
	SIGN_UP = 'sign-up',
	UPDATE = 'update'
}

export enum EmailEnum {
	FROM_NOREPLY = '<EMAIL>',
	AUTOPAY_REGISTRATION_SUBJECT = 'Sign Up Autopay Request',
	AUTOPAY_MODIFICATION_SUBJECT = 'Modify Autopay Request',
	AUTOPAY_TERMINATION_SUBJECT = 'Deactivate Autopay Request',
	ECOMMERCE_SUBJECT = 'Service Request for eCommerce Hub - ',
	CLOUD_CONNECT_SUBJECT = 'Service Request for unifi Cloud Storage - ',
	OTT_EMAIL_SUBJECT = 'Successful! Ready, set, STREAM!',
	OTT_FAILED_SWITCH_EMAIL_SUBJECT = 'unifi TV: Your streaming app switch request is unsuccessful',
	OTT_SUCCESSFUL_SWITCH_EMAIL_SUBJECT = 'unifi TV: Successful Switch! Ready, set, STREAM!',
	OTT_SUCCESSFUL_ALACARTE_EMAIL_SUBJECT = 'Start to enjoy unlimited great content from Unifi TV Ala Carte Exclusive',
	SAVE_ORDER_EMAIL_SUBJECT_UPB_RM0 = 'Thank you for your add on redemption',
	SAVE_ORDER_EMAIL_SUBJECT_TV_PACK = 'Unifi TV: Thank you for your Add-on subscription',
	SAVE_ORDER_EMAIL_SUBJECT = 'Thank you for your add-on subscription',
	SAVE_ORDER_EMAIL_SUBJECT_SMART_DEVICE = 'Your Unifi Smart Device order is successful',
	ADDON_EMAIL_FROM = '<EMAIL>'
}
