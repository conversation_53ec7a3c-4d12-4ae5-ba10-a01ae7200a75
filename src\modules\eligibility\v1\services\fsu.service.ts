import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2EdwhMoanaReq,
	Wso2EdwhMoanaRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2EdwhMoana.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getMyTimeZoneDate } from '../../../../shared/common';
import AccountService from '../../../user/v1/helpers/accountService.helper';
import type { FsuEligibilityRes } from '../schemas/api/fsu.schema';

class FsuEligibility {
	private integrationId: string;
	private idTokenInfo: IdTokenInfo;
	private mwIntegration: MwIntegration;
	private accountService: AccountService;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
		this.accountService = new AccountService(this.integrationId);
	}

	async getFsuEligibility(
		ServiceId: string,
		Segment: string
	): Promise<FsuEligibilityRes> {
		const hasAccessToServiceId: boolean =
			await this.accountService.hasAccessToServiceId(
				this.idTokenInfo.IdType,
				this.idTokenInfo.IdValue,
				ServiceId
			);

		if (!hasAccessToServiceId) {
			throw new UE_ERROR(
				'You do not have access to this service.',
				StatusCodeEnum.UNAUTHORIZED_ERROR
			);
		}
		const isoDateStringFormat: string = getMyTimeZoneDate()
			.toISOString()
			.replace(/[-:.TZ]/g, '')
			.slice(0, 17); // yyyyMMddhhmmssss
		const requestId: string = `FreeUpgradeInfo${isoDateStringFormat}`;
		const wso2Req: Wso2EdwhMoanaReq = {
			requestHeader: {
				requestId,
				eventName: 'evOEDWHFreeUpgradeInfo'
			},
			edwhRequest: {
				customerSvcId: ServiceId,
				segment: Segment
			}
		};
		const wso2Res: Wso2EdwhMoanaRes =
			await this.mwIntegration.Wso2EligibilityIntegration.getWso2EdwhMoana(
				wso2Req
			);
		const res: FsuEligibilityRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligibleForUpgrade:
					wso2Res?.edwhResponse.eligibleFreeUG?.startsWith('Y') ?? false,
				EligibilityMessage:
					wso2Res?.edwhResponse.eligibleFreeSpeedReason ?? null,
				EligibilitySpeed: wso2Res?.edwhResponse.eligibleFreeSpeedUG ?? null
			}
		};

		return res;
	}
}

export default FsuEligibility;
