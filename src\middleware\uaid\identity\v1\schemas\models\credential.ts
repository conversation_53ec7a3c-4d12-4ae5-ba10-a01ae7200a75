import { sql } from 'drizzle-orm';
import {
	boolean,
	index,
	integer,
	pgTable,
	text,
	timestamp,
	varchar
} from 'drizzle-orm/pg-core';
import randomString from 'random-string-gen';
import { identityDbSchema } from './identity';

export const credentialDbSchema = pgTable(
	'mw_credential',
	{
		Id: varchar('id', { length: 15 })
			.primaryKey()
			.notNull()
			.$defaultFn(() => randomString(15)),
		UserId: varchar('user_id', { length: 50 })
			.notNull()
			.references(() => identityDbSchema.UserId, { onDelete: 'cascade' }),
		CredentialKey: varchar('credential_key', { length: 150 })
			.notNull()
			.unique(),
		CredentialType: varchar('credential_type', { length: 50 }).notNull(),
		CredentialValue: text('credential_value').notNull(),
		IsPrimary: boolean('is_primary').default(false),
		IsUnifiNumber: boolean('is_unifi_number').default(false),
		IsVerified: boolean('is_verified').default(false),
		UpdateCount: integer('update_count').notNull().default(0),
		CreatedAt: timestamp('created_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now()`),
		UpdatedAt: timestamp('updated_at', { mode: 'date', withTimezone: true })
	},
	t => [
		index('idx_mw_credential_id').on(t.Id),
		index('idx_mw_credential_user_id').on(t.UserId),
		index('idx_mw_credential_credential_key').on(t.CredentialKey)
	]
);

export type InsertCredential = typeof credentialDbSchema.$inferInsert;
export type SelectCredential = typeof credentialDbSchema.$inferSelect;
