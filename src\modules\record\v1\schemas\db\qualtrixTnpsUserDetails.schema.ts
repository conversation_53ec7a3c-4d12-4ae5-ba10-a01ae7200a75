import { pgTable, serial, text, timestamp } from 'drizzle-orm/pg-core';

export const qualtrixTnpsUserDetailsTableSchema = pgTable(
	'qualtrix_tnps_user_details',
	{
		Id: serial('id').primaryKey(),
		IdType: text('id_type').notNull(),
		IdValue: text('id_value').notNull(),
		Email: text('email').notNull(),
		MobileNumber: text('mobile_number'),
		SubmissionStatus: text('submission_status'),
		ReleaseDate: timestamp('release_date', { withTimezone: true }).notNull(),
		SubmissionDate: timestamp('submission_date', {
			withTimezone: true
		}),
		ExpiryDate: timestamp('expiry_date', { withTimezone: true }).notNull()
	}
);

export type SelectTnpsUserDetails =
	typeof qualtrixTnpsUserDetailsTableSchema.$inferSelect;
