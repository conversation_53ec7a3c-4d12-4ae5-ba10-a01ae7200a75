import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../shared/schemas/api/responses.schema';
import {
	type GetCacheByKeyRes,
	type GetCacheListRes,
	cacheByKeyReqSchema,
	deleteCacheByKeyResSchema,
	getCacheByKeyResSchema,
	getCacheListResSchema
} from '../schemas/api/redis.schema';
import Redis from '../services/redis.service';

const redisRoutes = new Elysia({ prefix: '/redis' })
	.get(
		'/keys',
		async (): Promise<GetCacheListRes> => {
			return await new Redis().getCacheKeys();
		},
		{
			detail: {
				description: 'Retrieve cache keys',
				tags: ['Internal']
			},
			response: {
				200: getCacheListResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/value',
		async (ctx): Promise<GetCacheByKeyRes> => {
			return await new Redis().getCacheValueByKey(ctx.query);
		},
		{
			detail: {
				description: 'Retrieve cache value by key',
				tags: ['Internal']
			},
			query: cacheByKeyReqSchema,
			response: {
				200: getCacheByKeyResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.delete(
		'/value',
		async ctx => {
			return await new Redis().deleteCacheByKey(ctx.query);
		},
		{
			detail: {
				description: 'Delete cache by key',
				tags: ['Internal']
			},
			query: cacheByKeyReqSchema,
			response: {
				200: deleteCacheByKeyResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default redisRoutes;
