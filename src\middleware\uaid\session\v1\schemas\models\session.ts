import { index, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm/sql';
import randomString from 'random-string-gen';
import { identityDbSchema } from '../../../../identity/v1/schemas/models/identity';

export const userAuthDbSchema = pgTable(
	'mw_user_auth',
	{
		Id: text('id')
			.primaryKey()
			.notNull()
			.$defaultFn(() => randomString(15)),
		UserId: varchar('user_id', { length: 50 })
			.notNull()
			.references(() => identityDbSchema.UserId, { onDelete: 'cascade' }),
		DeviceId: varchar('device_id', { length: 150 }),
		CreatedAt: timestamp('created_at', { mode: 'date', withTimezone: true })
			.notNull()
			.default(sql`now()`)
	},
	t => [
		index('idx_mw_user_auth_user_id').on(t.UserId),
		index('idx_mw_user_auth_device_id').on(t.DeviceId)
	]
);

export type SelectUserAuth = typeof userAuthDbSchema.$inferSelect;

export const sessionDbSchema = pgTable(
	'mw_session',
	{
		Id: text('id').notNull().primaryKey(),
		UserAuthId: text('user_auth_id')
			.notNull()
			.references(() => userAuthDbSchema.Id, { onDelete: 'cascade' }),
		ExpiredAt: timestamp('expired_at', {
			withTimezone: true,
			mode: 'date'
		}).notNull()
	},
	t => [index('idx_mw_session_user_auth_id').on(t.UserAuthId)]
);

export type SelectSession = typeof sessionDbSchema.$inferSelect;
