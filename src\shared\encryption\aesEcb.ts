import * as nodeCrypto from 'node:crypto';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';

// Encryption using AES-128-ECB
const getKeyBuffer = (secret: string) => {
	const encoder = new TextEncoder();
	const keyBuffer = encoder.encode(secret);

	if (keyBuffer.length !== 16) {
		throw new Error('Key must be exactly 16 bytes for AES-128');
	}

	return keyBuffer;
};

export const encryptAesEcb = (strToEncrypt: string, secret: string) => {
	try {
		const keyBuffer = getKeyBuffer(secret);
		const cipher = nodeCrypto.createCipheriv('aes-128-ecb', keyBuffer, null);
		cipher.setAutoPadding(true); // Ensures PKCS5Padding behavior

		let encrypted = cipher.update(strToEncrypt, 'utf-8', 'hex');
		encrypted += cipher.final('hex');
		return parseHexStr(Buffer.from(encrypted, 'hex'));
	} catch (error) {
		throw new UE_ERROR(
			`Invalid string to encrypt: ${error}`,
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}
};

export const decryptAesEcb = (strToDecrypt: string, secret: string): string => {
	try {
		const keyBuffer = getKeyBuffer(secret);

		const decipher = nodeCrypto.createDecipheriv(
			'aes-128-ecb',
			keyBuffer,
			null
		);
		decipher.setAutoPadding(true);

		let decrypted = decipher.update(strToDecrypt, 'hex', 'utf-8');
		decrypted += decipher.final('utf-8');
		return decrypted;
	} catch (error) {
		throw new UE_ERROR(
			`Invalid string to decrypt: ${error}`,
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}
};

const parseHexStr = (buf: Buffer): string => {
	return buf.toString('hex').toLowerCase(); // Ensures lowercase hex, like Java
};
