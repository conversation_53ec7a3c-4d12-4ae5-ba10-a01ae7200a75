import { type Static, t } from 'elysia';

export const wso2NovaCTTSchema = t.Object({
	ListOfTmEaiSttCreate: t.Object({
		TmAccountIntegration: t.Object({
			AccountNo: t.MaybeEmpty(t.String({ examples: ['1-R9QA3P1'] })),
			ListOfTmTroubleTicketIntegration: t.Object({
				TmTroubleTicketIntegration: t.Object({
					Id: t.MaybeEmpty(t.String({ examples: ['1'] })),
					BillingAccountNo: t.MaybeEmpty(
						t.String({ examples: ['**********'] })
					),
					Category: t.MaybeEmpty(t.String({ examples: ['Failure'] })),
					AssetId: t.MaybeEmpty(t.String({ examples: ['1-********'] })),
					BillingAccountRowID: t.MaybeEmpty(
						t.String({ examples: ['1-1EFS213'] })
					),
					ContactId: t.MaybeEmpty(t.String({ examples: ['1-1EFS213'] })),
					Description: t.MaybeEmpty(
						t.String({
							examples: [
								'Fault Location= Customer Site, Condition=Failure Remark: Outside premise and check connection between FDP and ONU. Check optical power and replace damaged connectors/cables'
							]
						})
					),
					RelatedSRRowID: t.MaybeEmpty(t.String({ examples: ['1-1EFS213'] })),
					Owner: t.MaybeEmpty(t.String({ examples: ['WIDER_TMUC_TECH_SOC2'] })),
					Product: t.MaybeEmpty(t.String({ examples: [''] })),
					TTNumber: t.MaybeEmpty(t.String({ examples: ['1-*********'] })),
					Severity: t.MaybeEmpty(t.String({ examples: ['4-Low'] })),
					Source: t.MaybeEmpty(t.String({ examples: ['EASY FIX'] })),
					Status: t.MaybeEmpty(t.String({ examples: ['In Progress'] })),
					SymptomCode: t.MaybeEmpty(
						t.String({ examples: ['Line Disconnect'] })
					),
					TMCategory: t.MaybeEmpty(t.String({ examples: [''] })),
					ContactDetailReported: t.MaybeEmpty(
						t.String({ examples: ['1-1EFS213'] })
					),
					TicketSubType: t.MaybeEmpty(t.String({ examples: ['Reactive'] })),
					TicketType: t.MaybeEmpty(
						t.String({ examples: ['Customer Trouble Ticket'] })
					),
					CCPChargingMethod: t.MaybeEmpty(
						t.String({
							examples: ['Renewal Service Contract']
						})
					),
					TMAssetServicePointId: t.MaybeEmpty(
						t.String({ examples: ['1-1EFS213'] })
					),
					ServiceRowID: t.MaybeEmpty(t.String({ examples: ['1-1EFS213'] })),
					CustomerRowID: t.MaybeEmpty(t.String({ examples: ['1-1EFS213'] })),
					TMPreferredAcknowledgement: t.MaybeEmpty(
						t.String({ examples: ['Email'] })
					),
					ListOfTmActionIntegration3: t.Object({
						TmActionIntegration3: t.Object({
							Id: t.MaybeEmpty(t.String({ examples: ['1'] })),
							ActivityId: t.MaybeEmpty(t.String({ examples: ['1'] })),
							ActivityStatus: t.MaybeEmpty(
								t.String({ examples: ['Un-Scheduled'] })
							),
							ActivityType: t.MaybeEmpty(
								t.String({ examples: ['RNO Troubleshooting'] })
							),
							ActivityCreated: t.MaybeEmpty(t.String()),
							ActivityDescription: t.MaybeEmpty(t.String({ examples: [''] })),
							ActivityPlannedStart: t.MaybeEmpty(t.String({ examples: [''] })),
							Owner: t.MaybeEmpty(t.String({ examples: [''] }))
						})
					}),
					ListOfTmTroubleTicketNotesIntegration: t.Object({
						TmTroubleTicketNotesIntegration: t.Object({
							Id: t.MaybeEmpty(t.String({ examples: ['1'] })),
							CreatedBy: t.MaybeEmpty(t.String({ examples: ['EAI_USER'] })),
							NoteId: t.MaybeEmpty(t.String({ examples: ['1'] })),
							NoteDescription: t.MaybeEmpty(
								t.String({
									examples: [
										'Fault Location= Customer Site, Condition=Failure Remark: Outside premise and check connection between FDP and ONU. Check optical power and replace damaged connectors/cables'
									]
								})
							),
							NoteType: t.MaybeEmpty(t.String({ examples: ['Note'] })),
							CreatedByPosition: t.MaybeEmpty(t.String({ examples: ['EAI'] })),
							DateCreated: t.MaybeEmpty(t.String({ examples: [''] }))
						})
					})
				})
			})
		})
	})
});

export type Wso2NovaCTT = Static<typeof wso2NovaCTTSchema>;
