import { type Static, t } from 'elysia';

export const wso2OrderMonitoringReqSchema = t.Object({
	OrderMonitoringRequest: t.Object({
		serviceID: t.Optional(t.String()),
		OrderNumber: t.String()
	})
});

export const wso2OrderMonitoringResSchema = t.Object({
	Status: t.Object({
		Type: t.String(),
		Code: t.String(),
		Message: t.String()
	}),
	Response: t.Object({
		OrderMonitoringResponse: t.Object({
			SiebelOrderID: t.String({
				description: 'Order ID from Siebel',
				example: '1-105920787617'
			}),
			DiCEOrderNumber: t.String({
				description: 'Order number from DiCE system',
				example: '1-25-TV1779614'
			}),
			OrderStatus: t.String({
				description: 'Latest status of the order',
				example: 'Completed'
			}),
			FraudFlag: t.Optional(
				t.String({
					description: 'Fraud check flag (Y = Yes, N = No)',
					example: 'N'
				})
			)
		})
	})
});

export type Wso2OrderMonitoringReq = Static<
	typeof wso2OrderMonitoringReqSchema
>;
export type Wso2OrderMonitoringRes = Static<
	typeof wso2OrderMonitoringResSchema
>;
