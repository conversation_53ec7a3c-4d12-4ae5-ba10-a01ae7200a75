import { type Static, t } from 'elysia';
import { wso2BaseResponseSchema } from '../../../helper/schemas/api/wso2Base.schema';

const wso2NovaBillingProfileReqSchema = t.Object({
	RetrieveBillingProfileRequest: t.Object({
		BillingAccountNo: t.String()
	})
});

export type Wso2NovaBillingProfileReq = Static<
	typeof wso2NovaBillingProfileReqSchema
>;

const wso2CustomerAddressDetails = t.Object({
	AddressDetails: t.Object({
		Id: t.Nullable(t.String()),
		ApartmentNumber: t.Nullable(t.String()),
		City: t.Nullable(t.String()),
		StreetName: t.Nullable(t.String()),
		Section: t.Nullable(t.String()),
		State: t.Nullable(t.String()),
		BuildingName: t.Nullable(t.String()),
		FloorNo: t.Nullable(t.String()),
		PostalCode: t.Nullable(t.String()),
		StreetType: t.Nullable(t.String()),
		Country: t.Nullable(t.String()),
		ForeignCountry: t.Nullable(t.String()),
		ForeignState: t.Nullable(t.String()),
		ForeignAddrFlag: t.Nullable(t.String()),
		AddressType: t.Nullable(t.String())
	})
});

export type Wso2CustomerAddressDetails = Static<
	typeof wso2CustomerAddressDetails
>;

export const wso2BillingAccountsResObjSchema = t.Object({
	Id: t.Nullable(t.String()),
	BillingAccountNo: t.Nullable(t.String()),
	AccountClass: t.Nullable(t.String()),
	CSN: t.Nullable(t.String()),
	CurrencyCode: t.Nullable(t.String()),
	BillingName: t.Nullable(t.String()),
	FirstName: t.Nullable(t.String()),
	JobTitle: t.Nullable(t.String()),
	Division: t.Nullable(t.String()),
	EmailAddress: t.Nullable(t.String()),
	ContactNo: t.Nullable(t.String()),
	Status: t.Nullable(t.String()),
	Type: t.Nullable(t.String()),
	CustomerAddressDetails: wso2CustomerAddressDetails,
	InvoiceProfileDetails: t.Object({
		InvoiceProfile: t.Object({
			Id: t.Nullable(t.String()),
			DDAccountHolderId: t.Nullable(t.String()),
			AddressId: t.Nullable(t.String()),
			ApartmentNumber: t.Nullable(t.String()),
			DDBankAccountNumber: t.Nullable(t.String()),
			DDBankAccountType: t.Nullable(t.String()),
			BankLanguageCode: t.Nullable(t.String()),
			BankName: t.Nullable(t.String()),
			BillCycle: t.Nullable(t.String()),
			BillFrequency: t.Nullable(t.String()),
			BillType: t.Nullable(t.String()),
			City: t.Nullable(t.String()),
			TMSalesPTT: t.Nullable(t.String()),
			Country: t.Nullable(t.String()),
			EmailBillTo: t.Nullable(t.String()),
			MediaType: t.Nullable(t.String()),
			Name: t.Nullable(t.String()),
			PaymentMethod: t.Nullable(t.String()),
			PostalCode: t.Nullable(t.String()),
			State: t.Nullable(t.String()),
			StreetAddress: t.Nullable(t.String()),
			StreetAddress2: t.Nullable(t.String()),
			BuildingName: t.Nullable(t.String()),
			FloorNo: t.Nullable(t.String()),
			StreetType: t.Nullable(t.String()),
			AddressType: t.Nullable(t.String()),
			MaximumLimitAmount: t.Nullable(t.String()),
			CardType: t.Nullable(t.String()),
			CardNumber: t.Nullable(t.String()),
			CardHolderName: t.Nullable(t.String()),
			CardExpiryDateMonth: t.Nullable(t.String()),
			CardExpiryDateYear: t.Nullable(t.String()),
			MotherMaidenName: t.Nullable(t.String()),
			DDRelationship: t.Nullable(t.String()),
			CCRelationship: t.Nullable(t.String()),
			DDFrequency: t.Nullable(t.String()),
			DDFrequencyLimit: t.Nullable(t.String()),
			DDBankAccountHolderIdNew: t.Nullable(t.String()),
			DDBankAccountHolderIdOld: t.Nullable(t.String()),
			DDBankAccountHolderIdTypeNew: t.Nullable(t.String()),
			DDBankAccountHolderIdTypeOld: t.Nullable(t.String()),
			EmailBillCc: t.Nullable(t.String()),
			MobileNumber: t.String()
		})
	})
});

export type Wso2BillingAccountsResObj = Static<
	typeof wso2BillingAccountsResObjSchema
>;

export const wso2NovaBillingProfileResSchema = t.Object({
	...wso2BaseResponseSchema.properties,
	RetrieveBillingProfileResponse: t.Object({
		BillingProfileDetails: t.Object({
			BillingAccounts: wso2BillingAccountsResObjSchema
		})
	})
});

export type Wso2NovaBillingProfileRes = Static<
	typeof wso2NovaBillingProfileResSchema
>;
