import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type UserTaskAddRes,
	addUserTaskReqSchema,
	triggerUserTaskReqSchema,
	userTaskResSchema
} from '../schemas/api/userTask.schema';
import UserTask from '../services/userTask.service';
const userTaskV1Routes = new Elysia({ prefix: '/user-task' })
	.resolve(() => ({
		UserTask: new UserTask(randomUUID())
	}))
	.post(
		'/',
		async (ctx): Promise<UserTaskAddRes> => {
			const res = await ctx.UserTask.createUserTask(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Add User Task For Temporal.<br><br><b>Table:</b> temporal_user_task',
				tags: ['Temporal']
			},
			body: addUserTaskReqSchema,
			response: {
				201: userTaskResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/trigger-signal',
		async (ctx): Promise<BaseResponse> => {
			const res = await ctx.UserTask.triggerSignal(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Trigger User Task For Temporal.<br><br><b>Target System:</b> Temporal',
				tags: ['Temporal']
			},
			body: triggerUserTaskReqSchema,
			response: {
				201: baseResponseSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default userTaskV1Routes;
