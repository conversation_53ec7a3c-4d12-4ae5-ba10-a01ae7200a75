import { randomUUID } from 'node:crypto';
import { logger } from '@bogeychan/elysia-logger';
import { cors } from '@elysiajs/cors';
import { opentelemetry } from '@elysiajs/opentelemetry';
import swagger from '@elysiajs/swagger';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-node';
import { Elysia } from 'elysia';
import { closeDbConnection, createDbConnection } from './config/db.config';
import { envConfig } from './config/env.config';
import { pinoLog } from './config/pinoLog.config';
import { StatusCodeEnum } from './enum/statusCode.enum';
import { UE_ERROR } from './middleware/error';
import { apiModuleRoutes } from './routes';
import { scalarRoutes, tagsArray } from './shared/scalar';
import type { ErrorBaseResponse } from './shared/schemas/api/responses.schema';

// Initialize app
const app = new Elysia({
	name: '@grotto/logging',
	serve: { idleTimeout: 255 }
});

// OpenTelemetry
app.use(
	opentelemetry({
		spanProcessors: [
			new BatchSpanProcessor(
				new OTLPTraceExporter({
					url: 'https://api.axiom.co/v1/traces',
					headers: {
						Authorization: `Bearer ${process.env.AXIOM_TOKEN}`,
						'X-Axiom-Dataset': `${process.env.AXIOM_DATASET}`
					}
				})
			)
		]
	})
);

// CORS
app.use(
	cors({
		origin: '*',
		allowedHeaders: [
			'Origin',
			'Content-Type',
			'Accept',
			'Authorization',
			'Source',
			'Segment',
			'X-Api-Key',
			'Device-Id'
		],
		methods: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE', 'PATCH', 'OPTION'],
		exposeHeaders: ['Content-Length'],
		maxAge: 600,
		credentials: true
	})
);

// Request ID & Error Handling
app
	.derive(() => ({
		requestId: randomUUID()
	}))
	.error({
		UE_ERROR
	})
	.onError(({ code, error, set, requestId }): ErrorBaseResponse => {
		let errorResponse: ErrorBaseResponse;
		switch (code) {
			case 'UE_ERROR': {
				set.status = error.enumCode;
				errorResponse = {
					Success: false,
					Name: error.name,
					Code: error.enumCode,
					RequestId: requestId ?? '',
					IntegrationId: error.integrationId,
					Message: error.message,
					Cause: error.cause
				};
				break;
			}
			case 'PARSE': {
				set.status = StatusCodeEnum.BAD_REQUEST_ERROR;
				errorResponse = {
					Success: false,
					Name: 'PARSE',
					Code: StatusCodeEnum.BAD_REQUEST_ERROR,
					RequestId: requestId ?? '',
					Message:
						'Invalid JSON or unexpected content type. Please ensure correct data format is sent.',
					Cause: error
				};
				break;
			}
			case 'VALIDATION': {
				set.status = StatusCodeEnum.BAD_REQUEST_ERROR;
				errorResponse = {
					Success: false,
					Name: 'VALIDATION',
					Code: StatusCodeEnum.BAD_REQUEST_ERROR,
					RequestId: requestId ?? '',
					Message:
						'Missing or invalid fields in the request. Please fix data to match the schema rules.',
					Cause: JSON.parse(error.message)
				};
				break;
			}
			case 'NOT_FOUND': {
				set.status = StatusCodeEnum.NOT_FOUND_ERROR;
				errorResponse = {
					Success: false,
					Name: 'NOT_FOUND',
					Code: StatusCodeEnum.NOT_FOUND_ERROR,
					RequestId: requestId ?? '',
					Message:
						'Path does not exist. You may have mistyped the url or the api may not be available',
					Cause: String(error)
				};
				break;
			}
			case 'INTERNAL_SERVER_ERROR': {
				set.status = 500;
				errorResponse = {
					Success: false,
					Name: 'INTERNAL_SERVER',
					Code: 500,
					RequestId: requestId ?? '',
					Message:
						'Something went wrong on our side. Please try again or contact support if the problem continues.',
					Cause: String(error)
				};
				break;
			}
			default: {
				set.status = StatusCodeEnum.UNKNOWN_ERROR;
				errorResponse = {
					Success: false,
					Name: 'UNKNOWN_ERROR',
					Code: StatusCodeEnum.UNKNOWN_ERROR,
					RequestId: requestId ?? '',
					Message:
						"Something went wrong, and we're not sure what happened. Please try again or contact support.",
					Cause: String(error)
				};
				break;
			}
		}

		pinoLog.error({ errorResponse });
		return errorResponse;
	});

// Logger
app.use(
	logger({
		timestamp: true,
		level: process.env.ENVIRONMENT !== 'production' ? 'debug' : 'info',
		transport:
			process.env.ENVIRONMENT === 'dev'
				? { target: 'pino-pretty', options: { colorize: true } }
				: undefined,
		customProps: ctx => ({
			customRequest: {
				requestId: ctx.requestId,
				method: ctx.request.method,
				url: ctx.request.url,
				headers: ctx.request.headers,
				body: ctx.body
			},
			customResponse: {
				requestId: ctx.requestId,
				body: ctx.response ?? ctx.error
			}
		})
	})
);

// Ensure database connection is available
app.decorate('postgresql', createDbConnection());

// Routes
app
	.use(scalarRoutes)
	.use(apiModuleRoutes)
	.get('/health', () => ({ status: 'ok' }), { detail: { hide: true } });

// Environment-specific configurations
if (process.env.ENVIRONMENT === 'production') {
	process.env.NODE_TLS_REJECT_UNAUTHORIZED = '1';
	pinoLog.info('🔒 SSL verification enabled for production environment');
} else {
	process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
	pinoLog.info(
		`🔒 SSL verification disabled for ${process.env.ENVIRONMENT} environment`
	);

	app.use(
		swagger({
			scalarVersion: 'latest',
			documentation: {
				info: { title: 'Universal Engine API', version: '0.0.1' },
				tags: tagsArray,
				servers:
					envConfig().SERVER_PREFIX_PATH !== ''
						? [
								{
									url: envConfig().SERVER_PREFIX_PATH,
									description: 'UE API Server'
								}
							]
						: undefined
			}
		})
	);
}

// Start server
app.listen(3000);

// Log startup information
pinoLog.info(
	`🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`
);

// Graceful shutdown
const shutdown = async () => {
	pinoLog.info('Shutting down gracefully...');
	await closeDbConnection();
	app
		.stop()
		.then(() => {
			pinoLog.info('Server stopped');
			process.exit(0);
		})
		.catch((err: Error) => {
			pinoLog.error('Error during shutdown:', err);
			process.exit(1);
		});
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);
