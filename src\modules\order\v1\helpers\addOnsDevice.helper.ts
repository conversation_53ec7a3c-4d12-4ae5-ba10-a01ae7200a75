import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type {
	ListOfActivitySchema,
	OrderLineItemsSchema,
	OrderSchema
} from '../../../../integration/wso2/order/schemas/api/wso2CreateAddOnsOrder';
import type { Wso2ServiceAccountRes } from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { AddOnsOrderReq } from '../schemas/api/addOnsOrder.schema';
import type { SelectSiebelProductMap } from '../schemas/db/siebelProductMap.schema';

class AddOnsDeviceHelper {
	private integrationId: string;
	private HIGH_SPEED_INTERNET = 'High Speed Internet';
	private PART_NUM_UNIFI_TV_RESIDENTIAL = 'PR000200';
	private PART_NUM_UNIFI_PLAYTV_LITE = 'PR007670';

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	countCurrentDeviceServices(wso2SARes: Wso2ServiceAccountRes): number {
		let count = 0;
		for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
			for (const moli of sa.ServiceAccountMoli ?? []) {
				for (const oli of moli.ServiceAccountOli ?? []) {
					if (
						oli.ProductName?.includes('Device/Service') &&
						oli.Type === 'Service'
					) {
						count++;
					}
				}
			}
		}
		return count;
	}

	countTotalUpbDevices(wso2SARes: Wso2ServiceAccountRes): number {
		let count = 0;
		for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
			for (const moli of sa.ServiceAccountMoli ?? []) {
				for (const oli of moli.ServiceAccountOli ?? []) {
					if (
						oli.ProductName?.includes('unifi Plus Box Add On') &&
						oli.Type === 'Equipment'
					) {
						count++;
					}
				}
			}
		}
		return count;
	}

	addExistingServiceLineItem(
		req: AddOnsOrderReq,
		serviceAccounts: Wso2ServiceAccountRes,
		orderId: string
	): OrderSchema {
		// Return void to indicate in-place mutation
		const serviceAccount = serviceAccounts?.Response?.ServiceAccount?.[0];

		// Set general order information
		const order: OrderSchema = {
			AccountId: req.CustomerInfo.AccountNo,
			BillingAccountId: serviceAccount?.BillingAccountId || '',
			BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
			ContactId: req.CustomerInfo.ContactId,
			OrderType: 'Modify',
			PriceList: 'TM Consumer Price List',
			TMOrderSource: 'Customer Portal',
			TMPromotionName: '',
			OrderNumber: orderId,
			TMDiCEOrderNumber: orderId,
			TMServingExchange:
				serviceAccount?.['TmCutAssetMgmt-ServiceMeterIntegration']
					?.TMExchangeName || '',
			Description: req.Products[0].ProductName,
			ServicePointId: serviceAccount?.ServicePointId || '',
			DeliveryAddressId:
				serviceAccount?.['TmCutAssetMgmt-ServiceMeterIntegration']
					?.TmCutAddressIntegration?.Id || '',
			RecipientName: req.CustomerInfo.FullName,
			RecipientContactNum: req.CustomerInfo.BillingContactNo,
			ListOfOrderLineItems: { OrderLineItems: [] }
		};

		// Append OrderLineItems
		for (const serviceAccountMoli of serviceAccount?.ServiceAccountMoli || []) {
			const orderLineItems1Tier: OrderLineItemsSchema = {
				ActionCode: '-',
				AssetIntegrationId: serviceAccountMoli.IntegrationId,
				BillingAccountId: serviceAccountMoli.BillingAccountId || '',
				PartNumber: serviceAccountMoli.ProductPartNumber,
				ProdPromInstanceId: serviceAccountMoli.ProdPromInstanceId,
				Product: serviceAccountMoli.ProductName || '',
				ProductId: serviceAccountMoli.ProductId as string,
				ProdPromName: serviceAccount?.ProductPartNumber,
				ProductType: 'PRODUCT',
				ServiceAccountId: req.CustomerInfo.AccountNo,
				ServicePointId: serviceAccountMoli.ServicePointId,
				ReservationStatus: 'Reserve Success',
				Qty: serviceAccountMoli.Quantity || '1',
				TMServiceTaxFlag: serviceAccountMoli.TMServiceTaxFlag || 'N',
				CfgStateCode: 'New Item',
				ServiceId: serviceAccountMoli.SerialNumber,
				BillingProfileId: serviceAccount?.PrimaryBillingProfileId as string,
				OrderLineItems: []
			};

			const lineItemsList2Tier: OrderLineItemsSchema[] = [];
			for (const serviceAccountOli of serviceAccountMoli?.ServiceAccountOli ||
				[]) {
				if (
					serviceAccountMoli.ProductPartNumber !==
						this.PART_NUM_UNIFI_TV_RESIDENTIAL &&
					serviceAccountMoli.ProductPartNumber !==
						this.PART_NUM_UNIFI_PLAYTV_LITE
				) {
					const orderLineItems2Tier: OrderLineItemsSchema = {
						ActionCode: '-',
						BillingAccountId: serviceAccountOli.BillingAccountId ?? '',
						PartNumber: serviceAccountOli.ProductPartNumber,
						Product: serviceAccountOli.ProductName ?? '',
						ProductType: 'Product',
						ServiceAccountId: req.CustomerInfo.AccountNo,
						ReservationStatus: 'Reserve Success',
						CfgStateCode: 'New Item',
						AssetIntegrationId: serviceAccountOli.IntegrationId,
						BillingProfileId: serviceAccount?.PrimaryBillingProfileId,
						ProdPromInstanceId: serviceAccountOli.ProdPromInstanceId,
						ProdPromName: serviceAccountOli.ProdPromName,
						Qty: serviceAccountOli.Quantity,
						ServiceId: serviceAccountOli.SerialNumber,
						ServicePointId: serviceAccountOli.ServicePointId,
						TMServiceTaxFlag: 'N'
					};

					// to set assets if available
					if (
						serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
							'TmAssetMgmt-AssetXaIntegration'
						]?.some(xaAsset => xaAsset?.Name)
					) {
						orderLineItems2Tier.ListOfOrderItemXa = {
							OrderItemXa:
								serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
									'TmAssetMgmt-AssetXaIntegration'
								]?.map(xaAsset => ({
									ActionCode: '-',
									Name: xaAsset.Name,
									TextValue: xaAsset.Value
								})) ?? []
						};
					}

					lineItemsList2Tier.push(orderLineItems2Tier);
				} else {
					// to set second tier
					let orderLineItems2Tier: OrderLineItemsSchema =
						{} as OrderLineItemsSchema;
					const secondTierOlis = serviceAccountMoli?.ServiceAccountOli?.find(
						oli => oli.Id === serviceAccountOli.ParentAssetId
					);
					if (!secondTierOlis) {
						orderLineItems2Tier = {
							ActionCode: '-',
							BillingAccountId: serviceAccountOli.BillingAccountId ?? '',
							PartNumber: serviceAccountOli.ProductPartNumber,
							Product: serviceAccountOli.ProductName ?? '',
							ProductType: 'Product',
							ServiceAccountId: req.CustomerInfo.AccountNo,
							ReservationStatus: 'Reserve Success',
							CfgStateCode: 'New Item',
							AssetIntegrationId: serviceAccountOli.IntegrationId,
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId,
							ProdPromInstanceId: serviceAccountOli.ProdPromInstanceId,
							ProdPromName: serviceAccountOli.ProdPromName,
							Qty: serviceAccountOli.Quantity,
							ServiceId: serviceAccountOli.SerialNumber,
							ServicePointId: serviceAccountOli.ServicePointId,
							TMServiceTaxFlag: 'N'
						};

						// to set assets if available
						if (
							serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
								'TmAssetMgmt-AssetXaIntegration'
							]?.some(xaAsset => xaAsset?.Name)
						) {
							orderLineItems2Tier.ListOfOrderItemXa = {
								OrderItemXa:
									serviceAccountOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
										'TmAssetMgmt-AssetXaIntegration'
									]?.map(xaAsset => ({
										ActionCode: '-',
										Name: xaAsset.Name,
										TextValue: xaAsset.Value
									})) ?? []
							};
						}
					}

					// to set third tier
					const thirdTierOlis = serviceAccountMoli?.ServiceAccountOli?.filter(
						oli => oli.ParentAssetId === serviceAccountOli.Id
					);
					const orderLineItems3Tier: OrderLineItemsSchema[] = [];
					for (const thirdTierOli of thirdTierOlis ?? []) {
						const orderLineItem3Tier: OrderLineItemsSchema = {
							ActionCode: '-',
							BillingAccountId: thirdTierOli.BillingAccountId ?? '',
							PartNumber: thirdTierOli.ProductPartNumber,
							Product: thirdTierOli.ProductName ?? '',
							ProductType: 'Product',
							ServiceAccountId: req.CustomerInfo.AccountNo,
							ReservationStatus: 'Reserve Success',
							CfgStateCode: 'New Item',
							AssetIntegrationId: thirdTierOli.IntegrationId,
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId,
							ProdPromInstanceId: thirdTierOli.ProdPromInstanceId,
							ProdPromName: thirdTierOli.ProdPromName,
							Qty: thirdTierOli.Quantity,
							ServiceId: thirdTierOli.SerialNumber,
							ServicePointId: thirdTierOli.ServicePointId,
							TMServiceTaxFlag: 'N'
						};

						// to set assets if available
						if (
							thirdTierOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
								'TmAssetMgmt-AssetXaIntegration'
							]?.some(xaAsset => xaAsset?.Name)
						) {
							orderLineItem3Tier.ListOfOrderItemXa = {
								OrderItemXa:
									thirdTierOli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
										'TmAssetMgmt-AssetXaIntegration'
									]?.map(xaAsset => ({
										ActionCode: '-',
										Name: xaAsset.Name,
										TextValue: xaAsset.Value
									})) ?? []
							};
						}
						orderLineItems3Tier.push(orderLineItem3Tier);
					}

					// Ensure no empty objects are added
					if (Object.keys(orderLineItems2Tier).length > 0) {
						if (orderLineItems3Tier.length > 0) {
							orderLineItems2Tier.OrderLineItems =
								orderLineItems2Tier.OrderLineItems || [];
							orderLineItems2Tier.OrderLineItems?.push(...orderLineItems3Tier);
						}
						lineItemsList2Tier.push(orderLineItems2Tier);
					}
				}
			}

			orderLineItems1Tier.OrderLineItems = lineItemsList2Tier;
			order.ListOfOrderLineItems.OrderLineItems.push(orderLineItems1Tier);
		}

		return order;
	}

	addNewServiceLineItem(
		req: AddOnsOrderReq,
		order: OrderSchema,
		wso2SARes: Wso2ServiceAccountRes,
		siebelProductMap: SelectSiebelProductMap[],
		reservationNo?: string,
		activityId?: string
	): void {
		let currentDeviceServiceCount = this.countCurrentDeviceServices(wso2SARes);
		const serviceAccount = wso2SARes?.Response?.ServiceAccount?.[0];
		for (const product of req.Products) {
			++currentDeviceServiceCount;

			if (
				req.Category === AddOnsRequestCategoryEnum.MESH_WIFI ||
				req.Category === AddOnsRequestCategoryEnum.SMART_DEVICE ||
				req.Category === AddOnsRequestCategoryEnum.SMART_HOME
			) {
				const orderLineItems: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					ProdPromInstanceId: '',
					Product: `Device/Service ${currentDeviceServiceCount}`,
					ProdPromName: '',
					ProductType: 'Product',
					ServicePointId: '',
					Qty: '1',
					ReservationStatus:
						req.Category === AddOnsRequestCategoryEnum.SMART_DEVICE ||
						req.Category === AddOnsRequestCategoryEnum.SMART_HOME
							? 'Successful'
							: 'Reserve Success',
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'New Item',
					ServiceId:
						req.Category === AddOnsRequestCategoryEnum.SMART_DEVICE ||
						req.Category === AddOnsRequestCategoryEnum.SMART_HOME
							? reservationNo
							: '',
					BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
					ListOfOrderItemXa: {
						OrderItemXa: [
							{
								ActionCode: 'Add',
								Name: 'Device Name',
								TextValue: product.ProductName
							},
							{
								ActionCode: 'Add',
								Name: 'Device Price',
								TextValue: product.ProductPrice
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Period',
								TextValue: product.ContractTerm
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Type',
								TextValue: product.LeasingType
							},
							{
								ActionCode: 'Add',
								Name: 'Monthly RC',
								TextValue: product.MonthlyCommitment
							},
							{
								ActionCode: 'Add',
								Name: 'Partner ID',
								TextValue: product.PartnerId
							},
							{
								ActionCode: 'Add',
								Name: 'Purchase Price',
								TextValue: product.PurchasePrice
							}
						]
					}
				};

				const orderLineItemsWithCommitment: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					ProdPromInstanceId: '',
					Product: `Device/Service ${currentDeviceServiceCount} Commitment`,
					ProdPromName: '',
					ProductType: 'Product',
					ServicePointId: '',
					ReservationStatus: 'Reserve Success',
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'New Item',
					ServiceId: '',
					BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
					ListOfOrderItemXa: {
						OrderItemXa: [
							{
								ActionCode: 'Add',
								Name: 'Contract Period',
								TextValue: product.ContractTerm
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Type',
								TextValue: product.LeasingType
							}
						]
					}
				};

				for (const oli of order.ListOfOrderLineItems.OrderLineItems) {
					if (oli?.Product?.includes(this.HIGH_SPEED_INTERNET)) {
						oli.OrderLineItems?.push(orderLineItems);
						oli.OrderLineItems?.push(orderLineItemsWithCommitment);
					}
				}
			} else if (req.Category === AddOnsRequestCategoryEnum.BLACKNUT) {
				const orderLineItems: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					Product: `Device/Service ${currentDeviceServiceCount}`,
					ServiceAccountId: req.CustomerInfo.AccountNo,
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'Engine Picked Item',
					ListOfOrderItemXa: {
						OrderItemXa: [
							{
								ActionCode: 'Add',
								Name: 'Device Name',
								TextValue: product.ProductName
							},
							{
								ActionCode: 'Add',
								Name: 'Device Price',
								TextValue: product.ProductPrice
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Period',
								TextValue: product.ContractTerm
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Type',
								TextValue: product.LeasingType
							},
							{
								ActionCode: 'Add',
								Name: 'Monthly RC',
								TextValue: product.MonthlyCommitment
							},
							{
								ActionCode: 'Add',
								Name: 'Partner ID',
								TextValue: product.PartnerId
							},
							{
								ActionCode: 'Add',
								Name: 'Purchase Price',
								TextValue: product.PurchasePrice
							},
							{
								ActionCode: 'Add',
								Name: 'Smart Device'
							}
						]
					}
				};

				if (product.DeliveryPartner?.toUpperCase() === 'MMAG') {
					orderLineItems.ReservationStatus = 'Successful';
					orderLineItems.ServiceId = reservationNo;
					orderLineItems.ListOfOrderItemXa?.OrderItemXa?.push({
						ActionCode: 'Add',
						Name: 'Delivery Partner',
						TextValue: `${product.DeliveryPartner}`
					});
				} else {
					orderLineItems.ListOfOrderItemXa?.OrderItemXa?.push({
						ActionCode: 'Add',
						Name: 'Delivery Partner'
					});
				}

				const orderLineItemsWithCommitment: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					Product: `Device/Service ${currentDeviceServiceCount} Commitment`,
					ServiceAccountId: req.CustomerInfo.AccountNo,
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'Engine Picked Item',
					ListOfOrderItemXa: {
						OrderItemXa: [
							{
								ActionCode: 'Add',
								Name: 'Contract Period',
								TextValue: product.ContractTerm
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Type',
								TextValue: product.LeasingType
							}
						]
					}
				};

				for (const oli of order.ListOfOrderLineItems.OrderLineItems) {
					if (oli.Product?.includes(this.HIGH_SPEED_INTERNET)) {
						oli.OrderLineItems?.push(orderLineItems);
						oli.OrderLineItems?.push(orderLineItemsWithCommitment);
					}
				}
			} else if (req.Category === AddOnsRequestCategoryEnum.UPB) {
				if (this.countTotalUpbDevices(wso2SARes) > 3) {
					throw new UE_ERROR(
						'Maximum UPB addons exceeded. Only 3 UPB addons allowed',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0020'
						}
					);
				}
				const upb2ndTierProductMapping = siebelProductMap.find(obj =>
					obj.ProductName.startsWith(
						`unifi Plus Box Add On ${currentDeviceServiceCount}`
					)
				);
				const upb3rdTierProductMapping = siebelProductMap.find(obj =>
					obj.ProductName.startsWith(
						`unifi Plus Box ${currentDeviceServiceCount}`
					)
				);
				const upbCommitmentProductMapping = siebelProductMap.find(obj =>
					obj.ProductName.startsWith(
						`Commitment Period - UPB 24 Month Add On ${currentDeviceServiceCount}`
					)
				);
				const upbCourierFeeProductMapping = siebelProductMap.find(obj =>
					obj.ProductName.startsWith(
						`Courier Fee - Add On ${currentDeviceServiceCount}`
					)
				);

				if (
					!upb2ndTierProductMapping ||
					!upb3rdTierProductMapping ||
					!upbCommitmentProductMapping ||
					!upbCourierFeeProductMapping
				) {
					throw new UE_ERROR(
						'Unable to find UPB addon mapping',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0021'
						}
					);
				}

				const orderLineItems2tier: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					PartNumber: upb2ndTierProductMapping.PartNumber,
					ProdPromInstanceId: '',
					Product: upb2ndTierProductMapping.ProductName,
					ProductId: upb2ndTierProductMapping.ProductId,
					ProdPromName: '',
					ProductType: 'Product',
					ServicePointId: '',
					ReservationStatus: 'Reserve Success',
					Qty: '1',
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'New Item',
					ServiceId: '',
					BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
					OrderLineItems: [
						{
							ActionCode: 'Add',
							BillingAccountId: serviceAccount?.BillingAccountId || '',
							PartNumber: upbCourierFeeProductMapping.PartNumber,
							ProdPromInstanceId: '',
							Product: upbCourierFeeProductMapping.ProductName,
							ProductId: upbCourierFeeProductMapping.ProductId,
							ProdPromName: '',
							ProductType: 'Product',
							ServicePointId: '',
							ReservationStatus: 'Reserve Success',
							Qty: '1',
							TMServiceTaxFlag: 'N',
							CfgStateCode: 'New Item',
							ServiceId: '',
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId || ''
						},
						{
							ActionCode: 'Add',
							BillingAccountId: serviceAccount?.BillingAccountId || '',
							PartNumber: upbCommitmentProductMapping.PartNumber,
							ProdPromInstanceId: '',
							Product: upbCommitmentProductMapping.ProductName,
							ProductId: upbCommitmentProductMapping.ProductId,
							ProdPromName: '',
							ProductType: 'Product',
							ServicePointId: '',
							ReservationStatus: 'Reserve Success',
							Qty: '1',
							TMServiceTaxFlag: 'N',
							CfgStateCode: 'New Item',
							ServiceId: '',
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
							ListOfOrderItemXa: {
								OrderItemXa: [
									{
										ActionCode: 'Add',
										Name: 'Number of years',
										TextValue: '2'
									}
								]
							}
						},
						{
							ActionCode: 'Add',
							BillingAccountId: serviceAccount?.BillingAccountId || '',
							PartNumber: upb3rdTierProductMapping.PartNumber,
							ProdPromInstanceId: '',
							Product: upb3rdTierProductMapping.ProductName,
							ProductId: upb3rdTierProductMapping.ProductId,
							ProdPromName: '',
							ProductType: 'Product',
							ServicePointId: '',
							ReservationStatus: 'Reserve Success',
							Qty: '1',
							TMServiceTaxFlag: 'N',
							CfgStateCode: 'New Item',
							ServiceId: '',
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
							ListOfOrderItemXa: {
								OrderItemXa: [
									{
										ActionCode: 'Add',
										Name: 'Number of Months',
										TextValue: '24'
									}
								]
							}
						}
					]
				};

				if (order.ListOfOrderLineItems?.OrderLineItems) {
					for (const oli of order.ListOfOrderLineItems.OrderLineItems) {
						if (
							oli.PartNumber === 'PR007670' ||
							oli.PartNumber === 'PR000200'
						) {
							oli.OrderLineItems = oli.OrderLineItems || [];
							oli.OrderLineItems.push(orderLineItems2tier);
						}
					}
				}
			} else if (req.Category === AddOnsRequestCategoryEnum.UPBRM0) {
				const unifiPlusBoxProductName = 'unifi Plus Box';
				const unifiPlusBoxProductMapping = siebelProductMap.find(
					obj => obj.ProductName === unifiPlusBoxProductName
				);

				const courierFreeProductName = 'Courier Fee - Free of Charge';
				const courierFreeProductMapping = siebelProductMap.find(
					obj => obj.ProductName === courierFreeProductName
				);

				const atvDeviceChargeProductName = 'unifi Plus Box (ATV) Device Charge';
				const atvDeviceChargeProductMapping = siebelProductMap.find(
					obj => obj.ProductName === atvDeviceChargeProductName
				);

				if (
					!unifiPlusBoxProductMapping ||
					!courierFreeProductMapping ||
					!atvDeviceChargeProductMapping
				) {
					throw new UE_ERROR(
						'Unable to find UPBRM0 addon mapping',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0022'
						}
					);
				}

				const orderLineItems: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					ProdPromInstanceId: '',
					Product: `Device/Service ${currentDeviceServiceCount}`,
					ProdPromName: '',
					ProductType: 'Product',
					ServicePointId: '',
					ReservationStatus: 'Reserve Success',
					Qty: '1',
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'New Item',
					ServiceId: '',
					BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
					OrderLineItems: [],
					ListOfOrderItemXa: {
						OrderItemXa: [
							{
								ActionCode: 'Add',
								Name: 'Device Name',
								TextValue: 'Speed Upgrade unifi Plus Box'
							},
							{
								ActionCode: 'Add',
								Name: 'Device Price',
								TextValue: product.ProductPrice
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Period',
								TextValue: product.ContractTerm
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Type',
								TextValue: product.LeasingType
							},
							{
								ActionCode: 'Add',
								Name: 'Monthly RC',
								TextValue: product.MonthlyCommitment
							},
							{
								ActionCode: 'Add',
								Name: 'Partner ID',
								TextValue: product.PartnerId
							},
							{
								ActionCode: 'Add',
								Name: 'Purchase Price',
								TextValue: product.PurchasePrice
							}
						]
					}
				};

				const orderLineItemsWithCommitment: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					ProdPromInstanceId: '',
					Product: `Device/Service ${currentDeviceServiceCount} Commitment`,
					ProdPromName: '',
					ProductType: 'Product',
					ServicePointId: '',
					ReservationStatus: 'Reserve Success',
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'New Item',
					ServiceId: '',
					BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
					OrderLineItems: [],
					ListOfOrderItemXa: {
						OrderItemXa: [
							{
								ActionCode: 'Add',
								Name: 'Contract Period',
								TextValue: product.ContractTerm
							},
							{
								ActionCode: 'Add',
								Name: 'Leasing Type',
								TextValue: product.LeasingType
							}
						]
					}
				};

				const orderLineItemsUnifiTv: OrderLineItemsSchema = {
					Product: unifiPlusBoxProductName,
					ActionCode: 'Add',
					Qty: '1',
					ProductType: 'Product',
					PartNumber: unifiPlusBoxProductMapping?.PartNumber,
					ProductId: unifiPlusBoxProductMapping?.ProductId,
					ReservationStatus: 'Reserve Success',
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'New Item',
					BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					ProdPromInstanceId: '',
					ProdPromName: '',
					ServiceId: '',
					ServicePointId: '',
					OrderLineItems: [
						{
							Product: courierFreeProductName,
							ActionCode: 'Add',
							Qty: '1',
							ProductType: 'Product',
							ReservationStatus: 'Reserve Success',
							ProductId: courierFreeProductMapping?.ProductId,
							PartNumber: courierFreeProductMapping?.PartNumber,
							TMServiceTaxFlag: 'N',
							CfgStateCode: 'New Item',
							BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
							BillingAccountId: serviceAccount?.BillingAccountId || '',
							ProdPromInstanceId: '',
							ProdPromName: '',
							ServiceId: '',
							ServicePointId: ''
						}
					],
					ListOfOrderItemXa: { OrderItemXa: [] }
				};

				const orderLineItemsUnifiTvDeviceCharge: OrderLineItemsSchema = {
					Product: atvDeviceChargeProductName,
					ActionCode: 'Add',
					Qty: '1',
					ProductType: 'Product',
					ReservationStatus: 'Reserve Success',
					TMServiceTaxFlag: 'N',
					CfgStateCode: 'New Item',
					PartNumber: atvDeviceChargeProductMapping?.PartNumber,
					ProductId: atvDeviceChargeProductMapping?.ProductId,
					BillingProfileId: serviceAccount?.PrimaryBillingProfileId || '',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					ProdPromInstanceId: '',
					ProdPromName: '',
					ServiceId: '',
					ServicePointId: '',
					OrderLineItems: [],
					ListOfOrderItemXa: { OrderItemXa: [] }
				};

				for (const oli of order.ListOfOrderLineItems?.OrderLineItems ?? []) {
					// Add orderLineItemsUnifiTv and orderLineItemsCourierFee to the unifi TV OLI
					if (oli.PartNumber === 'PR000200') {
						oli.OrderLineItems?.push(orderLineItemsUnifiTv);
						oli.OrderLineItems?.push(orderLineItemsUnifiTvDeviceCharge);
					}

					// Add orderLineItems and orderLineItemsWithCommitment to the HSI OLI
					if (oli.Product?.includes(this.HIGH_SPEED_INTERNET)) {
						oli.OrderLineItems?.push(orderLineItems);
						oli.OrderLineItems?.push(orderLineItemsWithCommitment);
					}
				}
			} else if (req.Category === AddOnsRequestCategoryEnum.MESH_WIFI_6) {
				if (!activityId) {
					throw new UE_ERROR(
						'Activity ID from SWIFT is required for MESH WIFI 6',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0023'
						}
					);
				}

				if (product.PartNumber === null) {
					throw new UE_ERROR(
						'Part Number is required for MESH WIFI 6',
						StatusCodeEnum.CONFLICT,
						{
							integrationId: this.integrationId,
							response: 'ADDON-0024'
						}
					);
				}

				const orderLineItemsMeshwifi6: OrderLineItemsSchema = {
					ActionCode: 'Add',
					BillingAccountId: serviceAccount?.BillingAccountId || '',
					PartNumber: product.PartNumber,
					Product: product.ProductName,
					ProductType: 'Product',
					TMServiceTaxFlag: 'N',
					ReservationStatus: 'Reserve Success',
					CfgStateCode: 'User Requested Item'
				};

				const hsiOrderLine = order.ListOfOrderLineItems?.OrderLineItems?.filter(
					orderLineItems =>
						orderLineItems.Product?.includes(this.HIGH_SPEED_INTERNET)
				).find(oli => oli !== null);
				if (hsiOrderLine) {
					orderLineItemsMeshwifi6.AssetIntegrationId =
						hsiOrderLine.AssetIntegrationId;
					orderLineItemsMeshwifi6.BillingAccountId =
						hsiOrderLine.BillingAccountId;
					orderLineItemsMeshwifi6.ProdPromName = hsiOrderLine.ProdPromName;
					orderLineItemsMeshwifi6.ServiceAccountId =
						hsiOrderLine.ServiceAccountId;
					orderLineItemsMeshwifi6.Qty = hsiOrderLine.Qty;
					orderLineItemsMeshwifi6.TMServiceTaxFlag =
						hsiOrderLine.TMServiceTaxFlag;
					orderLineItemsMeshwifi6.ServiceId = hsiOrderLine.ServiceId;
					orderLineItemsMeshwifi6.BillingProfileId =
						hsiOrderLine.BillingProfileId;
				}

				if (product.ProductName.includes('MESH Wi-Fi 6 RC')) {
					orderLineItemsMeshwifi6.ListOfOrderItemXa = {
						OrderItemXa: [
							{
								ActionCode: 'Add',
								Name: 'Number of years',
								TextValue: '2'
							}
						]
					};
				}

				const listOfActivity: ListOfActivitySchema = {
					Activity: [
						{
							ActivityId: activityId
						}
					]
				};

				// Process order items
				if (order.ListOfOrderLineItems?.OrderLineItems) {
					for (const orderItem of order.ListOfOrderLineItems.OrderLineItems) {
						orderItem.ListOfActivity = listOfActivity;
						if (orderItem.Product?.includes(this.HIGH_SPEED_INTERNET)) {
							orderItem.OrderLineItems = orderItem.OrderLineItems || [];
							orderItem.OrderLineItems.push(orderLineItemsMeshwifi6);

							if (orderItem.OrderLineItems) {
								for (const subItem of orderItem.OrderLineItems) {
									if (subItem.ServiceId && subItem.ServiceId !== '') {
										subItem.ListOfActivity = listOfActivity;
									}
								}
							}
						}
					}
				}
			}
		}
	}
}

export default AddOnsDeviceHelper;
