import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type { Wso2LightWeightBillingDetailsRes } from '../../../../integration/wso2/user/schemas/api/wso2LightweightBillingDetails.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type { BillingDetailsObj } from '../schemas/api/billingAccount.schema';
import {
	type SelectAccountSettings,
	accountSettingsTableSchema
} from '../schemas/db/accountSettings.schema';

class AccountService {
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async getBillingAccountLabel(
		idValue: string,
		billingAccountNumber: string
	): Promise<string> {
		const result: SelectAccountSettings[] = await this.db
			.select()
			.from(accountSettingsTableSchema)
			.where(
				and(
					eq(accountSettingsTableSchema.IdValue, idValue),
					eq(accountSettingsTableSchema.BillingAccountNo, billingAccountNumber)
				)
			)
			.execute()
			.catch(err => {
				pinoLog.error(err);
				return [];
			});

		const accountLabel: string =
			result.at(0)?.AccountLabel ?? billingAccountNumber;

		return accountLabel;
	}

	async hasAccessToServiceId(
		idType: string,
		idValue: string,
		serviceId: string
	): Promise<boolean> {
		// Build the request for the Customer Account (CA)
		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: idType,
			idValue: idValue
		};

		// Call the Customer Account API (CA)
		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.YES
			);

		// Proceed only if Customer Accounts are returned
		if (wso2CARes?.Response?.CustomerAccounts) {
			for (const ca of wso2CARes.Response.CustomerAccounts) {
				if (ca.BillingAccounts) {
					// Loop through Billing Accounts
					for (const ba of ca.BillingAccounts) {
						if (ba.AccountNumber) {
							// Create Service Account (SA) request
							const wso2SAReq: Wso2ServiceAccountReq = {
								idType: idType,
								idValue: idValue,
								BillingAccountNo: ba.AccountNumber,
								SystemName: ca.SystemName ?? SystemNameEnum.NOVA
							};

							// Call the Service Account API (SA)
							const wso2SARes: Wso2ServiceAccountRes =
								(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
									wso2SAReq,
									LightweightFlagEnum.YES
								)) as Wso2ServiceAccountRes;

							// Check if Service Accounts are returned
							if (wso2SARes?.Response?.ServiceAccount) {
								for (const sa of wso2SARes.Response.ServiceAccount) {
									if (sa?.Products && sa.Products.length > 0) {
										for (const product of sa?.Products ?? []) {
											if (product.SerialNumber === serviceId) {
												return true;
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}

		return false;
	}

	async getServiceAccountByServiceId(
		idType: string,
		idValue: string,
		serviceId: string
	): Promise<Wso2ServiceAccountRes> {
		// Build the request for the Customer Account (CA)
		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: idType,
			idValue: idValue
		};

		// Call the Customer Account API (CA)
		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.YES
			);

		// Proceed only if Customer Accounts are returned
		if (wso2CARes?.Response?.CustomerAccounts) {
			for (const ca of wso2CARes.Response.CustomerAccounts) {
				if (ca.BillingAccounts) {
					// Loop through Billing Accounts
					for (const ba of ca.BillingAccounts) {
						if (ba.AccountNumber) {
							// Create Service Account (SA) request
							const wso2SAReq: Wso2ServiceAccountReq = {
								idType: idType,
								idValue: idValue,
								BillingAccountNo: ba.AccountNumber,
								SystemName: ca.SystemName ?? SystemNameEnum.NOVA
							};

							// Call the Service Account API (SA)
							const wso2SARes: Wso2ServiceAccountRes =
								(await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
									wso2SAReq,
									LightweightFlagEnum.NO
								)) as Wso2ServiceAccountRes;

							// Check if Service Accounts are returned
							if (wso2SARes?.Response?.ServiceAccount) {
								for (const sa of wso2SARes.Response.ServiceAccount) {
									for (const moli of sa.ServiceAccountMoli ?? []) {
										for (const oli of moli.ServiceAccountOli ?? []) {
											if (oli.SerialNumber === serviceId) {
												return wso2SARes;
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}

		return null;
	}

	async getBillingDetails(req: Wso2ServiceAccountReq): Promise<{
		LatestOutstandingAmount: string;
		LatestBillDueDate: string;
		BillingDetails: BillingDetailsObj;
	}> {
		/**
		 * Set retries to 1 to avoid timeout issues.
		 * By default, it's 3 — but since this API is used on the account page, we want faster failure feedback so that it won't impact the user experience.
		 */
		const wso2LightweightBillingDetailsRes: Wso2LightWeightBillingDetailsRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2LightWeightBillingDetails(
				req,
				false,
				1
			);

		const { latestBillDueDate, latestOutstandingAmount } =
			this.extractBillingSummary(wso2LightweightBillingDetailsRes);
		const billingDetailsObj: BillingDetailsObj = this.buildBillingDetailsObj(
			wso2LightweightBillingDetailsRes
		);

		const res = {
			LatestOutstandingAmount: latestOutstandingAmount,
			LatestBillDueDate: latestBillDueDate,
			BillingDetails: billingDetailsObj
		};

		return res;
	}

	private buildBillingDetailsObj(
		wso2LightweightBillingDetailsRes: Wso2LightWeightBillingDetailsRes
	): BillingDetailsObj {
		if (!wso2LightweightBillingDetailsRes) {
			return {
				IsErrorFromWso2: true,
				Response: {
					BillingHistory: null,
					CreditUtilization: null,
					PaymentHistory: null
				}
			};
		}

		const billingHistory =
			wso2LightweightBillingDetailsRes?.Response?.BillingHistory?.map(
				billingHistory => ({
					InvoiceId: billingHistory?.InvoiceId,
					AccountNo: billingHistory?.AccountNo,
					BillNo: billingHistory?.BillNo,
					BillDate: billingHistory?.BillDate,
					BillDueDate: billingHistory?.BillDueDate,
					TotalCurrent: billingHistory?.TotalCurrent,
					TotalTaxGST: billingHistory?.TotalTaxGST,
					TotalDue: billingHistory?.TotalDue,
					TotalOutstanding: billingHistory?.TotalOutstanding,
					TotalPreviousPayment: billingHistory?.TotalPreviousPayment,
					TotalPrevious: billingHistory?.TotalPrevious,
					TotalPrevAdj: billingHistory?.TotalPrevAdj,
					TotalRebate: billingHistory?.TotalRebate,
					URL: billingHistory?.URL
						? billingHistory.URL.replaceAll(
								envConfig().PRIVATE_PDF_DOMAIN,
								envConfig().PUBLIC_PDF_DOMAIN
							)
						: null
				})
			) ?? null;

		return {
			IsErrorFromWso2: false,
			Response: {
				BillingHistory: billingHistory,
				CreditUtilization:
					wso2LightweightBillingDetailsRes.Response?.CreditUtilization,
				PaymentHistory:
					wso2LightweightBillingDetailsRes.Response?.PaymentHistory
			}
		};
	}

	extractBillingSummary(
		wso2LightweightBillingDetailsRes: Wso2LightWeightBillingDetailsRes
	) {
		const latestBillDueDate: string =
			wso2LightweightBillingDetailsRes?.Response?.BillingHistory?.at(0)
				?.BillDueDate ?? 'N/A';
		const latestOutstandingAmount: string = wso2LightweightBillingDetailsRes
			?.Response?.LatestDueUnbilledUsage?.OutstandingAmount
			? Number.parseFloat(
					wso2LightweightBillingDetailsRes?.Response.LatestDueUnbilledUsage
						?.OutstandingAmount
				).toFixed(2)
			: 'N/A';

		return { latestBillDueDate, latestOutstandingAmount };
	}
}

export default AccountService;
