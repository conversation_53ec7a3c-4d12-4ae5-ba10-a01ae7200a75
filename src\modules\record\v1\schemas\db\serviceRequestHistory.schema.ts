import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

const contactDetailsSchema = t.Object({
	ContactID: t.Optional(t.String()),
	ContactDetailRowID: t.Optional(t.String()),
	ContactDetailReportedID: t.Optional(t.String())
});

type ContactDetails = Static<typeof contactDetailsSchema>;

const changePreferenceSchema = t.Object({
	CommFlag: t.MaybeEmpty(t.String()),
	CellularPhone: t.Maybe<PERSON>mpty(t.String()),
	EmailAddress: t.<PERSON>(t.String()),
	CommMethod: t.<PERSON>(t.String()),
	ContactName: t.Maybe<PERSON>mpty(t.String()),
	HomePhone: t.MaybeEmpty(t.String()),
	WorkPhone: t.MaybeEmpty(t.String())
});

type ChangePreference = Static<typeof changePreferenceSchema>;

const closureSchema = t.Object({
	ClosureCategory: t.<PERSON>(t.String()),
	ClosureReason: t.<PERSON>(t.String()),
	ClosureRemarks: t.<PERSON>(t.String())
});

type Closure = Static<typeof closureSchema>;

export const serviceRequestHistoryTableSchema = pgTable(
	'service_request_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		SystemName: text('system_name'),
		Type: text('type'),
		InterfaceStatus: text('interface_status'),
		OwnerGroup: text('owner_group'),
		SRNumber: text('sr_number'),
		Case: text('case'),
		Category: text('category'),
		SubCategory: text('sub_category'),
		CustomerAccountNo: text('customer_account_no'),
		BillingAccountNo: text('billing_account_no'),
		ServiceId: text('service_id'),
		ServiceRowId: text('service_row_id'),
		CustomerComments: text('customer_comments'),
		ContactDetails: json('contact_details').$type<ContactDetails>(),
		PreferredAcknowledgment: text('preferred_acknowledgment'),
		ChangePreference: json('change_preference').$type<ChangePreference>(),
		Product: text('product'),
		ProductCategory: text('product_category'),
		ProductType: text('product_type'),
		Closure: json('closure').$type<Closure>(),
		Priority: text('priority'),
		Status: text('status'),
		Source: text('source'),
		CreatedSource: text('created_source'),
		MarketingConsent: text('marketing_consent', { enum: ['Y', 'N', ''] }),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
	}
);

export type SelectServiceRequestHistory =
	typeof serviceRequestHistoryTableSchema.$inferSelect;

export type InsertServiceRequestHistory =
	typeof serviceRequestHistoryTableSchema.$inferInsert;
