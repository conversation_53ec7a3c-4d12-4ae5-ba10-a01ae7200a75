import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { EmailEnum } from '../../../../enum/notification.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { NetcoreAddActivityReq } from '../../../../integration/netcore/schemas/api/netcoreAddActivity.schema';
import type { NetcoreBaseResponse } from '../../../../integration/netcore/schemas/api/netcoreBaseResponse.schema';
import type { NetcoreSendEmailReq } from '../../../../integration/netcore/schemas/api/netcoreSendEmail.schema';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate } from '../../../../shared/common';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import {
	customerOrderTableSchema,
	orderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/orderable.schema';
import type { TemporalNotificationRequest } from '../schemas/api/notification.schema';
import {
	type SelectMailNotificationSchema,
	mailNotificationTableSchema
} from '../schemas/db/mailNotification.schema';

interface OrderDetails {
	customerId: string;
	orderId: string;
	email: string;
	orderStatus: string;
	orderProgress: Array<{ Status: string }>;
}

interface NotificationConfig {
	titleText: string;
	bodyText: string;
}

type NotificationMedium = 'email' | 'activity';

class NotificationService {
	private readonly db: NodePgDatabase;
	private readonly mwIntegration: MwIntegration;
	private readonly integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.mwIntegration = new MwIntegration(integrationId);
		this.integrationId = integrationId;
	}

	async dispatchNotification(
		medium: NotificationMedium,
		notificationRequest: TemporalNotificationRequest
	): Promise<BaseResponse> {
		try {
			this.validateNotificationRequest(medium, notificationRequest);

			const orderDetails: OrderDetails = await this.getOrderDetails(
				notificationRequest.OrderId
			);

			const notificationConfig: NotificationConfig =
				await this.getNotificationConfig(
					orderDetails.orderStatus,
					orderDetails.orderProgress
				);

			await this.sendNotification(
				medium,
				orderDetails,
				notificationConfig,
				notificationRequest.OrderId
			);

			return this.createSuccessResponse();
		} catch (error) {
			if (error instanceof UE_ERROR) {
				throw error;
			}

			throw new UE_ERROR(
				`Failed to dispatch ${medium} notification: ${error instanceof Error ? error.message : 'Unknown error'}`,
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}
	}

	private validateNotificationRequest(
		medium: NotificationMedium,
		notificationRequest: TemporalNotificationRequest
	): void {
		if (!medium) {
			throw new UE_ERROR(
				'Notification medium is required',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!['email', 'activity'].includes(medium)) {
			throw new UE_ERROR(
				'Invalid notification medium. Must be "email" or "activity"',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!notificationRequest?.OrderId) {
			throw new UE_ERROR(
				'Order ID is required in notification request',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}
	}

	private async getOrderDetails(orderId: string): Promise<OrderDetails> {
		const orderDetailsQuery = await this.db
			.select({
				customerId: customerOrderTableSchema.CustomerId,
				orderId: orderableTxnHistoryTableSchema.OrderId,
				email: customerOrderTableSchema.Email,
				orderStatus: orderableTxnHistoryTableSchema.OrderStatus,
				orderProgress: orderableTxnHistoryTableSchema.OrderProgress
			})
			.from(customerOrderTableSchema)
			.leftJoin(
				orderableTxnHistoryTableSchema,
				eq(
					customerOrderTableSchema.CustomerId,
					orderableTxnHistoryTableSchema.CustomerId
				)
			)
			.where(eq(orderableTxnHistoryTableSchema.OrderId, orderId))
			.execute();

		const orderDetail = orderDetailsQuery[0];

		if (!orderDetail) {
			throw new UE_ERROR(
				`Order not found with ID: ${orderId}`,
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const { orderStatus, orderProgress, email } = orderDetail;

		if (!orderStatus) {
			throw new UE_ERROR(
				'Order status not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderProgress?.length) {
			throw new UE_ERROR(
				'Order progress not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!email?.trim()) {
			throw new UE_ERROR(
				'Customer email not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return orderDetail as OrderDetails;
	}

	private async getNotificationConfig(
		orderStatus: string,
		orderProgress: Array<{ Status: string }>
	): Promise<NotificationConfig> {
		const latestOrderProgress = orderProgress[orderProgress.length - 1];

		if (!latestOrderProgress?.Status) {
			throw new UE_ERROR(
				'Latest order progress status not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const mailNotifications: SelectMailNotificationSchema[] = await this.db
			.select()
			.from(mailNotificationTableSchema)
			.where(
				and(
					eq(
						mailNotificationTableSchema.NextOrderProgress,
						latestOrderProgress.Status
					),
					eq(mailNotificationTableSchema.NextOrderStatus, orderStatus)
				)
			)
			.execute();

		if (!mailNotifications?.length) {
			throw new UE_ERROR(
				`Mail notification configuration not found for status: ${orderStatus}, progress: ${latestOrderProgress.Status}`,
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const notification = mailNotifications[0];

		return {
			titleText: notification.TitleText,
			bodyText: notification.BodyText
		};
	}

	private async sendNotification(
		medium: NotificationMedium,
		orderDetails: OrderDetails,
		notificationConfig: NotificationConfig,
		orderId: string
	): Promise<void> {
		switch (medium) {
			case 'email':
				await this.sendEmailNotification(
					orderDetails.email,
					notificationConfig,
					orderId
				);
				break;
			case 'activity':
				await this.sendActivityNotification(
					orderDetails.email,
					notificationConfig,
					orderId
				);
				break;
		}
	}

	private async sendEmailNotification(
		customerEmail: string,
		notificationConfig: NotificationConfig,
		orderId: string
	): Promise<void> {
		const emailRequest: NetcoreSendEmailReq = {
			from: {
				email: EmailEnum.FROM_NOREPLY
			},
			subject: notificationConfig.titleText,
			content: [
				{
					type: 'text/plain',
					value: notificationConfig.bodyText
				}
			],
			personalizations: [
				{
					to: [{ email: customerEmail }]
				}
			]
		};

		const response: NetcoreBaseResponse =
			await this.mwIntegration.NetcoreIntegration.sendEmail(
				emailRequest,
				orderId,
				'Temporal'
			);

		if (!response.success) {
			throw new UE_ERROR(
				'Failed to send email notification via Netcore',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}
	}

	private async sendActivityNotification(
		customerEmail: string,
		notificationConfig: NotificationConfig,
		orderId: string
	): Promise<void> {
		const activityPayload: NetcoreAddActivityReq = [
			{
				activity_name: 'trigger_email',
				timestamp: getMyTimeZoneDate().toISOString(),
				identity: customerEmail,
				activity_source: 'web' as const,
				activity_params: {
					orderId,
					title: notificationConfig.titleText,
					body: notificationConfig.bodyText
				}
			}
		];

		const response: NetcoreBaseResponse =
			await this.mwIntegration.NetcoreIntegration.addActivity(
				activityPayload,
				orderId,
				'Temporal'
			);

		if (!response.success) {
			throw new UE_ERROR(
				'Failed to add activity notification via Netcore',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}
	}

	private createSuccessResponse(): BaseResponse {
		return {
			Success: true,
			Code: StatusCodeEnum.ACCEPTED,
			IntegrationId: this.integrationId
		};
	}
}

export default NotificationService;
