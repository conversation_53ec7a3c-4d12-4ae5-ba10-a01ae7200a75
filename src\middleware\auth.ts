import { StatusCodeEnum } from '../enum/statusCode.enum';
import { UE_ERROR } from './error';
import { getIdTokenInfo } from './uaid/util/utils';

/**
 * 🔒 Validate Token
 * - Validates the Bearer Token from the request header.
 * - Ensures the request is coming from an authenticated user.
 */
export const validateToken = async (authValue: string): Promise<void> => {
	if (!authValue.startsWith('Bearer')) {
		throw new UE_ERROR(
			'Invalid token! You are not authorized!',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}

	const match = authValue.match(/^Bearer\s+(\S+)$/i);
	if (!match) {
		throw new UE_ERROR(
			'Invalid token! You are not authorized!',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}

	await getIdTokenInfo(match[1]);
};

/**
 * 🔒 Validate API Key
 * - Checks for a valid API Key in the request headers.
 * - Ensures the request is coming from an authorized client.
 */
export const validateApiKey = (xApiKey: string) => {
	const apiKey = process.env.X_API_KEY;
	if (xApiKey !== apiKey) {
		throw new UE_ERROR(
			'Invalid key! You are not authorized!',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}
};

/**
 * 🛠️ Internal Usage
 * - Ensures the request is coming from an internal system or network.
 * - Can be configured to check:
 *   - A special internal-only API key
 *   - Internal IP ranges
 */
export const restrictToInternal = (
	internalKey: string,
	clientIp: string,
	host: string
) => {
	// skip local development
	if (host.includes('127.0.0.1')) {
		return;
	}

	if (
		process.env.INTERNAL_API_KEY !== internalKey ||
		!isInternalRequest(clientIp)
	) {
		throw new UE_ERROR(
			'Forbidden: Internal access only',
			StatusCodeEnum.FORBIDDEN_ERROR
		);
	}
};

const isInternalRequest = (clientIp: string) => {
	const internalIPs = ['**********'];
	return internalIPs.includes(clientIp);
};
