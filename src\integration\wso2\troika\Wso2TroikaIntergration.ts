import { envConfig } from '../../../config/env.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import type { Wso2BodyReq } from '../helper/schemas/api/wso2Base.schema';
import type {
	Wso2CreateTroikaReq,
	Wso2CreateTroikaRes,
	Wso2QueryTroikaReq,
	Wso2QueryTroikaRes,
	Wso2UpdateTroikaReq,
	Wso2UpdateTroikaRes
} from './schemas/api/troika';

class Wso2TroikaIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async createTroikaDemand(
		bodyRequest: Wso2CreateTroikaReq
	): Promise<Wso2CreateTroikaRes> {
		const url: string = envConfig().WSO2_CREATE_TROIKA_DEMAND;
		const token: string = await getApimToken();

		const payload: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, payload);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Create Troika demand throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2CreateTroikaRes;
	}

	async updateTroikaDemand(
		bodyRequest: Wso2UpdateTroikaReq
	): Promise<Wso2UpdateTroikaRes> {
		const url: string = envConfig().WSO2_CREATE_TROIKA_DEMAND;
		const token: string = await getApimToken();

		const payload: Wso2BodyReq = {
			method: 'PUT',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, payload);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Update Troika demand throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2UpdateTroikaRes;
	}

	async queryTroikaDemandStatus(
		bodyRequest: Wso2QueryTroikaReq
	): Promise<Wso2QueryTroikaRes> {
		const url: string = envConfig().WSO2_QUERY_TROIKA_DEMAND;
		const token: string = await getApimToken();

		const payload: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, payload);
		const resBody = await res.json();

		if (!res.ok) {
			throw new UE_ERROR(
				'Query Troika demand throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2QueryTroikaRes;
	}
}

export default Wso2TroikaIntegration;
