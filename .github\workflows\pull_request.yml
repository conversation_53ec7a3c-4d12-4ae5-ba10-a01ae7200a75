name: Universal Engine Pull Request Workflow

env:
  HUSKY: 0

on:
  pull_request:
    types: [opened, synchronize, reopened]

# Linux x64 self-hosted runner
jobs:
  # allow dev branch only to merge to env branch
  merge-to-environment:
    runs-on: [self-hosted, Linux, x64]
    if: ${{ (contains(github.event.pull_request.base.ref, 'sit') || contains(github.event.pull_request.base.ref, 'uat') || contains(github.event.pull_request.base.ref, 'preprod') || contains(github.event.pull_request.base.ref, 'staging')) }}
    steps:
      - run: |
          if [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.DEV_BRANCH_REGEX }} ]]; then
            echo "${{ github.event.pull_request.head.ref }} is allowed to merge to environment branch"
            exit 0
          else
            echo "${{ github.event.pull_request.head.ref }} is not a feature / bugfix / hotfix branch"
            echo 'Only feature / bugfix / hotfix branch can be merged to non-prod environment branch, please follow this convention'
            exit 1
          fi

  # allow dev branch only to merge to release branch
  merge-to-release:
    runs-on: [self-hosted, Linux, x64]
    if: ${{ contains(github.event.pull_request.base.ref, 'release') }}
    steps:
      - run: |
          if [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.DEV_BRANCH_REGEX }} ]]; then
            echo "${{ github.event.pull_request.head.ref }} is allowed to merge to release branch"
            exit 0
          elif [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.REVERT_BRANCH_REGEX }} ]]; then
            echo "${{ github.event.pull_request.head.ref }} is allowed to merge to release branch"
            exit 0
          else
            echo "${{ github.event.pull_request.head.ref }} is not a feature / bugfix / hotfix branch"
            echo 'Only feature / bugfix / hotfix branch can be merged to release branch, please follow this convention'
            exit 1
          fi

  # allow release or revert branch only to merge to master
  merge-to-main:
    runs-on: [self-hosted, Linux, x64]
    if: ${{ github.event.pull_request.base.ref == github.event.pull_request.base.repo.default_branch }}
    steps:
      - run: |
          if [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.RELEASE_BRANCH_REGEX }} ]]; then
            echo "${{ github.event.pull_request.head.ref }} is allowed to merge to main branch"
            exit 0
          else
            echo "${{github.event.pull_request.head.ref}} is not a release or revert branch"
            echo 'Only release or revert branch can be merged to main, please follow this convention'
            exit 1
          fi

# MacOS x64 self-hosted runner
# MacOS runner is maintained to support the CI/CD as a backup solution.
# If the Linux runner is not available, the MacOS runner will be used.
# jobs:
#   # allow dev branch only to merge to env branch
#   merge-to-environment:
#     runs-on: [self-hosted, macOS, ARM64]
#     if: ${{ (contains(github.event.pull_request.base.ref, 'sit') || contains(github.event.pull_request.base.ref, 'uat') || contains(github.event.pull_request.base.ref, 'preprod') || contains(github.event.pull_request.base.ref, 'staging')) }}
#     steps:
#       - run: |
#           if [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.DEV_BRANCH_REGEX }} ]]; then
#             echo "${{ github.event.pull_request.head.ref }} is allowed to merge to environment branch"
#             exit 0
#           else
#             echo "${{ github.event.pull_request.head.ref }} is not a feature / bugfix / hotfix branch"
#             echo 'Only feature / bugfix / hotfix branch can be merged to non-prod environment branch, please follow this convention'
#             exit 1
#           fi

#   # allow dev branch only to merge to release branch
#   merge-to-release:
#     runs-on: [self-hosted, macOS, ARM64]
#     if: ${{ contains(github.event.pull_request.base.ref, 'release') }}
#     steps:
#       - run: |
#           if [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.DEV_BRANCH_REGEX }} ]]; then
#             echo "${{ github.event.pull_request.head.ref }} is allowed to merge to release branch"
#             exit 0
#           elif [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.REVERT_BRANCH_REGEX }} ]]; then
#             echo "${{ github.event.pull_request.head.ref }} is allowed to merge to release branch"
#             exit 0
#           else
#             echo "${{ github.event.pull_request.head.ref }} is not a feature / bugfix / hotfix branch"
#             echo 'Only feature / bugfix / hotfix branch can be merged to release branch, please follow this convention'
#             exit 1
#           fi

#   # allow release or revert branch only to merge to master
#   merge-to-main:
#     runs-on: [self-hosted, macOS, ARM64]
#     if: ${{ github.event.pull_request.base.ref == github.event.pull_request.base.repo.default_branch }}
#     steps:
#       - run: |
#           if [[ "${{ github.event.pull_request.head.ref }}" =~ ${{ vars.RELEASE_BRANCH_REGEX }} ]]; then
#             echo "${{ github.event.pull_request.head.ref }} is allowed to merge to main branch"
#             exit 0
#           else
#             echo "${{github.event.pull_request.head.ref}} is not a release or revert branch"
#             echo 'Only release or revert branch can be merged to main, please follow this convention'
#             exit 1
#           fi
