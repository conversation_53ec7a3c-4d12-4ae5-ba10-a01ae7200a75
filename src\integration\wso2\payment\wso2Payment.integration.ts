import { envConfig } from '../../../config/env.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import type { Wso2BodyReq } from '../helper/schemas/api/wso2Base.schema';
import type {
	Wso2EnableAutopayReq,
	Wso2EnableAutopayRes
} from './schemas/api/wso2AutopayEnable.schema';
import type {
	Wso2CheckPaymentSRStatusReq,
	Wso2CheckPaymentSRStatusRes
} from './schemas/api/wso2PaymentSRStatus.schema';

class Wso2PaymentIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getWso2SettingAutopay(
		bodyRequest: Wso2EnableAutopayReq
	): Promise<Wso2EnableAutopayRes> {
		const url: string = envConfig().WSO2_AUTOPAY_SETTING;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2EnableAutopayRes;

		if (parsedBody.Status.Code === 'DEAI-BUSINESS_ERROR-017') {
			throw new UE_ERROR(
				'AutoPay SR already exist.',
				StatusCodeEnum.NOT_ACCEPTABLE_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!res.ok) {
			throw new UE_ERROR(
				'Autopay Setting throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return parsedBody;
	}

	async getWso2AutopayCheckPaymentSRStatus(
		bodyRequest: Wso2CheckPaymentSRStatusReq
	): Promise<Wso2CheckPaymentSRStatusRes> {
		const url: string = envConfig().WSO2_AUTOPAY_CHECK;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Autopay Check Payment SR Status throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2CheckPaymentSRStatusRes;
	}
}

export default Wso2PaymentIntegration;
