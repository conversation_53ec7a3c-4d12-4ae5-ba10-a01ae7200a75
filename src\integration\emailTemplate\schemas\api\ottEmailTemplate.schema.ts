import { type Static, t } from 'elysia';

export const AppListSchema = t.Object({
	appImgUrl: t.String(),
	userId: t.String(),
	ottUniversalLink: t.String(),
	ottLoginInstruction: t.<PERSON>(t.String()),
	isMobile: t.<PERSON>(),
	isEmail: t.<PERSON>(),
	isUnifiId: t.<PERSON>(),
	price: t.String(),
	startDate: t.String(),
	isSuccess: t.<PERSON>(),
	tvAppName: t.String(),
	isNetflix: t.<PERSON>(),
	isActivate: t.<PERSON>()
});

export const OttNotificationEmailRequestSchema = t.Object({
	customerName: t.String(),
	appList: t.A<PERSON>y(AppListSchema),
	unifiId: t.String()
});

export type AppList = Static<typeof AppListSchema>;
export type OttNotificationEmailRequest = Static<
	typeof OttNotificationEmailRequestSchema
>;

export const disneyCheckChangeMobileEmailTemplateReqSchema = t.Object({
	ottUniversalLink: t.String(),
	customerName: t.String(),
	mobileNo: t.String()
});

export type DisneyCheckChangeMobileEmailTemplateReq = Static<
	typeof disneyCheckChangeMobileEmailTemplateReqSchema
>;
