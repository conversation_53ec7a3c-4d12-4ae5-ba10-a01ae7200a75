import Elysia from 'elysia';
import privateAlertV1Routes from './v1/controllers/alert.controller';
import { protectedConfigV1Routes } from './v1/controllers/config.controller';
import {
	privateLovV1Routes,
	protectedLovV1Routes
} from './v1/controllers/lov.controller';

export const privateUtilV1Routes = new Elysia({ prefix: '/v1/util' })
	.use(privateLovV1Routes)
	.use(privateAlertV1Routes);

export const protectedUtilV1Routes = new Elysia({ prefix: '/v1/util' })
	.use(protectedLovV1Routes)
	.use(protectedConfigV1Routes);
