import { format, parse } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { sql } from 'drizzle-orm';
import { getDbInstance } from '../config/db.config';
import {
	AddOnsPrefixCategoryEnum,
	AddOnsRequestCategoryEnum
} from '../enum/addOns.enum';
import { DownloadSpeedEnum, InternetSpeedEnum } from '../enum/speed.enum';
import { StatusCodeEnum } from '../enum/statusCode.enum';
import { LightweightFlagEnum } from '../enum/wso2.enum';
import { MwIntegration } from '../integration/mw.integration';
import type { Wso2CustomerAccountRes } from '../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type { Wso2NovaBillingProfileReq } from '../integration/wso2/user/schemas/api/wso2NovaBillingProfile.schema';
import type { Wso2ServiceAccountRes } from '../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../middleware/error';
import type { LightweightServiceInfo } from './schemas/api/lightweightServiceInfo.schema';
import type { NovaIcpBillingProfile } from './schemas/api/novaBillingProfile.schema';

export const isValidDate = (dateString: string): boolean => {
	const timestamp = Date.parse(dateString);
	if (Number.isNaN(timestamp)) {
		return false;
	}
	const date = new Date(timestamp);
	return date.toISOString().startsWith(dateString);
};

export const getMyTimeZoneDate = (): Date => {
	return new Date(toZonedTime(new Date(), 'Asia/Kuala_Lumpur'));
};

export const findPlanSpeedByDownloadSpeed = (downloadSpeed: string): string => {
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_5_MBPS))
		return InternetSpeedEnum.SPEED_5_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_10_MBPS_1))
		return InternetSpeedEnum.SPEED_10_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_10_MBPS_2))
		return InternetSpeedEnum.SPEED_10_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_20_MBPS))
		return InternetSpeedEnum.SPEED_20_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_30_MBPS))
		return InternetSpeedEnum.SPEED_30_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_50_MBPS))
		return InternetSpeedEnum.SPEED_50_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_100_MBPS))
		return InternetSpeedEnum.SPEED_100_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_200_MBPS))
		return InternetSpeedEnum.SPEED_200_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_300_MBPS))
		return InternetSpeedEnum.SPEED_300_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_500_MBPS))
		return InternetSpeedEnum.SPEED_500_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_800_MBPS))
		return InternetSpeedEnum.SPEED_800_MBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_1_GBPS))
		return InternetSpeedEnum.SPEED_1_GBPS;
	if (downloadSpeed.includes(DownloadSpeedEnum.SPEED_2_GBPS))
		return InternetSpeedEnum.SPEED_2_GBPS;

	return downloadSpeed;
};

const extractSpeed = (input: string): string | null => {
	// Updated regex to match speeds like 300M, 300Mbps, 1G, or 1Gbps
	const speedRegex = /\b\d+(M|G|Mbps|Gbps)\b/gi;
	const matches = input.match(speedRegex);

	if (!matches || matches.length === 0) {
		return null;
	}

	// Normalize speeds (convert M to Mbps and G to Gbps for consistency)
	const normalizedSpeeds = matches.map(
		speed =>
			speed
				.replace(/(?<![a-zA-Z])M\b/i, 'Mbps') // Convert standalone 'M' to 'Mbps'
				.replace(/(?<![a-zA-Z])G\b/i, 'Gbps') // Convert standalone 'G' to 'Gbps'
	);

	if (normalizedSpeeds.length === 2) {
		return normalizedSpeeds[1]; // Return the second speed to cater upgrade plan such as 'unifi 30Mbps - Upgrade 100Mbps'
	}

	return normalizedSpeeds[0];
};

export const findPlanSpeedByInternetPlanName = (planName: string): string => {
	const planSpeed = extractSpeed(planName);
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_30_MBPS))
		return InternetSpeedEnum.SPEED_30_MBPS;
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_50_MBPS))
		return InternetSpeedEnum.SPEED_50_MBPS;
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_100_MBPS))
		return InternetSpeedEnum.SPEED_100_MBPS;
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_300_MBPS))
		return InternetSpeedEnum.SPEED_300_MBPS;
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_500_MBPS))
		return InternetSpeedEnum.SPEED_500_MBPS;
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_800_MBPS))
		return InternetSpeedEnum.SPEED_800_MBPS;
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_1_GBPS))
		return InternetSpeedEnum.SPEED_1_GBPS;
	if (planSpeed?.includes(InternetSpeedEnum.SPEED_2_GBPS))
		return InternetSpeedEnum.SPEED_2_GBPS;

	return InternetSpeedEnum.SPEED_OTHERS;
};

export const getUltimatePackSiebelTvPackName = (
	internetPlanName: string,
	siebelTvPackName: string
): string => {
	if (internetPlanName.includes('UP49')) {
		return 'New Ultimate Pack 49';
	}
	if (internetPlanName.includes('UP59')) {
		return 'New Ultimate Pack 59';
	}
	if (internetPlanName.includes('UP60')) {
		return 'New Ultimate Pack 60';
	}

	return siebelTvPackName;
};

/** BRN Formats and Types
 * String is for cloud connect
 * Integer is for eCommerce
 */
export const BRNPatterns = [
	{
		pattern: /^00\d{7}-[A-Za-z]$/i,
		format: 'BRN ROB 1',
		type: 'Business',
		integerFormat: 1,
		integerType: 8
	},
	{
		pattern: /^[A-Za-z]{2}\d{7}-[A-Za-z]$/i,
		format: 'BRN ROB 2',
		type: 'Business',
		integerFormat: 2,
		integerType: 9
	},
	{
		pattern: /^(\d{6}|\d{7})-[A-Za-z]$/i,
		format: 'BRN ROC',
		type: 'Company',
		integerFormat: 3,
		integerType: 10
	},
	{
		pattern: /^LLP\d{8}-[A-Za-z]{3}$/i,
		format: 'BRN LLP',
		type: 'LLP',
		integerFormat: 4,
		integerType: 1
	}
];

export const paginateArray = <T>(
	array: T[],
	page: number,
	limit: number
): T[] => {
	if (page < 1 || limit < 1) {
		throw new UE_ERROR(
			'Page and limit must be positive integers.',
			StatusCodeEnum.BAD_REQUEST_ERROR
		);
	}

	const offset = (page - 1) * limit;

	if (offset >= array.length) {
		return [];
	}

	return array.slice(offset, offset + limit);
};

/**
 * This function is to standardize all billing details to be received from ONLY one attribute in WSO2's response.
 * @param integrationId - Intgration ID to attach the WSO2 call
 * @param billingAccount - Billing Account
 */
export const novaBillingProfile = async (
	integrationId: string,
	billingAccountNo: string
): Promise<NovaIcpBillingProfile> => {
	const wso2NovaBillingProfileReq: Wso2NovaBillingProfileReq = {
		RetrieveBillingProfileRequest: {
			BillingAccountNo: billingAccountNo
		}
	};
	const wso2NovaBillingProfileRes = await new MwIntegration(
		integrationId
	).Wso2UserIntegration.getWso2NovaBillingProfile(
		wso2NovaBillingProfileReq,
		false
	);

	const res = {
		AccountName:
			wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
				.BillingProfileDetails.BillingAccounts.BillingName ?? 'N/A',
		AccountEmail:
			wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
				.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
				.InvoiceProfile.EmailBillTo ?? 'N/A',
		AccountContactNo:
			wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
				.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
				.InvoiceProfile.MobileNumber ?? 'N/A',
		AccountAddress: {
			AddressId:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.AddressId ?? 'N/A',
			AddressType:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.AddressType ?? 'N/A',
			UnitLot:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.ApartmentNumber ?? 'N/A',
			City:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.City ?? 'N/A',
			StreetName:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.StreetAddress ?? 'N/A',
			Section:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.StreetAddress2 ?? 'N/A',
			State:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.State ?? 'N/A',
			BuildingName:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.BuildingName ?? 'N/A',
			FloorNo:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.FloorNo ?? 'N/A',
			Postcode:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.PostalCode ?? 'N/A',
			StreetType:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.StreetType ?? 'N/A',
			Country:
				wso2NovaBillingProfileRes?.RetrieveBillingProfileResponse
					.BillingProfileDetails.BillingAccounts.InvoiceProfileDetails
					.InvoiceProfile.Country ?? 'N/A'
		}
	};

	return res;
};

export const icpBillingProfile = async (
	integrationId: string,
	idType: string,
	idValue: string,
	billingAccountNo: string,
	wso2CustomerAccountsRes?: Wso2CustomerAccountRes
): Promise<NovaIcpBillingProfile> => {
	const wso2CARes: Wso2CustomerAccountRes = wso2CustomerAccountsRes
		? wso2CustomerAccountsRes
		: await new MwIntegration(
				integrationId
			).Wso2UserIntegration.getWso2CustomerAccount(
				{
					idType,
					idValue
				},
				LightweightFlagEnum.NO
			);

	const ca = wso2CARes.Response?.CustomerAccounts?.find(ca =>
		ca.BillingAccounts?.find(ba => ba.AccountNumber === billingAccountNo)
	);
	const ba = ca?.BillingAccounts?.find(
		ba => ba.AccountNumber === billingAccountNo
	);

	return {
		AccountName: ba?.BillingName ?? 'N/A',
		AccountEmail:
			ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.EmailBillTo ?? 'N/A',
		AccountContactNo:
			ca?.PreferredCustomerContactDetails?.TMContactCellPhone ?? 'N/A',
		AccountAddress: {
			AddressId: ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.AddressId,
			AddressType:
				ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.TMAddressType,
			UnitLot: ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.ApartmentNumber,
			FloorNo: ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.TMFloorNo,
			BuildingName:
				ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.TMBuildingName,
			StreetType:
				ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.TMStreetType ?? 'N/A',
			StreetName:
				ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.StreetName ?? 'N/A',
			Section:
				ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.Section ?? 'N/A',
			City: ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.City ?? 'N/A',
			Postcode:
				ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.PostalCode ?? 'N/A',
			State: ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.State ?? 'N/A',
			Country: ba?.ListOfTmComInvoiceProfileIntegration?.at(0)?.Country ?? 'N/A'
		}
	};
};

export const formatDate = (
	dateString: string | null | undefined,
	inputFormat: string,
	outputFormat: string
): string => {
	return dateString
		? format(parse(dateString, inputFormat, new Date()), outputFormat)
		: '-';
};

export const getAddOnsOrderId = async (prefix: string): Promise<string> => {
	const db = getDbInstance();
	const result = await db.execute(sql`SELECT nextval('addons_order_id_seq')`);
	const nextValue = result.rows[0]?.nextval; // Extract the sequence value
	const paddedSequence = String(nextValue).padStart(5, '0'); // Ensure 5 digits
	const now = getMyTimeZoneDate();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, '0'); // Ensure 2 digits
	const day = String(now.getDate()).padStart(2, '0'); // Ensure 2 digits
	return `${prefix}-${year}${month}${day}${paddedSequence}`;
};

export const getEnumKeyByValue = <T extends NonNullable<unknown>>(
	enumObj: T,
	value: T[keyof T]
): keyof T => {
	return Object.keys(enumObj).find(
		key => enumObj[key as keyof T] === value
	) as keyof T;
};

export const getPrefix = (category: AddOnsRequestCategoryEnum): string => {
	const prefixMap: Record<AddOnsRequestCategoryEnum, string> = {
		[AddOnsRequestCategoryEnum.SMART_DEVICE]:
			AddOnsPrefixCategoryEnum.SMART_DEVICE_PREFIX,
		[AddOnsRequestCategoryEnum.SME_SMART_DEVICE]:
			AddOnsPrefixCategoryEnum.BIZ_PREFIX,
		[AddOnsRequestCategoryEnum.SMART_HOME]:
			AddOnsPrefixCategoryEnum.SMART_HOME_PREFIX,
		[AddOnsRequestCategoryEnum.BLACKNUT]:
			AddOnsPrefixCategoryEnum.CLOUD_GAMING_PREFIX,
		[AddOnsRequestCategoryEnum.TV_PACK]: AddOnsPrefixCategoryEnum.TV_PREFIX,
		[AddOnsRequestCategoryEnum.OTT]: AddOnsPrefixCategoryEnum.OTT_PREFIX,
		[AddOnsRequestCategoryEnum.MESH_WIFI]:
			AddOnsPrefixCategoryEnum.MESH_WIFI_PREFIX,
		[AddOnsRequestCategoryEnum.MESH_WIFI_6]:
			AddOnsPrefixCategoryEnum.MESH_WIFI_6_PREFIX,
		[AddOnsRequestCategoryEnum.MESH_WIFI_7]:
			AddOnsPrefixCategoryEnum.MESH_WIFI_7_PREFIX,
		[AddOnsRequestCategoryEnum.UPB]: AddOnsPrefixCategoryEnum.UPB_PREFIX,
		[AddOnsRequestCategoryEnum.UPBRM0]: AddOnsPrefixCategoryEnum.UPB_PREFIX
	};

	return prefixMap[category];
};

/** This function is introduced to cater Ultimate Plus and Ultimate Max */
export const getPlanSpeedByInternetSpeed = (internetSpeed: string): string => {
	if (
		![
			'2Gbps',
			'100Mbps',
			'300Mbps',
			'500Mbps',
			'800Mbps',
			'1Gbps',
			'30Mbps'
		].includes(internetSpeed)
	) {
		return 'Others';
	}
	return internetSpeed;
};

export const getLightweightServiceInfo = (
	wso2ServiceAccounts: Wso2ServiceAccountRes
): LightweightServiceInfo => {
	let accountId = '';
	let planName = '';
	let planSpeed = '';
	let tvPackName = '';
	let netflixPlanName = '';

	for (const sa of wso2ServiceAccounts?.Response?.ServiceAccount ?? []) {
		if (sa.Status !== 'Active') continue;
		planName = sa.ProductName ?? '';
		for (const moli of sa.ServiceAccountMoli ?? []) {
			for (const oli of moli.ServiceAccountOli ?? []) {
				if (oli.Type === 'Speed') {
					accountId = oli.SerialNumber ?? '';
					const downloadSpeedKbps = oli[
						'ListOfTmAssetMgmt-AssetXaIntegration'
					]?.['TmAssetMgmt-AssetXaIntegration']?.find(
						asset => asset.Name === 'Download Speed'
					);
					planSpeed = findPlanSpeedByDownloadSpeed(
						downloadSpeedKbps?.Value ?? ''
					);
				}
				if (oli.Type === 'HyppTV Package') {
					tvPackName = getUltimatePackSiebelTvPackName(
						sa.ProductName ?? '',
						oli.ProductName ?? ''
					);
				}
				if (
					oli.Type === 'OTT' &&
					oli.ProductName?.toLowerCase().includes('netflix') &&
					oli.Status === 'Active'
				) {
					netflixPlanName = oli.ProductName ?? null;
				}
			}
		}
	}

	return { accountId, planName, planSpeed, tvPackName, netflixPlanName };
};
