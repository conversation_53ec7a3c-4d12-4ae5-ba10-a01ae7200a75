import fetchRetry from 'fetch-retry';
import { pinoLog } from '../../config/pinoLog.config';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import { getTaasApimToken } from '../taas/helper/taasApimToken.helper';
import { getWerasToken } from '../weras/helper/werasToken.helper';
import { getApimToken } from '../wso2/helper/apimToken.helper';

const fetchWithNative = fetchRetry(global.fetch as typeof fetch);
type FlexibleRequestInit = Omit<RequestInit, 'body'> & {
	body?: BodyInit | FormData | null;
};

const generateCurlCommand = (url: string, req: FlexibleRequestInit) => {
	const method = req.method || 'GET';
	const headers = req.headers
		? Object.entries(req.headers as Record<string, string>)
				.map(([k, v]) => `-H "${k}: ${v}"`)
				.join(' ')
		: '';
	const body =
		req.body && typeof req.body === 'string'
			? `--data '${req.body.replace(/'/g, "'\\''")}'`
			: '';
	return `curl -X ${method} ${headers} ${body} "${url}"`;
};

const updateAuthHeader = (req: FlexibleRequestInit, token: string) => {
	if (req.headers && 'Authorization' in req.headers) {
		req.headers.Authorization = `Bearer ${token}`;
	}
};

export const fetchApi = async (
	integrationId: string,
	url: string,
	req: FlexibleRequestInit,
	options: { isEnabledErrorException?: boolean; retries?: number } = {}
): Promise<Response> => {
	const { isEnabledErrorException = true, retries = 3 } = options;
	const curlCommand = generateCurlCommand(url, req);

	const res = await fetchWithNative(url, {
		...req,
		retryDelay: 1000,
		retryOn: async (attempt, error, response) => {
			if (
				(error || (response && response.status >= 400)) &&
				attempt < retries
			) {
				const errorRes = await response?.clone().text();
				pinoLog.error({
					integrationId,
					attempt,
					url,
					status: response?.statusText,
					code: response?.status,
					curl: curlCommand,
					error: JSON.stringify(errorRes)
				});

				const host = response?.headers.get('X-Forwarded-Host') || '';
				if (
					response?.status === 401 &&
					errorRes?.toLowerCase().includes('invalid credentials')
				) {
					if (
						/api\.apigate\.tm\.com\.my|apigw\.dev\.tmoip\.tm\.com\.my/.test(
							host
						)
					) {
						updateAuthHeader(req, await getApimToken(false));
					} else if (
						/gateway\.apigate\.tm\.com\.my|gateway\.dev\.tmoip\.tm\.com\.my/.test(
							host
						)
					) {
						updateAuthHeader(req, await getTaasApimToken(false));
					}
				}
				if (
					response?.status === 401 &&
					/rewards(stg|sit)?\.unifi\.com\.my/.test(host)
				) {
					updateAuthHeader(req, await getWerasToken(false));
				}
				return true;
			}
			return false;
		}
	});

	const resBody = await res.clone().text();
	try {
		pinoLog.info({
			integrationId,
			url,
			status: res.statusText,
			code: res.status,
			req: JSON.stringify(req),
			res: resBody,
			curl: curlCommand
		});
		return res;
	} catch (error) {
		if (isEnabledErrorException) {
			throw new UE_ERROR(String(error), StatusCodeEnum.UNPROCESSABLE_ENTITY, {
				integrationId
			});
		}
		return res;
	}
};
