import { and, eq, ilike, or } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { pinoLog } from '../../../../config/pinoLog.config';
import {
	OttMerchantIdEnum,
	SubscribedAddOnsTagsEnum,
	TvPackPlanTypeEnum
} from '../../../../enum/addOns.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	OmgGetOttSubscriptionReq,
	OmgGetOttSubscriptionRes,
	OmgOttSubscribedRes,
	OmgPlanSubscribedRes
} from '../../../../integration/omg/schemas/api/omgOttSubscription.schema';
import { getPlanSpeedByInternetSpeed } from '../../../../shared/common';
import {
	type SelectAddonsCatalogue,
	addonsCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/addOnsCatalogue.schema';
import {
	type SelectAddonsMetadata,
	addonsMetadataTableSchema
} from '../../../catalogue/v1/schemas/db/addOnsMetadata.schema';
import {
	type SelectOttCatalogueView,
	ottCatalogueViewSchema
} from '../../../catalogue/v1/schemas/db/ottCatalogueView.schema';
import { tvOttCatalogueViewSchema } from '../../../catalogue/v1/schemas/db/tvOttCatalogueView.schema';
import type {
	SubscribedAddonsObj,
	SubscribedOttList,
	SubscribedOttListRes
} from '../schemas/api/billingAccount.schema';

class SubscribedAddOns {
	private integrationId: string;
	private db: NodePgDatabase;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async generateDeviceAddonsResObj(
		name: string,
		quantity: string,
		addOnDate: string
	): Promise<SubscribedAddonsObj> {
		const [addonsCatalogue]: SelectAddonsCatalogue[] = await this.db
			.select()
			.from(addonsCatalogueTableSchema)
			.where(ilike(addonsCatalogueTableSchema.Name, name))
			.execute()
			.catch((err: Error) => {
				pinoLog.error(err);
				return [];
			});

		if (addonsCatalogue) {
			const addOnsMetadata: SelectAddonsMetadata[] = await this.db
				.select()
				.from(addonsMetadataTableSchema)
				.where(eq(addonsMetadataTableSchema.Category, addonsCatalogue.Category))
				.execute()
				.catch((err: Error) => {
					pinoLog.error(err);
					return [];
				});

			return {
				Name: addonsCatalogue.Name ?? name,
				DisplayName: addonsCatalogue.DisplayName ?? name,
				Quantity: quantity,
				MonthlyCharge: `${addonsCatalogue.MonthlyCommitment}`,
				ProductDetails: addonsCatalogue.Description ?? [],
				Category: addonsCatalogue.Category,
				Tag: addonsCatalogue.Tag ?? SubscribedAddOnsTagsEnum.DEFAULT,
				AddonDate: addOnDate,
				ContractTerm: `${addonsCatalogue.ContractTerm}`,
				Summary: addonsCatalogue.Summary,
				Image: addonsCatalogue.ImageUrl,
				WarrantyPolicy: addOnsMetadata[0]?.WarrantyPolicyUrl ?? null,
				FaqUrl: addOnsMetadata[0]?.FaqUrl ?? null,
				TncUrl: addOnsMetadata[0]?.TncUrl ?? null,
				Specification: addonsCatalogue.Specification,
				UserGuideUrl: addOnsMetadata[0]?.UserGuideUrl ?? null
			};
		}
		return {
			Name: name,
			DisplayName: name,
			Quantity: quantity,
			MonthlyCharge: null,
			ProductDetails: [],
			Category: null,
			Tag: SubscribedAddOnsTagsEnum.DEFAULT,
			AddonDate: addOnDate,
			ContractTerm: null,
			Summary: null,
			Image: null,
			WarrantyPolicy: null,
			FaqUrl: null,
			TncUrl: null,
			Specification: null,
			UserGuideUrl: null
		};
	}

	async getOttListByTvPack(
		accountId: string,
		siebelTvPackName: string | undefined,
		internetSpeed: string,
		netflixProductName: string | null,
		enableErrorException = false
	): Promise<SubscribedOttListRes> {
		const omgReq: OmgGetOttSubscriptionReq = {
			accountType: 'Broadband',
			accountId: accountId
		};

		const omgRes: OmgGetOttSubscriptionRes = await new MwIntegration(
			this.integrationId
		).OmgIntegration.getOmgGetOttSubscription(omgReq, enableErrorException);

		if (!omgRes) {
			return {
				IsErrorFromOmg: true,
				OttSelectionCustChoice: [],
				OttSelectionFixed: [],
				OttAlaCarte: [],
				OttSelectionNetflix: []
			};
		}

		const omgActivePlanSubscribed: OmgPlanSubscribedRes =
			await this.getActiveOttSubscribed(omgRes);

		const planSpeed = getPlanSpeedByInternetSpeed(internetSpeed);

		const ottList: SubscribedOttListRes = await this.getActivatedOttList(
			omgActivePlanSubscribed
		);

		const nonActivatedOttList =
			siebelTvPackName && siebelTvPackName !== ''
				? await this.getNonActivatedOttList(
						siebelTvPackName,
						planSpeed,
						omgActivePlanSubscribed
					)
				: {
						ottSelectionFixedArr: [],
						ottSelectionNetflixArr: []
					};

		if (nonActivatedOttList.ottSelectionFixedArr.length > 0) {
			ottList.OttSelectionFixed.push(
				...nonActivatedOttList.ottSelectionFixedArr
			);
		}

		if (nonActivatedOttList.ottSelectionNetflixArr.length > 0) {
			ottList.OttSelectionNetflix.push(
				...nonActivatedOttList.ottSelectionNetflixArr
			);
		}

		if (ottList.OttSelectionNetflix.length === 0) {
			const netflixComplementary: SubscribedOttList =
				await this.getNetflixComplementary(internetSpeed, netflixProductName);
			ottList.OttSelectionNetflix.push(...netflixComplementary);
		}

		return ottList;
	}

	private async getActivatedOttList(
		omgPlanSubscribed: OmgPlanSubscribedRes
	): Promise<SubscribedOttListRes> {
		const ottSelectionCustChoiceArr: SubscribedOttList = [];
		const ottSelectionFixedArr: SubscribedOttList = [];
		const ottAlaCarteArr: SubscribedOttList = [];
		const ottSelectionNetflixArr: SubscribedOttList = [];
		for (const planSubscribed of omgPlanSubscribed) {
			const findTvPackByPlanId = await this.db
				.select()
				.from(ottCatalogueViewSchema)
				.where(eq(ottCatalogueViewSchema.PlanId, planSubscribed.ottPlanId))
				.execute()
				.catch((err: Error) => {
					pinoLog.error(err);
					return [];
				});

			for (const ottSub of planSubscribed.ottSubscribed) {
				for (const tvPackObj of findTvPackByPlanId) {
					if (
						this.isOttSubscribedMatchedOttCatalogue(
							ottSub.ottMerchantId,
							tvPackObj.OttMerchantId,
							ottSub.ottName,
							tvPackObj.OttName,
							ottSub.ottOmgId,
							tvPackObj.OttOmgId
						)
					) {
						const generatedOtt = this.generateOttListRes(
							tvPackObj,
							ottSub,
							true
						);

						if (tvPackObj.OttMerchantId === OttMerchantIdEnum.NETFLIX) {
							ottSelectionNetflixArr.push(...generatedOtt);
						} else if (
							planSubscribed.ottPlanId === 'P7' ||
							planSubscribed.ottPlanId === 'P8'
						) {
							ottAlaCarteArr.push(...generatedOtt);
						} else if (
							tvPackObj.SelectionType === 'ott_selection_cust_choice'
						) {
							ottSelectionCustChoiceArr.push(...generatedOtt);
						} else {
							ottSelectionFixedArr.push(...generatedOtt);
						}
					}
				}
			}
		}

		const res: SubscribedOttListRes = {
			IsErrorFromOmg: false,
			OttSelectionCustChoice: ottSelectionCustChoiceArr,
			OttSelectionFixed: ottSelectionFixedArr,
			OttAlaCarte: ottAlaCarteArr,
			OttSelectionNetflix: ottSelectionNetflixArr
		};

		return res;
	}

	private async getNonActivatedOttList(
		siebelTvPackName: string,
		planSpeed: string,
		omgPlanSubscribed: OmgPlanSubscribedRes
	): Promise<{
		ottSelectionFixedArr: SubscribedOttList;
		ottSelectionNetflixArr: SubscribedOttList;
	}> {
		const ottSelectionFixedArr: SubscribedOttList = [];
		const ottSelectionNetflixArr: SubscribedOttList = [];
		const findTvPackBySiebelTvPackName = await this.db
			.select()
			.from(tvOttCatalogueViewSchema)
			.where(
				and(
					eq(tvOttCatalogueViewSchema.SiebelTvPackName, siebelTvPackName),
					eq(tvOttCatalogueViewSchema.AllowActivation, true),
					or(
						eq(tvOttCatalogueViewSchema.PlanSpeed, 'All'),
						eq(tvOttCatalogueViewSchema.PlanSpeed, planSpeed)
					)
				)
			)
			.execute()
			.catch((err: Error) => {
				pinoLog.error(err.message);
				return [];
			});

		const countFixedOtt = findTvPackBySiebelTvPackName.filter(tvPack => {
			return tvPack.SelectionType === 'ott_selection_fixed';
		}).length;

		for (const tvPackObj of findTvPackBySiebelTvPackName) {
			if (tvPackObj.SelectionType === 'ott_selection_fixed') {
				const hasBundlePlanActivated = omgPlanSubscribed.find(
					planSubscribed => {
						return (
							planSubscribed.ottPlanId === tvPackObj.PlanId &&
							countFixedOtt === planSubscribed.ottSubscribed.length
						);
					}
				);

				const isOttActivatedInOmg = omgPlanSubscribed.find(planSubscribed => {
					return planSubscribed.ottSubscribed.find(ottSubscribed => {
						return this.isOttSubscribedMatchedOttCatalogue(
							ottSubscribed.ottMerchantId,
							tvPackObj.OttMerchantId,
							ottSubscribed.ottName,
							tvPackObj.OttName,
							ottSubscribed.ottOmgId,
							tvPackObj.OttOmgId
						);
					});
				});

				if (!isOttActivatedInOmg && !hasBundlePlanActivated) {
					if (tvPackObj.OttMerchantId === OttMerchantIdEnum.NETFLIX) {
						ottSelectionNetflixArr.push(
							...this.generateOttListRes(tvPackObj, undefined, false)
						);
					} else {
						ottSelectionFixedArr.push(
							...this.generateOttListRes(tvPackObj, undefined, false)
						);
					}
				}
			}
		}
		return {
			ottSelectionFixedArr,
			ottSelectionNetflixArr
		};
	}

	private async getNetflixComplementary(
		internetSpeed: string,
		netflixProductName: string | null
	): Promise<SubscribedOttList> {
		const ottSelectionNetflixArr: SubscribedOttList = [];
		if (netflixProductName) {
			const findTvPackBySiebelTvPackName = await this.db
				.select()
				.from(tvOttCatalogueViewSchema)
				.where(
					and(
						eq(tvOttCatalogueViewSchema.SiebelTvPackName, netflixProductName),
						eq(tvOttCatalogueViewSchema.PlanSpeed, internetSpeed)
					)
				)
				.execute()
				.catch((err: Error) => {
					pinoLog.error(err);
					return [];
				});

			for (const tvPackObj of findTvPackBySiebelTvPackName) {
				if (
					tvPackObj.SelectionType === 'ott_selection_fixed' &&
					tvPackObj.OttMerchantId === OttMerchantIdEnum.NETFLIX
				) {
					ottSelectionNetflixArr.push(
						...this.generateOttListRes(tvPackObj, undefined, false)
					);
				}
			}
		}

		return ottSelectionNetflixArr;
	}

	generateOttListRes = (
		ottPlanCatalogueTableSchema: SelectOttCatalogueView,
		ottSubscribed: OmgOttSubscribedRes | undefined,
		isActivated: boolean
	): SubscribedOttList => {
		return [
			{
				OttPlanType: ottPlanCatalogueTableSchema.PlanType,
				OttPlanId: ottPlanCatalogueTableSchema.PlanId,
				OttPlanName: ottPlanCatalogueTableSchema.OttPlanName,
				OttIsActive: ottPlanCatalogueTableSchema?.OttIsActive ?? true,
				OttTxnId: ottSubscribed ? ottSubscribed.ottTxnId : 0,
				OttStartDate: ottSubscribed ? ottSubscribed.ottStartDate : '',
				OttExpiryDate: ottSubscribed ? ottSubscribed.ottExpiryDate : '',
				OttStatus: ottSubscribed ? ottSubscribed.ottStatus : '',
				OttUserId: ottSubscribed ? ottSubscribed.ottUserId : '',
				SwapAvailable: ottSubscribed ? ottSubscribed.swapAvailable : '',
				Swapped: ottSubscribed ? ottSubscribed.swapped : '',
				SwappedOttTxnId: ottSubscribed ? ottSubscribed.swappedOttTxnId : 0,
				SwapMode: ottSubscribed ? ottSubscribed.swapMode : '',
				SwapOttExpiryDate: ottSubscribed ? ottSubscribed.swapOttExpiryDate : '',
				SwapOttStartDate: ottSubscribed ? ottSubscribed.swapOttStartDate : '',
				OttName:
					ottPlanCatalogueTableSchema?.OttName ?? ottSubscribed?.ottName ?? '',
				OttMerchantId:
					ottPlanCatalogueTableSchema?.OttMerchantId ??
					ottSubscribed?.ottMerchantId ??
					0,
				OttProductId: ottPlanCatalogueTableSchema?.OttProductId ?? '',
				OttOmgId:
					ottPlanCatalogueTableSchema?.OttOmgId ?? ottSubscribed?.ottOmgId ?? 0,
				OttUniversalLink:
					ottPlanCatalogueTableSchema?.OttUniversalLink ??
					ottSubscribed?.ottUniversalLink ??
					'',
				OttIconPath:
					ottPlanCatalogueTableSchema?.OttIconPath ??
					ottSubscribed?.ottIconPath ??
					'',
				OttLoginType:
					ottPlanCatalogueTableSchema?.OttLoginType ??
					ottSubscribed?.ottLoginType ??
					'',
				OttLoginInstruction:
					ottPlanCatalogueTableSchema?.OttLoginInstruction ?? '',
				OttVerificationInstruction:
					ottPlanCatalogueTableSchema?.OttVerificationInstruction ?? '',
				OttActivationLink: ottPlanCatalogueTableSchema?.OttActivationLink ?? '',
				OttSequence: ottPlanCatalogueTableSchema?.OttSequence ?? 0,
				OttPrice: ottPlanCatalogueTableSchema?.OttPrice ?? -1,
				OttPlanSwapGroup: ottPlanCatalogueTableSchema.OttSwapGroup,
				OttActivateStatus:
					isActivated &&
					ottSubscribed &&
					ottSubscribed.ottStatus !== 'Processing_Active'
						? 'Watch'
						: 'Activate',
				OttActivateType:
					ottPlanCatalogueTableSchema.PlanType ===
						TvPackPlanTypeEnum.ALA_CARTE ||
					ottPlanCatalogueTableSchema.PlanType ===
						TvPackPlanTypeEnum.SOFT_BUNDLE ||
					this.isSelfActivateMerchant(ottSubscribed?.ottMerchantId) ||
					this.isSelfActivateMerchant(
						ottPlanCatalogueTableSchema?.OttMerchantId
					)
						? 'Self'
						: 'Group',
				ShowChangePlan:
					ottPlanCatalogueTableSchema.PlanType !==
						TvPackPlanTypeEnum.ALA_CARTE &&
					(ottPlanCatalogueTableSchema?.OttMerchantId !==
						OttMerchantIdEnum.NETFLIX ||
						ottSubscribed?.ottMerchantId !== OttMerchantIdEnum.NETFLIX),
				AllowDisneyUpgrade: ottSubscribed?.allowDisneyUpgrade ?? '',
				AllowMaxUpgrade: ottSubscribed?.allowMaxUpgrade ?? '',
				NetflixTxnId: ottSubscribed?.netflixTxnId ?? '',
				AllowNetflixCancel: ottSubscribed?.allowNetflixCancel ?? '',
				OttDescription: ottPlanCatalogueTableSchema?.OttDescription ?? '',
				OttPackageDetails: ottPlanCatalogueTableSchema?.OttPackageDetails ?? [],
				OttPackageDuration:
					ottPlanCatalogueTableSchema?.OttPackageDuration ?? '',
				OttPackageType: ottPlanCatalogueTableSchema?.OttPackageType ?? ''
			}
		];
	};

	private isSelfActivateMerchant = (
		merchantId: number | undefined
	): boolean => {
		return (
			merchantId === OttMerchantIdEnum.HBO ||
			merchantId === OttMerchantIdEnum.NETFLIX ||
			merchantId === OttMerchantIdEnum.CMGO
		);
	};

	private isOttSubscribedMatchedOttCatalogue = (
		ottSubscribedMerchantId: number,
		ottCatalogueMerchantId: number,
		ottSubscribedName: string,
		ottCatalogueName: string,
		ottSubscribedOmgId: number,
		ottCatalogueOmgId: number
	): boolean => {
		return (
			ottSubscribedMerchantId === ottCatalogueMerchantId &&
			ottSubscribedOmgId === ottCatalogueOmgId &&
			(ottSubscribedName
				.trim()
				.toLowerCase()
				.includes(ottCatalogueName.trim().toLowerCase()) ||
				ottCatalogueName
					.trim()
					.toLowerCase()
					.includes(ottSubscribedName.trim().toLowerCase()))
		);
	};

	private isOttSubscribedActive = (ottSubscribedStatus: string): boolean => {
		return (
			ottSubscribedStatus === 'Active' ||
			ottSubscribedStatus === 'Processing_Active' ||
			ottSubscribedStatus === 'Swap_Retry' ||
			ottSubscribedStatus === 'Swapped'
		);
	};

	private getActiveOttSubscribed = async (
		omgRes: OmgGetOttSubscriptionRes
	): Promise<OmgPlanSubscribedRes> => {
		const activeOttList: OmgPlanSubscribedRes = [];
		for (const planSubscribed of omgRes?.planSubscribed ?? []) {
			activeOttList.push({
				ottPlanId: planSubscribed.ottPlanId,
				ottPlanName: planSubscribed.ottPlanName,
				ottSubscribed: planSubscribed.ottSubscribed.filter(ottSubscribed => {
					return this.isOttSubscribedActive(ottSubscribed.ottStatus);
				})
			});
		}

		return activeOttList;
	};
}

export default SubscribedAddOns;
