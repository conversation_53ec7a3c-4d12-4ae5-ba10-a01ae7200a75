import { type Static, t } from 'elysia';

// VoucherCodeList equivalent
const voucherCodeListSchema = t.Object({
	id: t.Optional(t.Integer()),
	item: t.Optional(t.Integer()),
	code: t.Optional(t.String())
});

// Redemption info
const redemptionSchema = t.Object({
	created_at: t.Optional(t.String()),
	id: t.Optional(t.Number()), // redemption_id in XE
	use_date: t.Optional(t.String()),
	expire_date: t.Optional(t.String())
});

// Main reward item
const rewardItemSchema = t.Object({
	id: t.Integer(),
	name: t.String(),
	description: t.Optional(t.String()),
	category: t.Optional(t.Integer()),
	brand: t.Optional(t.Integer()),
	inventory_channel: t.Optional(t.String()),
	redemption_flow: t.Optional(t.String()),
	status: t.Optional(t.String()),
	segment: t.Optional(t.String()),
	additional_description: t.Optional(t.String()),
	rm_value: t.Optional(t.Number()),
	point_value: t.Optional(t.Integer()),
	code_value: t.Optional(t.String()),
	start_date: t.Optional(t.String()),
	end_date: t.Optional(t.String()),
	image: t.Optional(t.String()),
	csvfile: t.Optional(t.String()),
	fast_track: t.Optional(t.Integer()),
	tnc: t.Optional(t.String()),
	download_url: t.Optional(t.String()),
	highlighted: t.Optional(t.Integer()),
	exclude: t.Optional(t.Integer()),
	barcode: t.Optional(t.String()),
	qrcode: t.Optional(t.String()),
	created_by: t.Optional(t.Integer()),
	modified_by: t.Optional(t.Integer()),
	deleted_by: t.Optional(t.Integer()),
	created_at: t.Optional(t.String()),
	updated_at: t.Optional(t.String()),
	deleted_at: t.Optional(t.String()),
	expiry_date: t.Optional(t.String()),
	redemption_created_at: t.Optional(t.String()),
	redemptionId: t.Optional(t.Number()),
	useDate: t.Optional(t.String())
});

// Combines one row of data (item, redemption, voucher_code_list in XE)
const dataDetailsSchema = t.Object({
	item: rewardItemSchema,
	redemption: redemptionSchema,
	voucher_code_lists: voucherCodeListSchema
});

// WerasGetMyRewardsDetails pagination + data
const rewardsDetailsSchema = t.Object({
	current_page: t.Optional(t.String()),
	first_page_url: t.Optional(t.String()),
	from: t.Optional(t.String()),
	last_page: t.Optional(t.String()),
	last_page_url: t.Optional(t.String()),
	next_page_url: t.Optional(t.String()),
	path: t.Optional(t.String()),
	per_page: t.Optional(t.String()),
	prev_page_url: t.Optional(t.String()),
	to: t.Optional(t.String()),
	total: t.Optional(t.String()),
	data: t.Array(dataDetailsSchema)
});

// WerasGetMyRewardsData wrapper
const werasGetMyRewardsDataSchema = t.Object({
	error: t.Optional(t.String()),
	message: t.Optional(t.String()),
	response: rewardsDetailsSchema
});

// Final Integration Schema: werasGetMyRewardsResSchema
export const werasGetMyRewardsResSchema = t.Object({
	status: t.Boolean(),
	code: t.Integer(),
	data: werasGetMyRewardsDataSchema
});

export type WerasGetMyRewardsRes = Static<typeof werasGetMyRewardsResSchema>;
