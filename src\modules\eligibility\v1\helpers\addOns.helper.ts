import { differenceInDays, differenceInMonths, parse } from 'date-fns';
import { and, eq, ne, or } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import {
	AddOnsRequestCategoryEnum,
	OttMerchantIdEnum,
	TvPackPlanTypeEnum
} from '../../../../enum/addOns.enum';
import { OrderTypeEnum, ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../../enum/user.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import { taasServiceDetailsTableSchema } from '../../../../integration/taas/user/schemas/db/taasServiceDetails.schema';
import type {
	Wso2QueryHardSoftBundleReq,
	Wso2QueryHardSoftBundleRes
} from '../../../../integration/wso2/eligibility/schemas/api/wso2QueryHardSoftBundle.schema';
import type {
	Wso2OrderTrackingReq,
	Wso2OrderTrackingRes
} from '../../../../integration/wso2/record/schemas/api/wso2OrderTracking.schema';
import type {
	Wso2ConciseCustInfoReq,
	Wso2ConciseCustInfoRes
} from '../../../../integration/wso2/user/schemas/api/wso2ConciseCustInfo.schema';
import type { Wso2CustomerAccountRes } from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountMoli,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	getMyTimeZoneDate,
	novaBillingProfile
} from '../../../../shared/common';
import type { LightweightServiceInfo } from '../../../../shared/schemas/api/lightweightServiceInfo.schema';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import {
	type SelectAddonsCatalogue,
	addonsCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/addOnsCatalogue.schema';
import {
	type SelectOttCatalogueView,
	ottCatalogueViewSchema
} from '../../../catalogue/v1/schemas/db/ottCatalogueView.schema';
import {
	type SelectTvPackCatalogue,
	tvPackCatalogueTableSchema
} from '../../../catalogue/v1/schemas/db/tvPackCatalogue.schema';
import { orderableTxnHistoryTableSchema } from '../../../order/v1/schemas/db/orderable.schema';
import BackgroundJob from '../../../setting/v1/services/backgroundJob.service';
import SubscribedAddOns from '../../../user/v1/helpers/subscribedAddOns.helper';
import type {
	SubscribedOttList,
	SubscribedOttListRes
} from '../../../user/v1/schemas/api/billingAccount.schema';
import type {
	AddOnsEligibilityReq,
	AddOnsEligibilityRes,
	ReportingParams
} from '../schemas/api/addOns.schema';
import {
	type AddOnsReportingInfoData,
	addOnsReportingTableSchema
} from '../schemas/db/addOnsReporting.schema';
import {
	type SelectErrorMessages,
	errorMessagesTableSchema
} from '../schemas/db/errorMessages.schema';

class AddOnsHelper {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private db: NodePgDatabase;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.db = getDbInstance();
		this.idTokenInfo = idTokenInfo;
	}

	async checkTCOPFlag(
		billingAccountNo: string
	): Promise<AddOnsEligibilityRes | null> {
		const [result] = await this.db
			.select()
			.from(taasServiceDetailsTableSchema)
			.where(
				and(
					eq(taasServiceDetailsTableSchema.IdType, this.idTokenInfo.IdType),
					eq(taasServiceDetailsTableSchema.IdValue, this.idTokenInfo.IdValue),
					eq(taasServiceDetailsTableSchema.BillingAccountNo, billingAccountNo)
				)
			)
			.execute();

		// No TCOP flag found
		if (!result) {
			await new BackgroundJob(
				this.integrationId,
				this.idTokenInfo
			).triggerTaasServiceDetailsUpdate();
			return await this.createErrorResponse('TCOP-IN-PROGRESS');
		}

		// TCOP checking is still in progress or failed
		if (result.IsTcop === null)
			return await this.createErrorResponse(
				result.Status === ProgressStatusEnum.FAILED
					? 'TCOP-FAILED'
					: 'TCOP-IN-PROGRESS'
			);

		if (result.IsTcop === true)
			return await this.createErrorResponse('TCOP-TRUE');

		return null; // Not TCOP
	}

	/**
	 * Checks if the customer has an open order in siebel.
	 */
	async checkOpenOrderInSiebel(
		billingAccountNo: string
	): Promise<AddOnsEligibilityRes | null> {
		const wso2Req: Wso2OrderTrackingReq = {
			OrderTracking: {
				OrderList: {
					CustomerID: this.idTokenInfo.IdValue,
					IdType: this.idTokenInfo.IdType
				}
			}
		};

		const wso2Res: Wso2OrderTrackingRes =
			await this.mwIntegration.Wso2RecordIntegration.getWso2OrderTracking(
				wso2Req
			);

		if (
			wso2Res.Response.OrderList?.['TmOrderEntry-OrdersIntegration-NOVA'] &&
			wso2Res.Response.OrderList?.['TmOrderEntry-OrdersIntegration-NOVA']
				.length > 0
		) {
			const hasOpenOrder: boolean = wso2Res.Response.OrderList?.[
				'TmOrderEntry-OrdersIntegration-NOVA'
			].some(
				novaOrders =>
					novaOrders.BillingAccountNumber === billingAccountNo &&
					novaOrders.Status !== 'Completed' &&
					novaOrders.Status !== 'Cancelled'
			);

			if (hasOpenOrder) {
				return await this.createErrorResponse('OPEN-ORDER');
			}
		}

		return null;
	}

	/**
	 * Checks if the customer has an open order in DB.
	 */
	async checkOpenOrderInDb(
		billingAccountNo: string
	): Promise<AddOnsEligibilityRes | null> {
		const dbRes = await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(
				and(
					eq(orderableTxnHistoryTableSchema.BillingAccountNo, billingAccountNo),
					or(
						eq(
							orderableTxnHistoryTableSchema.OrderStatus,
							ProgressStatusEnum.SUBMITTED
						),
						eq(
							orderableTxnHistoryTableSchema.OrderStatus,
							ProgressStatusEnum.INPROGRESS
						)
					),
					eq(orderableTxnHistoryTableSchema.OrderType, OrderTypeEnum.ADD_ONS),
					ne(
						orderableTxnHistoryTableSchema.OrderCategory,
						AddOnsRequestCategoryEnum.OTT
					)
				)
			)
			.execute();

		if (dbRes.length > 0) {
			return await this.createErrorResponse('OPEN-ORDER');
		}

		return null;
	}

	/**
	 * Fetches service account details.
	 */
	async fetchServiceAccount(
		decryptedBillAccNo: string
	): Promise<Wso2ServiceAccountRes> {
		const wso2SARes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				{
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue,
					BillingAccountNo: decryptedBillAccNo,
					SystemName: SystemNameEnum.NOVA
				},
				LightweightFlagEnum.NO
			);

		if (wso2SARes?.Response?.ServiceAccount?.length === 0) {
			throw new UE_ERROR(
				'You have no access to this service',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{
					integrationId: this.integrationId,
					response: null
				}
			);
		}

		return wso2SARes;
	}

	/**
	 * Retrieves High-Speed Internet Moli.
	 */
	getHighSpeedInternetMoli(
		wso2SARes: Wso2ServiceAccountRes
	): Wso2ServiceAccountMoli | null {
		const hsiMoli =
			wso2SARes?.Response?.ServiceAccount?.flatMap(
				sa => sa.ServiceAccountMoli ?? []
			).find(
				moli =>
					moli.Status === 'Active' &&
					(moli.ProductName?.includes('Residential High Speed Internet') ||
						moli.ProductName?.includes('Business High Speed Internet'))
			) ?? null;

		return hsiMoli;
	}

	async getDeviceDetailsByName(name: string): Promise<SelectAddonsCatalogue> {
		const [result]: SelectAddonsCatalogue[] = await this.db
			.select()
			.from(addonsCatalogueTableSchema)
			.where(eq(addonsCatalogueTableSchema.Name, name))
			.execute();

		return result;
	}

	async getBundleProductsFromDb(): Promise<string[]> {
		const result = await this.db
			.select({
				name: addonsCatalogueTableSchema.Name
			})
			.from(addonsCatalogueTableSchema)
			.where(eq(addonsCatalogueTableSchema.IsBundle, true))
			.execute();

		return result.map(item => item.name);
	}

	async getEligibleOtt(
		req: AddOnsEligibilityReq,
		serviceInfo: LightweightServiceInfo
	): Promise<AddOnsEligibilityRes> {
		const subscribedAddOn = new SubscribedAddOns(this.integrationId);
		const subscribedOtts: SubscribedOttListRes =
			await subscribedAddOn.getOttListByTvPack(
				serviceInfo.accountId,
				serviceInfo.tvPackName,
				serviceInfo.planSpeed,
				serviceInfo.netflixPlanName
			);

		const planId = serviceInfo.tvPackName === '' ? 'P8' : 'P7';
		const alaCartePlan: SelectOttCatalogueView[] = await this.db
			.select()
			.from(ottCatalogueViewSchema)
			.where(eq(ottCatalogueViewSchema.PlanId, planId))
			.execute();

		const subscribedOttList: SubscribedOttList = [
			...subscribedOtts.OttSelectionCustChoice,
			...subscribedOtts.OttSelectionFixed,
			...subscribedOtts.OttAlaCarte
		];

		let filteredList = alaCartePlan.filter(
			ottAlaCarte =>
				!subscribedOttList.some(
					userOtt =>
						(userOtt.OttMerchantId === OttMerchantIdEnum.YUPPTV &&
							userOtt.OttName === ottAlaCarte.OttName) ||
						(userOtt.OttMerchantId !== OttMerchantIdEnum.YUPPTV &&
							userOtt.OttMerchantId === ottAlaCarte.OttMerchantId)
				)
		);

		filteredList = this.removeConflictingOtts(
			filteredList,
			subscribedOttList,
			OttMerchantIdEnum.TVB,
			{
				Premium: ['Premium', 'VOD', 'Channel'],
				VOD: ['Premium', 'VOD'],
				Channel: ['Premium', 'Channel']
			}
		);

		const notEligibleOttList: string[] = [];
		const res: AddOnsEligibilityRes = {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: 0,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null,
				EligibleOtt: {
					OttPlanId: planId,
					Otts: []
				}
			}
		};

		for (const selectedOtt of req.Otts) {
			const hasOtt = filteredList.find(
				ott => ott.OttMerchantId === selectedOtt.OttMerchantId
			);

			if (!hasOtt) {
				notEligibleOttList.push(selectedOtt.OttName);
			} else {
				res.Response.EligibleOtt?.Otts.push({
					OttName: hasOtt.OttName,
					OttPrice: hasOtt.OttPrice,
					OttMerchantId: hasOtt?.OttMerchantId,
					OttProductId: hasOtt?.OttProductId,
					OttOmgId: hasOtt?.OttOmgId,
					OttLoginType: hasOtt?.OttLoginType,
					OttIconPath: hasOtt?.OttIconPath
				});
			}
		}

		res.Response.IsEligible = notEligibleOttList.length !== req.Otts.length;
		res.Response.EligibleQuantity = res.Response.EligibleOtt?.Otts.length ?? 0;

		if (notEligibleOttList.length > 0) {
			const notEligibleOtts = notEligibleOttList.join(', ');
			const errorMessage = await this.getErrorMessage('OTT-SUBSCRIBED');
			res.Response.Title = errorMessage.Title;
			res.Response.Reason = errorMessage.Message.replace(
				'${notEligibleOtts}',
				notEligibleOtts
			);
			res.Response.ReasonCode = errorMessage.Code;
			res.Response.CTAButtonText = errorMessage.CTAButtonText;
			res.Response.CTAUrl = errorMessage.CTAUrl;
		}

		return res;
	}

	async getEligibleSmartDevice(
		req: AddOnsEligibilityReq,
		hsiMoli: Wso2ServiceAccountMoli,
		reportingParam: ReportingParams
	): Promise<AddOnsEligibilityRes> {
		const maxSmartDeviceInContract: number =
			this.idTokenInfo.IdType === IdTypeEnum.BRN ||
			this.idTokenInfo.IdType === IdTypeEnum.NON_BRN
				? 3
				: 2;
		let totalSmartDeviceInContract = 0;
		const upgradePlan = [
			'unifi 30Mbps - Upgrade 100Mbps (Try Me) Ultimate',
			'unifi 30Mbps - Upgrade 100Mbps (Try Me) VAR',
			'unifi 30Mbps - Upgrade 100Mbps (Extra Exclusive Promo) VAR',
			'unifi 30Mbps - Upgrade 100Mbps (Try Me)',
			'unifi 30Mbps - Upgrade 100Mbps (Try Me Premium)',
			'unifi 30Mbps - Upgrade 100Mbps (Exclusive Promo unifi Plus Box)',
			'unifi 30Mbps - Upgrade 100Mbps (Exclusive Promo)',
			'unifi 30Mbps - Upgrade 100Mbps Ultimate Extraordinary',
			'unifi 30Mbps - Upgrade 100Mbps Premium Extraordinary'
		];

		const citizenship = await this.isMalaysianCitizen(reportingParam);
		if (citizenship) {
			return citizenship;
		}

		// Plan compatibility
		const isUpgradePlan = upgradePlan.some(planName =>
			hsiMoli.ProdPromName?.includes(planName)
		);

		if (isUpgradePlan) {
			return await this.createErrorResponse('GENERAL', reportingParam);
		}

		// Service Tenure
		// Served a minimum of 6 months service tenure with Unifi Home subscriptions
		const serviceStartDateDifference = this.getStartDateDifference(
			hsiMoli.StartDate ?? ''
		);

		if (
			serviceStartDateDifference &&
			serviceStartDateDifference.differenceInMonths <= 6
		) {
			return await this.createErrorResponse('TENURE-SMD', reportingParam);
		}

		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Commitment'
			) {
				// 4. Number of eligible smart devices per contract:
				// 		- 1 Smart device while contract is active.
				// 		- Existing customers can purchase a maximum of one (1) smart device under one Unifi account.
				// 		- Existing customer can add another device (1) once they have completed their contract for their current smart device add-on (24/36 months contract)
				if (oli.SmartDeviceFlag === 'Y') {
					const contractPeriod = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
						'TmAssetMgmt-AssetXaIntegration'
					]?.find(asset => asset.Name === 'Contract Period');

					if (oli.StartDate) {
						const contractStartDate = new Date(oli.StartDate);
						const currentDate = getMyTimeZoneDate();

						// Calculate the contract end date
						const contractEndDate = new Date(contractStartDate);
						contractEndDate.setMonth(
							contractEndDate.getMonth() + Number(contractPeriod?.Value ?? 24)
						);

						const contract6Month = new Date(contractStartDate);
						contract6Month.setMonth(contractEndDate.getMonth() + 6);
						if (currentDate <= contract6Month) {
							return await this.createErrorResponse(
								'ACTIVE-SMD',
								reportingParam
							);
						}

						// Check if the current date is before the contract end date
						if (currentDate <= contractEndDate) {
							++totalSmartDeviceInContract;
						}
					}
				}
			}

			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				// Bundle plan
				// Can add up until 2 Smart devices within contract is active (more than 6 months tenure)
				// Existing customer with Bundle Smart Device still within contract, is not allowed to add any new smart device (single or bundle) until contract ends.
				const bundleProducts = await this.getBundleProductsFromDb();
				const totalSmartDeviceBundle =
					oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
						'TmAssetMgmt-AssetXaIntegration'
					]?.filter(device => bundleProducts.includes(device.Value ?? ''))
						.length ?? 0;
				totalSmartDeviceInContract += totalSmartDeviceBundle * 2;
			}

			// 5. Check eligible speed
			// (Broadband speed => 100 Mbps (Residential High-Speed Internet))
			// (Broadband speed => 30 Mbps (Business High-Speed Internet))
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');
				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				const isSpeedInvalid =
					this.idTokenInfo.IdType === IdTypeEnum.BRN ||
					this.idTokenInfo.IdType === IdTypeEnum.NON_BRN
						? speedInNumber < 35840
						: speedInNumber < 112640;

				if (isSpeedInvalid) {
					return await this.createErrorResponse('SPEED-SMD', reportingParam);
				}
			}
		}

		const deviceDetails = await this.getDeviceDetailsByName(req.ProductName);
		if (!deviceDetails) {
			return await this.createErrorResponse(
				'INVALID-PRODUCT-ID',
				reportingParam
			);
		}

		// Existing customer with Bundle Smart Device still within contract,
		// is not allowed to add any new smart device (single or bundle) until contract ends.
		if (
			totalSmartDeviceInContract >= maxSmartDeviceInContract ||
			(deviceDetails.IsBundle && totalSmartDeviceInContract > 0)
		) {
			return await this.createErrorResponse('LIMIT-SMD', reportingParam);
		}
		// 1. Existing customer with one Single smart device within device contract > 6 months is allowed to add another new Single Smart Device But not allowed to add new Bundle Smart Device.
		// 2. Existing customer with one Single smart device within device contract <= 6 months is not allowed to add on any new smart device (single or bundle)
		// 3. Existing customer with Bundle Smart Device still within contract, is not allowed to add any new smart device (single or bundle) until contract ends.
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: maxSmartDeviceInContract - totalSmartDeviceInContract,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null
			}
		};
	}

	async getEligibleSmartHome(
		hsiMoli: Wso2ServiceAccountMoli,
		reportingParam: ReportingParams
	): Promise<AddOnsEligibilityRes> {
		const citizenship = await this.isMalaysianCitizen(reportingParam);
		if (citizenship) {
			return citizenship;
		}

		// Add on up to maximum of five (5) Smart Home Packs/devices per Unifi Home service subscription
		let smartHomeLimit = 5;

		const smartHomeList = await this.db
			.select()
			.from(addonsCatalogueTableSchema)
			.where(
				eq(
					addonsCatalogueTableSchema.Category,
					AddOnsRequestCategoryEnum.SMART_HOME
				)
			);

		// Service Tenure
		// Served a minimum of 6 months service tenure with Unifi Home subscriptions
		const serviceStartDateDifference = this.getStartDateDifference(
			hsiMoli.StartDate ?? ''
		);

		if (
			serviceStartDateDifference &&
			serviceStartDateDifference.differenceInMonths <= 6
		) {
			return await this.createErrorResponse('TENURE-SH', reportingParam);
		}
		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				const leasingPeriod = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Leasing Period');

				const deviceName = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Device Name');

				const isSmartHome = smartHomeList.some(
					sh => sh.Name === deviceName?.Value
				);

				if (oli.StartDate && isSmartHome) {
					--smartHomeLimit;
					const contractStartDate = new Date(oli.StartDate);
					const currentDate = getMyTimeZoneDate();

					// Calculate the contract end date
					const contractEndDate = new Date(contractStartDate);
					contractEndDate.setMonth(
						contractEndDate.getMonth() + Number(leasingPeriod?.Value ?? 24)
					);

					const contract6Month = new Date(contractStartDate);
					contract6Month.setMonth(contractEndDate.getMonth() + 6);

					// Check if the current date is before the contract end date
					if (currentDate <= contractEndDate) {
						return await this.createErrorResponse('ACTIVE-SH', reportingParam);
					}
				}
			}

			// Check eligible speed (30Mbps – 2Gbps)
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');

				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				if (speedInNumber < 35840) {
					return await this.createErrorResponse('SPEED-SH', reportingParam);
				}
			}
		}

		if (smartHomeLimit === 0) {
			return await this.createErrorResponse('LIMIT-SH', reportingParam);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: smartHomeLimit,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null
			}
		};
	}

	async getEligibleMeshWifi(
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		const meshLimit = 5;
		let totalMeshWifi = 0;
		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				totalMeshWifi +=
					oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
						'TmAssetMgmt-AssetXaIntegration'
					]?.reduce(
						(count, asset) =>
							count +
							(asset.Value?.replaceAll('-', '')
								.toUpperCase()
								.includes(AddOnsRequestCategoryEnum.MESH_WIFI)
								? 1
								: 0),
						0
					) ?? 0;
			}

			// Check eligible speed (>= 100Mbps)
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');
				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				if (speedInNumber < 112640) {
					return await this.createErrorResponse('SPEED-MESH');
				}
			}
		}

		const totalRemainingMesh: number = meshLimit - totalMeshWifi;
		if (totalRemainingMesh <= 0) {
			return await this.createErrorResponse('LIMIT-MESH');
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: totalRemainingMesh,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null
			}
		};
	}

	async getEligibleMeshWifiCombo(
		category: AddOnsRequestCategoryEnum,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		const comboBoxLimit = 2;
		let totalMeshWifiCombo = 0;
		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			// Check eligible speed (>= 100Mbps)
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');
				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				/**
				 * Check speed and then determine the product ultra or non ultra
				 * RG 6 - APPLICABLE for NON ULTRA - EXAMPLE: MESH Wi-Fi 6 (Monthly) & MESH Wi-Fi 6 non Ultra (One-Time Charge)
				 * RG 7 - APPLICABLE for ULTRA - EXAMPLE: MESH Wi-Fi 6 Ultra (Monthly) & MESH Wi-Fi 6 Ultra (One-Time Charge)
				 * RG Mode must be combo
				 * if below 1GBPS, then look for RG mode and version (RG 6)
				 * if above and equal 1GBPS, then look RG mode and version (RG 7)
				 */

				if (speedInNumber < 112640) {
					return await this.createErrorResponse('SPEED-MESH');
				}

				if (
					speedInNumber < 1126400 &&
					category === AddOnsRequestCategoryEnum.MESH_WIFI_7
				) {
					return await this.createErrorResponse('SPEED-MESH');
				}
			}

			// Check RG Version and RG Mode
			if (oli.ProductName?.includes('Wi-Fi (RG)') && oli.Type === 'Equipment') {
				if (!oli.TMRGVersion) {
					return await this.createErrorResponse('MESH-RG');
				}
				if (
					(category === AddOnsRequestCategoryEnum.MESH_WIFI_6 &&
						(oli.TMRGVersion !== 'RG6' || oli.TMRGMode !== 'Combo')) ||
					(category === AddOnsRequestCategoryEnum.MESH_WIFI_7 &&
						(oli.TMRGVersion !== 'RG7' || oli.TMRGMode !== 'Combo'))
				) {
					return await this.createErrorResponse('MESH-RG');
				}
			}

			// * info: how to identify ala carte and bundle devices?
			// Starter Pack (Bundle) - Recurring Charge (RC)
			// Premium Pack (Bundle) - Recurring Charge(RC)
			// Ala Carte - One time charge (OTC)
			const productName = oli.ProductName?.replaceAll('-', '').toUpperCase();
			if (
				productName?.includes(AddOnsRequestCategoryEnum.MESH_WIFI) &&
				(productName?.includes('RC') || productName.includes('OTC'))
			) {
				++totalMeshWifiCombo;
			}
		}

		const eligibleQuantity: number = comboBoxLimit - totalMeshWifiCombo;
		if (eligibleQuantity <= 0) {
			return await this.createErrorResponse('LIMIT-MESH');
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: eligibleQuantity,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null
			}
		};
	}

	async getEligibleUpb(
		availableQuota: number,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		// Maximum 3 UPB per service.
		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			// Check eligible speed (>= 100Mbps)
			if (oli?.Type === 'Speed') {
				const downloadSpeed = oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
					'TmAssetMgmt-AssetXaIntegration'
				]?.find(asset => asset.Name === 'Download Speed');

				const speedInNumber = Number(
					(downloadSpeed?.Value ?? '0').replaceAll(' Kbps', '')
				);

				if (speedInNumber < 112640) {
					return await this.createErrorResponse('SPEED-UPB');
				}
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: availableQuota,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null
			}
		};
	}

	async getEligibleBlacknut(
		billingAccountNo: string,
		availableQuota: number,
		isEligibleGamepad: boolean,
		hsiMoli: Wso2ServiceAccountMoli
	): Promise<AddOnsEligibilityRes> {
		const maxGamepadController = 2;
		const maxBlacknut = 1;
		for (const oli of hsiMoli.ServiceAccountOli ?? []) {
			if (
				oli?.ProductName?.includes('Device/Service') &&
				oli.Type === 'Service'
			) {
				if (
					oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
						'TmAssetMgmt-AssetXaIntegration'
					]?.some(device => device.Value?.includes('Blacknut'))
				) {
					return await this.createErrorResponse('LIMIT-CG');
				}
			}
		}

		// Maximum gamepad controller is 2
		// Service need to have Cloud Gaming plan (Device bundle) to add Cloud Gaming addon
		// Customer only eligible to purchase 1 Cloud Gaming plan per Billing email address (refer Matrix 1)
		let targetBillingEmailAddress = null;
		const billingAccounts: {
			billingAccountNo: string;
			billingEmail: string | null;
		}[] = [];
		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				{
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue
				},
				LightweightFlagEnum.YES
			);

		for (const ca of wso2CARes.Response?.CustomerAccounts ?? []) {
			if (ca.SystemName === SystemNameEnum.NOVA) {
				for (const ba of ca.BillingAccounts ?? []) {
					if (ba.AccountNumber) {
						const emailBillTo =
							ba?.InvoiceProfileIntegration?.EmailBillTo ?? null;
						if (ba.AccountNumber === billingAccountNo)
							targetBillingEmailAddress = emailBillTo;

						if (ba.AccountNumber !== billingAccountNo) {
							billingAccounts.push({
								billingAccountNo: ba.AccountNumber,
								billingEmail: emailBillTo
							});
						}
					}
				}
			}
		}

		for (const ba of billingAccounts) {
			const wso2SARes = await this.fetchServiceAccount(ba.billingAccountNo);
			const hsiMoli = this.getHighSpeedInternetMoli(wso2SARes);
			if (
				hsiMoli?.ServiceAccountOli?.some(
					oli =>
						oli?.ProductName?.includes('Device/Service') &&
						oli.Type === 'Service' &&
						!oli['ListOfTmAssetMgmt-AssetXaIntegration']?.[
							'TmAssetMgmt-AssetXaIntegration'
						]?.some(
							device =>
								device.Name === 'Device Name' &&
								device.Value?.includes('Blacknut')
						)
				) &&
				ba.billingEmail === targetBillingEmailAddress
			) {
				return await this.createErrorResponse('EMAIL-CG');
			}
		}

		// The maximum number of gamepad controllers is 2
		// The maximum number of Blacknut devices is 1
		// If the number of total devices is greater than 7, the maximum number of gamepad controllers is 1
		// If the number of total devices is greater than 8, the maximum number of gamepad controllers is 0
		// The priority is given to the number of blacknut devices
		const remainingQuotaAfterBlacknut: number = availableQuota - maxBlacknut;
		const remainingGamepad =
			isEligibleGamepad && remainingQuotaAfterBlacknut >= maxGamepadController
				? maxGamepadController
				: isEligibleGamepad && remainingQuotaAfterBlacknut === 1
					? 1
					: 0;

		const eligibleQuantity = maxBlacknut + remainingGamepad;

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: eligibleQuantity,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null
			}
		};
	}

	async getEligibleTvPack(
		req: AddOnsEligibilityReq,
		wso2SARes: Wso2ServiceAccountRes
	): Promise<AddOnsEligibilityRes> {
		let hasContentDiscount = false;
		let hasHardBundle = false;
		let hasNonUpgradableTvPack = false;
		let isInInstallationPeriod = false;
		let pmeNumber: string | null = null;
		let siebelTvPackName: string | null = null;
		for (const sa of wso2SARes?.Response?.ServiceAccount ?? []) {
			pmeNumber = sa?.ProductPartNumber ?? null;

			for (const moli of sa.ServiceAccountMoli ?? []) {
				if (moli.ProductName?.includes('High Speed Internet')) {
					if (this.hasRecentInstallationOrder(moli)) {
						isInInstallationPeriod = true;
					}
				}

				if (moli.ProductName?.includes('unifi TV')) {
					for (const oli of moli.ServiceAccountOli ?? []) {
						if (oli.Status === 'Active') {
							if (oli.Type === 'HyppTV Package') {
								// Check for TV pack
								siebelTvPackName = oli.ProductName ?? null;
								// Check for non upgradeable tv pack
								if (oli.ProductName?.toLowerCase().includes('staff')) {
									hasNonUpgradableTvPack = true;
								}
							}

							// Check for content discount
							if (
								oli?.Type === 'Content Discount' &&
								oli.ProductName?.toLowerCase().includes('content discount')
							) {
								hasContentDiscount = true;
							}
						}
					}
				}
			}
		}

		if (pmeNumber) {
			const wso2QueryHardSoftBundleReq: Wso2QueryHardSoftBundleReq = {
				pmeNumber
			};

			const wso2QueryHardSoftBundleRes: Wso2QueryHardSoftBundleRes =
				await this.mwIntegration.Wso2EligibilityIntegration.getWso2QueryHardSoftBundle(
					wso2QueryHardSoftBundleReq
				);

			// Check if any item in the datalist satisfies the hard bundle condition
			hasHardBundle = wso2QueryHardSoftBundleRes.datalist.some(
				data => (data.oliMinQty ?? 0) >= 1
			);
		}

		if (siebelTvPackName) {
			if (
				siebelTvPackName.replaceAll(' (Promo)', '') ===
				req.ProductName.replaceAll(' (Promo)', '')
			) {
				return await this.createErrorResponse('TV-PACK-SAME');
			}

			const [currentTvPackPlan]: SelectTvPackCatalogue[] = await this.db
				.select()
				.from(tvPackCatalogueTableSchema)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE),
						eq(tvPackCatalogueTableSchema.SiebelTvPackName, siebelTvPackName)
					)
				)
				.execute();

			const [selectedTvPackPlan]: SelectTvPackCatalogue[] = await this.db
				.select()
				.from(tvPackCatalogueTableSchema)
				.where(
					and(
						eq(tvPackCatalogueTableSchema.PlanType, TvPackPlanTypeEnum.BUNDLE),
						eq(tvPackCatalogueTableSchema.SiebelTvPackName, req.ProductName)
					)
				)
				.execute();

			if (
				selectedTvPackPlan.MonthlyCommitment <
				currentTvPackPlan.MonthlyCommitment
			) {
				// If the selected TV pack plan is cheaper than the current TV pack plan, return error
				return await this.createErrorResponse('TV-PACK-DOWNGRADE');
			}
		}

		if (
			hasNonUpgradableTvPack ||
			isInInstallationPeriod ||
			hasHardBundle ||
			hasContentDiscount
		) {
			return await this.createErrorResponse('GENERAL');
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: true,
				EligibleQuantity: 1,
				Title: null,
				Reason: null,
				ReasonCode: null,
				CTAButtonText: null,
				CTAUrl: null
			}
		};
	}

	private removeConflictingOtts(
		ottAlaCarteList: SelectOttCatalogueView[],
		userOttListAll: SubscribedOttList,
		merchantId: number,
		conflictDict: Record<string, string[]>
	): SelectOttCatalogueView[] {
		const exists = userOttListAll.some(
			entitlement => entitlement.OttMerchantId === merchantId
		);
		if (!exists) return ottAlaCarteList;

		for (const [key, values] of Object.entries(conflictDict)) {
			if (
				userOttListAll.some(entitlement => entitlement.OttName.includes(key))
			) {
				return ottAlaCarteList.filter(
					alaCarte => !values.some(name => alaCarte.OttName.includes(name))
				);
			}
		}
		return ottAlaCarteList;
	}

	/**
	 * Checks if the platform is FTTH.
	 */
	isFtthPlatform(hsiMoli: Wso2ServiceAccountMoli): boolean {
		return (
			hsiMoli?.[
				'TmCutAssetMgmt-ServiceMeterIntegration'
			]?.TMAccessTechnology?.toUpperCase() === 'FTTH'
		);
	}

	/**
	 * Checks if there is a recent installation order within 14 days.
	 */
	hasRecentInstallationOrder(hsiMoli: Wso2ServiceAccountMoli): boolean {
		return (hsiMoli.ServiceAccountOli ?? []).some(oli => {
			const startDateDifference = this.getStartDateDifference(
				oli.StartDate ?? ''
			);
			return startDateDifference && startDateDifference.differenceInDays <= 14;
		});
	}

	/**
	 * Checks if the service account is active and has a Unifi Play TV.
	 */
	getUnifiPlayTvMoli(
		wso2ServiceAccounts: Wso2ServiceAccountRes
	): Wso2ServiceAccountMoli | null {
		for (const sa of wso2ServiceAccounts?.Response?.ServiceAccount ?? []) {
			if (sa.Status === 'Active') {
				for (const moli of sa.ServiceAccountMoli ?? []) {
					if (
						moli.Status === 'Active' &&
						(moli.ProductPartNumber?.includes('PR007670') ||
							moli.ProductPartNumber?.includes('PR000200'))
					) {
						return moli;
					}
				}
			}
		}
		return null;
	}

	/**
	 * Checks if the billing account has a good payment record
	 * (e.g., CBPR is A, No advance payment)
	 */
	async hasGoodPaymentRecord(
		serviceId: string,
		billingAccountNo: string,
		category: AddOnsRequestCategoryEnum
	): Promise<{
		reasonCode: string;
		cbpr: string | null;
	} | null> {
		const wso2Req: Wso2ConciseCustInfoReq = {
			requestHeader: {
				requestId: `XE-${Math.random().toString(36).slice(2)}`,
				eventName: 'evOXECONCISECustInfo'
			},
			kciRequest: {
				customerId: '',
				serviceNo: '',
				serviceId: serviceId,
				account: billingAccountNo
			}
		};
		const wso2Res: Wso2ConciseCustInfoRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ConciseCustInfo(
				wso2Req
			);

		if (
			wso2Res.kciResponse === null ||
			wso2Res.kciResponse.kciresponseData === null ||
			wso2Res.kciResponse.kciresponseData.length === 0
		) {
			return {
				reasonCode: 'PAYMENT-CONCISE-EMPTY',
				cbpr: null
			};
		}

		const conciseResponse = wso2Res.kciResponse.kciresponseData
			.filter(item => item !== null) // Remove nulls
			.find(() => true); // Pick the first element

		if (conciseResponse && 'Y' === conciseResponse.advancepaymentflag) {
			return {
				reasonCode: 'PAYMENT-ADVANCE',
				cbpr: conciseResponse.cpbr
			};
		}

		if (
			conciseResponse &&
			conciseResponse.cpbr !== 'A' &&
			(category === AddOnsRequestCategoryEnum.SMART_DEVICE ||
				category === AddOnsRequestCategoryEnum.SMART_HOME)
		) {
			const reasonCode =
				category === AddOnsRequestCategoryEnum.SMART_DEVICE
					? 'PAYMENT-SMD'
					: 'PAYMENT-SH';

			return {
				reasonCode,
				cbpr: conciseResponse.cpbr
			};
		}

		return null;
	}

	private getStartDateDifference(
		startDate: string
	): { differenceInDays: number; differenceInMonths: number } | null {
		if (!startDate || Number.isNaN(Date.parse(startDate))) {
			return null;
		}

		const currentDate = getMyTimeZoneDate();
		const completedOrderDate = parse(
			startDate,
			'yyyy-MM-dd HH:mm:ss',
			currentDate
		);

		return {
			differenceInDays: differenceInDays(currentDate, completedOrderDate),
			differenceInMonths: differenceInMonths(currentDate, completedOrderDate)
		};
	}

	private async isMalaysianCitizen(
		reportingParam: ReportingParams
	): Promise<AddOnsEligibilityRes | null> {
		if (this.idTokenInfo.IdType === IdTypeEnum.PASSPORT) {
			await this.createErrorResponse('GENERAL', reportingParam);
		}

		return null;
	}

	checkAvailableQuota(
		category: AddOnsRequestCategoryEnum,
		wso2ServiceAccountMoli: Wso2ServiceAccountMoli
	): {
		availableQuota: number;
		reasonCode: string | null;
	} {
		if (
			[
				AddOnsRequestCategoryEnum.MESH_WIFI_6,
				AddOnsRequestCategoryEnum.MESH_WIFI_7
			].includes(category)
		) {
			// OLI MESH
			// Maximum 2 MESH Wi-Fi 6 / 7 per service
			const quotaLimit: number = 2;
			const totalMeshWifi6: number =
				wso2ServiceAccountMoli.ServiceAccountOli?.reduce(
					(count, oli) =>
						count +
						(oli?.ProductName?.includes('MESH Wi-Fi') &&
						(oli?.ProductName?.includes('OTC') ||
							oli?.ProductName?.includes('RC'))
							? 1
							: 0),
					0
				) ?? 0;

			return {
				availableQuota: quotaLimit - totalMeshWifi6,
				reasonCode: totalMeshWifi6 >= quotaLimit ? 'LIMIT-ADDONS' : null
			};
		}

		if (category === AddOnsRequestCategoryEnum.UPB) {
			// OLI UPB
			// Maximum 3 for UPB
			const quotaLimit: number = 3;
			const totalUpb: number =
				wso2ServiceAccountMoli.ServiceAccountOli?.reduce(
					(count, oli) =>
						count +
						(oli?.ProductName?.includes('unifi Plus Box Add On') ? 1 : 0),
					0
				) ?? 0;
			return {
				availableQuota: quotaLimit - totalUpb,
				reasonCode: totalUpb >= quotaLimit ? 'LIMIT-ADDONS' : null
			};
		}
		// OLI Device/Service (MESH WIFI, SMART DEVICE, SMART HOME, BLACKNUT)
		// Maximum 10 devices per service
		const quotaLimit: number = 10;
		const totalDevices =
			wso2ServiceAccountMoli.ServiceAccountOli?.reduce(
				(count, oli) =>
					count + (oli?.ProductName?.includes('Device/Service') ? 1 : 0),
				0
			) ?? 0;
		return {
			availableQuota: quotaLimit - totalDevices,
			reasonCode: totalDevices >= quotaLimit ? 'LIMIT-ADDONS' : null
		};
	}

	async createErrorResponse(
		reasonCode: string,
		reportingParam?: ReportingParams
	): Promise<AddOnsEligibilityRes> {
		const error = await this.getErrorMessage(reasonCode);
		if (reportingParam) {
			const reportingDataParams = {
				category: reportingParam.category,
				idType: this.idTokenInfo.IdType,
				idValue: this.idTokenInfo.IdValue,
				billingAccountNo: reportingParam.decryptedBillAccNo,
				serviceId: reportingParam.serviceId,
				serviceStartDate: reportingParam.serviceStartDate,
				remarks: error.Message,
				cbpr: reportingParam.cbpr
			};
			const transactionId =
				await this.generateReportingData(reportingDataParams);
			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					IsEligible: false,
					Title: error.Title,
					Reason: error.Message,
					ReasonCode: error.Code,
					TransactionId: transactionId,
					CTAButtonText: error.CTAButtonText,
					CTAUrl: error.CTAUrl
				}
			};
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				IsEligible: false,
				Title: error.Title,
				Reason: error.Message,
				ReasonCode: error.Code,
				CTAButtonText: error.CTAButtonText,
				CTAUrl: error.CTAUrl
			}
		};
	}

	async getErrorMessage(code: string): Promise<SelectErrorMessages> {
		const [errorMessage] = await this.db
			.select()
			.from(errorMessagesTableSchema)
			.where(eq(errorMessagesTableSchema.Code, code))
			.execute();

		if (errorMessage) {
			return errorMessage;
		}

		//  return generic error message if specific one is not found
		return {
			Id: 0,
			Code: code,
			Title: 'Try again later',
			Message:
				"We're temporarily unable to retrieve your plan information. Please try again in a few minutes.",
			CTAButtonText: 'Try Again Later',
			CTAUrl: null,
			CreatedAt: getMyTimeZoneDate(),
			UpdatedAt: getMyTimeZoneDate()
		};
	}

	private async generateReportingData(params: {
		category: AddOnsRequestCategoryEnum;
		idType: string;
		idValue: string;
		billingAccountNo: string;
		serviceId: string;
		serviceStartDate: string | null;
		remarks: string;
		cbpr: string | null;
	}) {
		const billingProfile: NovaIcpBillingProfile = await novaBillingProfile(
			this.integrationId,
			params.billingAccountNo
		);

		const data: AddOnsReportingInfoData = {
			IdType: params.idType,
			IdValue: params.idValue,
			BillingAccountNo: params.billingAccountNo,
			BillingName: billingProfile.AccountName,
			BillingEmail: billingProfile.AccountEmail,
			BillingContactNo: billingProfile.AccountContactNo,
			LoginId: params.serviceId,
			ServiceStartDate: params.serviceStartDate,
			CBPR: params.cbpr
		};

		const [transaction] = await this.db
			.insert(addOnsReportingTableSchema)
			.values({
				IdType: params.idType,
				IdValue: params.idValue,
				OrderStatus: params.remarks === null ? 'Not Submitted' : 'Failed',
				Category: params.category,
				Remarks: params.remarks,
				InfoData: data,
				ErrorMessage:
					billingProfile.AccountName === 'N/A'
						? 'Unable to retrieve billing details'
						: null
			})
			.returning();

		return transaction.Id;
	}
}

export default AddOnsHelper;
