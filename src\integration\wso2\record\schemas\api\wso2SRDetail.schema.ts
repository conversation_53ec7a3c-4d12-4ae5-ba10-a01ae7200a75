import { type Static, t } from 'elysia';

const wso2SRDetailReqSchema = t.Object({
	Type: t.Optional(t.String()),
	icpIDType: t.Optional(t.String()),
	icpIDValue: t.Optional(t.String()),
	icpAccountNo: t.Optional(t.String()),
	novaAccountNo: t.Optional(t.String()),
	novaBillingAccountNo: t.Optional(t.String()),
	MobileAccountNo: t.Optional(t.String()),
	SearchSpecByPeriod: t.Optional(t.String()),
	SearchSpecByStartDate: t.Optional(t.String()),
	SearchSpecByEndDate: t.Optional(t.String()),
	SRNumber: t.Optional(t.String())
});

export type Wso2SRDetailReq = Static<typeof wso2SRDetailReqSchema>;

export const wso2SRDetailsReturnObjSchema = t.Object({
	SystemName: t.String({ examples: ['NOVA'] }),
	AccountNo: t.String({ examples: ['1-R9QA3P1'] }),
	BillingAccount: t.String({ examples: ['**********'] }),
	SRNumber: t.String({ examples: ['1-**********'] }),
	CustomerName: t.MaybeEmpty(t.String({ examples: ['JOHN DOE'] })),
	CustomerComments: t.MaybeEmpty(
		t.String({ examples: ['You have logged a complaint report.'] })
	),
	CreatedDate: t.String({ examples: ['2024-01-01'] }),
	ClosedDate: t.String({ examples: ['2024-01-01'] }),
	Status: t.String({ examples: ['Open'] }),
	Source: t.String({ examples: ['Customer Portal'] }),
	novaCase: t.String({ examples: ['Line Disconnect'] }),
	novaTTId: t.MaybeEmpty(t.String({ examples: ['1-**********'] })),
	Type: t.MaybeEmpty(t.String({ examples: ['Transfer/Refund'] })),
	Category: t.MaybeEmpty(t.String({ examples: ['Billing'] })),
	SubCategory: t.MaybeEmpty(t.String({ examples: ['Refund'] })),
	icpTelCase: t.MaybeEmpty(t.String()),
	icpSRNetCase: t.MaybeEmpty(t.String())
});

export type Wso2SRDetailsReturnObj = Static<
	typeof wso2SRDetailsReturnObjSchema
>;

export const wso2TTDetailsReturnObjSchema = t.Object({
	SystemName: t.String({ examples: ['NOVA'] }),
	TTNumber: t.String({ examples: ['1-**********'] }),
	Status: t.String({ examples: ['Open'] }),
	CreatedDate: t.String({ examples: ['2024-01-01'] }),
	ClosedDate: t.String({ examples: ['2024-01-01'] }),
	NovaCategory: t.MaybeEmpty(t.String({ examples: ['Billing'] })),
	ICPProductCategory: t.MaybeEmpty(
		t.String({ examples: ['High-Speed Internet'] })
	),
	ICPProductType: t.MaybeEmpty(t.String({ examples: ['Internet'] })),
	SymptomCode: t.String({ examples: ['Line Disconnect'] }),
	Product: t.String({ examples: ['Unifi 30Mbps'] }),
	ServiceID: t.String({ examples: ['Test@unifi'] }),
	NttNumber: t.String({ examples: ['NTT-********-94621'] }),
	NovaNttStatus: t.MaybeEmpty(t.String({ examples: ['Open'] })),
	BillingAccount: t.String({ examples: ['**********'] }),
	AccountNo: t.String({ examples: ['1-R9QA3P1'] }),
	TMActions: t.Array(
		t.Object({
			ActivityUID: t.String({ examples: ['**********'] }),
			Due: t.String({ examples: ['26/07/2024 15:30:00'] }),
			PlannedDate: t.String({ examples: ['26/07/2024 15:30:00'] }),
			PlannedCompletion: t.String({ examples: ['50'] }),
			Status: t.String({ examples: ['Open'] }),
			SubType: t.String({ examples: [''] }),
			Type: t.String({ examples: [''] }),
			ActivityAssignedDate: t.String({ examples: ['26/07/2024 15:30:00'] })
		})
	)
});

export type Wso2TTDetailsReturnObj = Static<
	typeof wso2TTDetailsReturnObjSchema
>;

export const wso2SRDetailResSchema = t.Object({
	Status: t.Object({
		Type: t.String({ examples: ['OK'] }),
		Code: t.String({ examples: ['0'] }),
		Message: t.MaybeEmpty(t.String({ examples: ['Success'] }))
	}),
	Response: t.Optional(
		t.Object({
			SRDetailsReturn: t.Array(wso2SRDetailsReturnObjSchema),
			TTDetailsReturn: t.Array(wso2TTDetailsReturnObjSchema)
		})
	)
});

export type Wso2SRDetailRes = Static<typeof wso2SRDetailResSchema>;
