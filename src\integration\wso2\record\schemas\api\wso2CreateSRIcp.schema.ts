import { type Static, t } from 'elysia';

export const wso2CreateSRICPSchema = t.Object({
	ListOfTmbCreateSrFromSttIo: t.MaybeEmpty(
		t.Object({
			TmbSttServiceRequest: t.MaybeEmpty(
				t.Array(
					t.MaybeEmpty(
						t.Object({
							CustomerRowID: t.MaybeEmpty(
								t.String({ examples: ['1-1F32AJK23'] })
							),
							ServiceRowID: t.MaybeEmpty(
								t.String({ examples: ['1-1F32AJK23'] })
							),
							ContactDetailRowID: t.MaybeEmpty(
								t.String({ examples: ['1-1F32AJK23'] })
							),
							DateCreated: t.Maybe<PERSON>(t.String()),
							CreatedBy: t.MaybeEmpty(t.String()),
							Area: t.MaybeEmpty(t.String({ examples: ['Service Failure'] })),
							Type: t.MaybeEmpty(t.String({ examples: ['Fault'] })),
							SubArea: t.MaybeEmpty(
								t.String({ examples: ['All Services Down'] })
							),
							SRRowId: t.MaybeEmpty(t.String({ examples: ['1-1F32AJK23'] })),
							Group: t.MaybeEmpty(
								t.String({ examples: ['WIDER_TMUC_TECH_SOC2'] })
							),
							Priority: t.MaybeEmpty(t.String({ examples: ['4-Low'] })),
							SRNumber: t.MaybeEmpty(t.String({ examples: ['1-1F32AJK23'] })),
							Severity: t.MaybeEmpty(t.String({ examples: ['Low'] })),
							CreatedSource: t.MaybeEmpty(t.String({ examples: ['DICE'] })),
							Status: t.MaybeEmpty(t.String({ examples: ['Open'] })),
							ServiceType: t.MaybeEmpty(t.String({ examples: ['Internet'] })),
							BillingAccountNo: t.MaybeEmpty(
								t.String({ examples: ['**********'] })
							),
							ClosureCategory: t.MaybeEmpty(t.String()),
							ClosureRemarks: t.MaybeEmpty(t.String()),
							ComplaintCounter: t.MaybeEmpty(t.String()),
							DetailedDescription: t.MaybeEmpty(
								t.String({ examples: ['This is a test description'] })
							),
							InterfaceStatus: t.MaybeEmpty(t.String({ examples: ['New'] })),
							BillingAccountRowID: t.MaybeEmpty(
								t.String({ examples: ['1-1F32AJK23'] })
							),
							'TMBNetCaseCat-Fault': t.MaybeEmpty(t.String()),
							SRNetCaseCategory: t.MaybeEmpty(t.String()),
							SRNetProduct: t.MaybeEmpty(t.String()),
							PreferredAcknowledgement: t.MaybeEmpty(
								t.String({ examples: ['Email'] })
							),
							Product: t.MaybeEmpty(t.String({ examples: ['Unifi 30Mbps'] })),
							ProductCategory: t.MaybeEmpty(
								t.String({ examples: ['High-Speed Internet'] })
							),
							ClosureReason: t.MaybeEmpty(t.String()),
							ContactDetailReportedID: t.MaybeEmpty(
								t.String({ examples: ['1-1F32AJK23'] })
							),
							TelCaseCategory: t.MaybeEmpty(t.String()),
							TelProductType: t.MaybeEmpty(t.String()),
							Source: t.MaybeEmpty(t.String({ examples: ['EASY FIX'] })),
							NewPassword: t.MaybeEmpty(t.String()),
							TTCAging: t.MaybeEmpty(t.String()),
							TTNum: t.MaybeEmpty(t.String()),
							SRFNum: t.MaybeEmpty(t.String()),
							CallBack: t.MaybeEmpty(t.String({ examples: ['Y'] })),
							CallBackDate: t.MaybeEmpty(t.String()),
							CallBackTime: t.MaybeEmpty(t.String()),
							LoginIDType: t.MaybeEmpty(
								t.String({ examples: ['/service/tmm_streamyx'] })
							),
							NetProductType: t.MaybeEmpty(t.String())
						})
					)
				)
			)
		})
	)
});

export type Wso2CreateSRICP = Static<typeof wso2CreateSRICPSchema>;
