import { createHash } from 'node:crypto';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../enum/user.enum';
import { UE_ERROR } from '../../error';
import type { EncryptedKeyValueData } from './schemas/encryption';

function encodeHex(bytes: Uint8Array): string {
	return Array.from(bytes)
		.map(b => b.toString(16).padStart(2, '0'))
		.join('');
}

function decodeHex(hex: string): Uint8Array {
	const bytes = new Uint8Array(Math.ceil(hex.length / 2));
	for (let i = 0; i < bytes.length; i++) {
		bytes[i] = Number.parseInt(hex.slice(i * 2, i * 2 + 2), 16);
	}
	return bytes;
}

// Utility function to create a SHA-256 hash
//* Replace Crypto-js's sha256 function
export function sha256(salt: string, value: string): string {
	const text = `${value}_ue_${salt.trim().toUpperCase().replace(/\s+/g, '_')}`;
	return createHash('sha256').update(text).digest('hex');
}

// Utility function to create a hash key
async function createHashKey(
	secret: string,
	passphrase: string
): Promise<CryptoKey> {
	const hashInput: string = `${passphrase}${secret}`;
	const md: ArrayBuffer = await crypto.subtle.digest(
		'SHA-256',
		new TextEncoder().encode(hashInput)
	);
	const hashOutput: Uint8Array = new Uint8Array(md);
	return crypto.subtle.importKey(
		'raw',
		hashOutput,
		{ name: 'AES-GCM' },
		false,
		['encrypt', 'decrypt']
	);
}

// Utility function to generate random nonce
function getRandomNonce(numBytes: number): Uint8Array {
	const nonce: Uint8Array = new Uint8Array(numBytes);
	crypto.getRandomValues(nonce);
	return nonce;
}

// Function to decrypt data
export async function decrypt(
	encrypted: string,
	passphrase: string
): Promise<string> {
	const secret: string = process.env.UDID_SYSTEM_SECRET ?? '';
	const key: CryptoKey = await createHashKey(secret, passphrase);
	const bytes: Uint8Array = decodeHex(encrypted);
	const iv: Uint8Array = bytes.slice(0, 12);
	const cipherText: Uint8Array = bytes.slice(12);

	const decrypted: ArrayBuffer = await crypto.subtle.decrypt(
		{ name: 'AES-GCM', iv: iv, tagLength: 128 },
		key,
		cipherText
	);

	return new TextDecoder().decode(decrypted);
}

// Function to encrypt data
export async function encrypt(
	originalText: string,
	passphrase: string
): Promise<string> {
	const secret: string = process.env.UDID_SYSTEM_SECRET ?? '';
	const key: CryptoKey = await createHashKey(secret, passphrase);
	const iv: Uint8Array = getRandomNonce(12);

	const encrypted: ArrayBuffer = await crypto.subtle.encrypt(
		{ name: 'AES-GCM', iv: iv, tagLength: 128 },
		key,
		new TextEncoder().encode(originalText)
	);

	const encryptedBytes: Uint8Array = new Uint8Array(encrypted);
	const encryptedWithIv: Uint8Array = new Uint8Array(
		iv.length + encryptedBytes.length
	);
	encryptedWithIv.set(iv);
	encryptedWithIv.set(encryptedBytes, iv.length);

	return encodeHex(encryptedWithIv);
}

export async function getMaskedValue(
	encValue: string,
	credentialType: string,
	credentialKey: string
): Promise<string> {
	let maskedValue = '';
	const oriValue = await decrypt(encValue, credentialKey);
	if (credentialType === 'mobile') {
		maskedValue = `6${oriValue.substring(0, 5)}****${oriValue.substring(
			oriValue.length - 2
		)}`;
	} else {
		maskedValue = maskEmailAddress(oriValue);
	}
	return maskedValue;
}

// sample output : r*******z@g******.com
export function maskEmailAddress(email: string): string {
	const [localPart, domain] = email.split('@');

	if (!localPart || !domain)
		throw new UE_ERROR(
			'Invalid email format',
			StatusCodeEnum.UNPROCESSABLE_ENTITY
		);

	// Mask local part: keep first and last character, mask the rest
	const maskedLocal =
		localPart.length > 2
			? localPart[0] +
				'*'.repeat(localPart.length - 2) +
				localPart[localPart.length - 1]
			: `${localPart[0]}*`;

	// Mask domain part: keep first letter
	const domainParts = domain.split('.');
	const maskedDomain =
		domainParts.length > 1
			? `${domainParts[0][0]}${'*'.repeat(domainParts[0].length - 1)}.${domainParts.slice(1).join('.')}`
			: domain;

	return `${maskedLocal}@${maskedDomain}`;
}

export async function getEncryptedKeyValue(
	value: string,
	type: string
): Promise<EncryptedKeyValueData> {
	// for Identification: IdType and IdValue
	// for Credential: CredentialType and CredentialValue
	let sanitizedValue: string = value.trim();

	// remove all whitespaces and hyphens
	if (type.includes(IdTypeEnum.NEW_NRIC))
		sanitizedValue = sanitizedValue.replace(/[\s-]+/g, '');

	if (type.includes(IdTypeEnum.PASSPORT))
		sanitizedValue = sanitizedValue.toUpperCase();

	const key: string = sha256(type, sanitizedValue).toString();
	const encValue: string = await encrypt(sanitizedValue, key);

	return { key, encValue };
}

/**
 * Mask an ID by keeping the first 3 and last 2 chars,
 * replacing everything in between with asterisks.
 * the valid ID is at least 5 chars
 */
export function maskIdNumber(id: string): string {
	const minLength: number = 5;
	const prefixLength: number = 3;
	const suffixLength: number = 2;

	if (id.length < minLength) return id;

	const prefix: string = id.slice(0, prefixLength);
	const suffix: string = id.slice(-suffixLength);
	const maskedCount: number = id.length - (prefixLength + suffixLength);
	const masked: string = '*'.repeat(maskedCount);

	return `${prefix}${masked}${suffix}`;
}
