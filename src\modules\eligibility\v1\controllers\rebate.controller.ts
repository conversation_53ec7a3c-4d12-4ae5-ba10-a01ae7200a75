import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import { type RebateRes, rebateResSchema } from '../schemas/api/rebate.schema';
import RebateEligibility from '../services/rebate.service';

const rebateV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			RebateEligibility: new RebateEligibility(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/rebate',
		async (ctx): Promise<RebateRes> => {
			return await ctx.RebateEligibility.getRebateEligibility();
		},
		{
			detail: {
				description:
					'Get rebate eligibility for a given customer ID. This API is normally used by Easyfix team to compensate customers troubles.<br><br><b>Backend System:</b> Unknown <br> <b>Table:</b> rebate_redeem_eligibility',
				tags: ['Eligibility']
			},
			response: {
				200: rebateResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default rebateV1Routes;
