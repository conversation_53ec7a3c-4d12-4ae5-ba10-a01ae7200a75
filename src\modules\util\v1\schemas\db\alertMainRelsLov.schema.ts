import { integer, pgTable, text } from 'drizzle-orm/pg-core';

export const alertMainRelsTableSchema = pgTable('alert_main_rels', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Order: integer('order'),
	ParentId: integer('parent_id').notNull(),
	Path: text('path').notNull(),
	AlertServiceLovId: integer('alert_service_lov_id'),
	AlertAreaLovId: integer('alert_area_lov_id')
});

export type SelectAlertMainRels = typeof alertMainRelsTableSchema.$inferSelect;
