import { envConfig } from '../../config/env.config';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import { fetchApi } from '../helper/fetchApi.helper';
import type {
	TemporalTriggerWorkflowReq,
	TemporalTriggerWorkflowRes,
	TemporalUserTaskSignalReq,
	TemporalUserTaskSignalRes
} from './schemas/api/temporal.schema';

class TemporalIntegration {
	private integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	private async handleRequest<TReq extends object, TRes>(
		url: string,
		body: TReq,
		errorMessage: string
	): Promise<TRes> {
		try {
			const apiKey = process.env.TEMPORAL_X_API_KEY ?? '';

			const payload: RequestInit = {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'X-Api-Key': apiKey
				},
				body: JSON.stringify(body)
			};

			const res = await fetchApi(this.integrationId, url, payload);
			const resBody: TRes & { Code?: number } = await res.json();

			if (!res.ok || resBody.Code !== 200) {
				throw new UE_ERROR(errorMessage, StatusCodeEnum.TEMPORAL_ERROR, {
					integrationId: this.integrationId,
					response: resBody
				});
			}

			return resBody;
		} catch (error: unknown) {
			if (error instanceof UE_ERROR) throw error;
			throw new UE_ERROR(
				`${errorMessage} - Unexpected`,
				StatusCodeEnum.TEMPORAL_ERROR,
				{
					integrationId: this.integrationId,
					response:
						error instanceof Error ? error.message : JSON.stringify(error)
				}
			);
		}
	}

	async triggerUserTaskSignal(
		bodyRequest: TemporalUserTaskSignalReq
	): Promise<TemporalUserTaskSignalRes> {
		const url = envConfig().TEMPORAL_TRIGGER_USER_TASK_SIGNAL_URL;

		return this.handleRequest<
			TemporalUserTaskSignalReq,
			TemporalUserTaskSignalRes
		>(url, bodyRequest, 'Temporal trigger user task signal threw an error');
	}

	async triggerWorkflow(
		bodyRequest: TemporalTriggerWorkflowReq
	): Promise<TemporalTriggerWorkflowRes> {
		const url: string = envConfig().TEMPORAL_TRIGGER_WORKFLOW_URL.replace(
			'/api/',
			`/api/${bodyRequest.Namespace}/`
		);
		const body = {
			OrderId: bodyRequest.OrderId,
			IsOrderable: bodyRequest.IsOrderable
		};

		return this.handleRequest<typeof body, TemporalTriggerWorkflowRes>(
			url,
			body,
			'Temporal trigger workflow threw an error'
		);
	}
}

export default TemporalIntegration;
