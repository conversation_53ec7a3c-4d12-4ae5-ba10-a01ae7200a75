import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia } from 'elysia';
import { SegmentEnum } from '../../../../enum/header.enum';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type AddOnsAppointmentSlotRes,
	addOnsAppointmentSlotReqSchema,
	addOnsAppointmentSlotResSchema
} from '../schemas/api/addOns.schema';
import AddOns from '../services/addOns.service';

const addOnsV1Routes = new Elysia({ prefix: '/add-ons' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			AddOns: new AddOns(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/appointment-slots',
		async (ctx): Promise<AddOnsAppointmentSlotRes> => {
			return await ctx.AddOns.getAppointmentSlots(
				ctx.body,
				ctx.headers.segment ?? SegmentEnum.CONSUMER
			);
		},
		{
			detail: {
				description:
					'Get appointment slots for add-ons delivery. This endpoint is currently only available for mesh wifi 6. <br><br> <b>Backend System: </b> NOVA SIEBEL, ICP',
				tags: ['Record']
			},
			body: addOnsAppointmentSlotReqSchema,
			response: {
				200: addOnsAppointmentSlotResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default addOnsV1Routes;
