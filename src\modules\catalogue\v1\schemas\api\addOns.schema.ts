import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

const productObjSchema = t.Object({
	Id: t.Number(),
	Name: t.String(),
	DisplayName: t.String(),
	PartnerId: t.Nullable(t.String()),
	Description: t.Array(t.String()),
	LeasingType: t.String(),
	ContractTerm: t.Number(),
	MonthlyCommitment: t.Numeric(),
	PurchasePrice: t.Numeric(),
	DevicePrice: t.Numeric(),
	RRP: t.Nullable(t.Numeric()),
	Summary: t.Nullable(t.String()),
	Specification: t.Nullable(t.String()),
	ImageUrl: t.String(),
	Save: t.Nullable(t.String()),
	VoucherName: t.Nullable(t.String()),
	PartNumber: t.Nullable(t.String()),
	ProductId: t.Nullable(t.String()),
	IsBundle: t.Bo<PERSON>an(),
	DeliveryPartner: t.Nullable(t.String())
});

export const productDetailsListSchema = t.Array(productObjSchema);

export type ProductDetailsList = Static<typeof productDetailsListSchema>;

const ottDetailsSchema = t.Object({
	OttName: t.String(),
	OttIsActive: t.Boolean(),
	OttMerchantId: t.Number(),
	OttProductId: t.String(),
	OttOmgId: t.Number(),
	OttUniversalLink: t.String(),
	OttIconPath: t.String(),
	OttLoginType: t.String(),
	OttLoginInstruction: t.Nullable(t.String()),
	OttVerificationInstruction: t.Nullable(t.String()),
	OttActivationLink: t.Nullable(t.String()),
	OttSequence: t.Number(),
	OttPrice: t.Numeric(),
	OttVideoUrl: t.MaybeEmpty(t.String()),
	OttDescription: t.MaybeEmpty(t.String()),
	OttPackageType: t.MaybeEmpty(t.String()),
	OttPackageDetails: t.MaybeEmpty(t.Array(t.String())),
	OttPackageDuration: t.MaybeEmpty(t.String()),
	TmBundleId: t.MaybeEmpty(t.String()),
	NetflixBundleId: t.MaybeEmpty(t.String()),
	NetflixOfferId: t.MaybeEmpty(t.String())
});

export type OttDetails = Static<typeof ottDetailsSchema>;

const ottDetailsListSchema = t.Array(ottDetailsSchema);

const tvPackProductListSchema = t.Array(
	t.Object({
		Id: t.Number(),
		Name: t.String(),
		PartnerId: t.Nullable(t.String()),
		Description: t.Array(t.String()),
		MonthlyCommitment: t.Numeric(),
		LeasingType: t.Nullable(t.String()),
		ContractTerm: t.Nullable(t.Number()),
		Summary: t.Nullable(t.String()),
		Specification: t.Nullable(t.String()),
		ImageUrl: t.String(),
		VideoUrl: t.Nullable(t.String()),
		DisplayName: t.String(),
		Save: t.Nullable(t.String()),
		VoucherName: t.Nullable(t.String()),
		PartNumber: t.Nullable(t.String()),
		ProductId: t.Nullable(t.String()),
		PlanId: t.String(),
		OttSelectionCustChoice: ottDetailsListSchema,
		OttSelectionFixed: ottDetailsListSchema
		// OttSelectionNetflix: ottDetailsListSchema,
	})
);

export type TvPackProductList = Static<typeof tvPackProductListSchema>;

export const addOnsCatalogueListSchema = t.Array(
	t.Object({
		IsErrorFromWso2: t.Optional(t.Boolean()),
		Category: t.String(),
		FaqUrl: t.Nullable(t.String()),
		TncUrl: t.Nullable(t.String()),
		WarrantyPolicyUrl: t.Nullable(t.String()),
		PrivacyNoticeUrl: t.Nullable(t.String()),
		PlanId: t.Optional(t.String()),
		ProductDetails: t.Optional(
			t.Union([
				productDetailsListSchema,
				tvPackProductListSchema,
				ottDetailsListSchema
			])
		)
	})
);

export type AddOnsCatalogueList = Static<typeof addOnsCatalogueListSchema>;

export const addonsCatalogueReqSchema = t.Object({
	AccountId: t.String({ minLength: 1 })
});

export type AddOnsCatalogueReq = Static<typeof addonsCatalogueReqSchema>;

export const addonsCatalogueResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: addOnsCatalogueListSchema
	},
	{ description: 'A list of device addons details is successfully retrieved.' }
);

export type AddOnsCatalogueRes = Static<typeof addonsCatalogueResSchema>;

export const ottAlaCarteCatalogueReqSchema = t.Object({
	AccountId: t.String({ minLength: 1 })
});

export type OttAlaCarteCatalogueReq = Static<
	typeof ottAlaCarteCatalogueReqSchema
>;

export const ottSwapCatalogueReqSchema = t.Object({
	AccountId: t.String({ minLength: 1 }),
	OttPlanSwapGroup: t.String({ minLength: 1 })
});

export type OttSwapCatalogueReq = Static<typeof ottSwapCatalogueReqSchema>;

export const ottChangePlanCatalogueReqSchema = t.Object({
	OttPlanId: t.String({ minLength: 1 }),
	OttMerchantId: t.String({ minLength: 1 })
});

export type OttChangePlanCatalogueReq = Static<
	typeof ottChangePlanCatalogueReqSchema
>;
