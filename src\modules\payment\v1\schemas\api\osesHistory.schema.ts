import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const osesHistoryReqSchema = t.Object({
	Email: t.String({ format: 'email', example: '<EMAIL>' })
});

export type OsesHistoryReq = Static<typeof osesHistoryReqSchema>;

export const osesHistoryResObjSchema = t.Object({
	MerchantTxnId: t.String({ examples: [100000] }),
	MerchantId: t.String({ examples: ['**********'] }),
	PayerEmail: t.String({ format: 'email', examples: ['<EMAIL>'] }),
	PayerName: t.String({ examples: ['John Doe'] }),
	BankReference: t.Nullable(t.String()),
	Source: t.Nullable(t.String({ examples: ['UNIFI_APP'] })),
	TotalAmount: t.String({ examples: ['1.00'] }),
	PaymentType: t.String({ examples: ['PFM'] }),
	TxnDate: t.Nullable(t.String()),
	TxnStatus: t.Nullable(t.String({ examples: ['SUCCESSFUL'] })),
	BillingAccounts: t.Array(
		t.Optional(
			t.Object({
				Name: t.String({ examples: ['John Doe'] }),
				Email: t.String({ format: 'email', examples: ['<EMAIL>'] }),
				BillingAccountNo: t.String({ examples: ['**********'] }),
				BillNo: t.Optional(t.String({ examples: ['1234'] })),
				GrossAmount: t.String({ examples: ['1.00'] }),
				GbtAmount: t.String({ examples: ['1.00'] }),
				NettAmount: t.String({ examples: ['1.00'] }),
				MiscAmount: t.String({ examples: ['1.00'] }),
				SystemName: t.String({ examples: ['NOVA'] })
			})
		)
	)
});

export type OsesHistoryResObj = Static<typeof osesHistoryResObjSchema>;

export const osesHistoryResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			TransactionHistory: t.Array(osesHistoryResObjSchema)
		})
	},
	{
		description:
			"Customer's OSES payment transaction history successfully retrieved."
	}
);

export type OsesHistoryRes = Static<typeof osesHistoryResSchema>;
