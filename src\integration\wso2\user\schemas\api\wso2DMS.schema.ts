import { type Static, t } from 'elysia';

const wso2DMSReqSchema = t.Object({
	requestHeader: t.Object({ requestId: t.String(), eventName: t.String() }),
	service: t.Object({ serviceId: t.String() })
});

export type Wso2DMSReq = Static<typeof wso2DMSReqSchema>;

export const wso2DMSResSchema = t.Object({
	responseHeader: t.MaybeEmpty(
		t.Object({
			rqUuid: t.MaybeEmpty(t.String()),
			requestId: t.MaybeEmpty(t.String()),
			status: t.MaybeEmpty(t.String()),
			statusCode: t.MaybeEmpty(t.String()),
			errorCode: t.Maybe<PERSON>mpty(t.Null()),
			errorMessage: t.Maybe<PERSON>mpty(t.Null()),
			errorDetail: t.MaybeEmpty(t.Null()),
			errorPayload: t.MaybeEmpty(t.Null())
		})
	),
	service: t.MaybeEmpty(
		t.Object({
			dmsServiceId: t.MaybeEmpty(t.String()),
			serviceStatus: t.Maybe<PERSON>mpty(t.String()),
			serviceStartDate: t.Maybe<PERSON>mpty(t.String()),
			serviceEndDate: t.MaybeEmpty(t.String()),
			cmpgnMgrProfile: t.MaybeEmpty(
				t.Object({
					firstName: t.MaybeEmpty(t.String()),
					lastName: t.MaybeEmpty(t.String()),
					email: t.MaybeEmpty(t.String())
				})
			),
			adCreditPurchase: t.MaybeEmpty(
				t.Array(
					t.MaybeEmpty(
						t.Object({
							product: t.MaybeEmpty(t.String()),
							purchId: t.MaybeEmpty(t.String()),
							adCreditAmount: t.MaybeEmpty(t.String()),
							purchaseDate: t.MaybeEmpty(t.String()),
							expiryDate: t.MaybeEmpty(t.String()),
							balance: t.MaybeEmpty(t.String()),
							type: t.MaybeEmpty(t.String()),
							orderNumber: t.MaybeEmpty(t.String())
						})
					)
				)
			),
			adCreditUtilization: t.MaybeEmpty(
				t.Array(
					t.MaybeEmpty(
						t.Object({
							utilId: t.MaybeEmpty(t.String()),
							item: t.MaybeEmpty(t.String()),
							adCreditUtilized: t.MaybeEmpty(t.String()),
							freeAdCreditUsed: t.MaybeEmpty(t.String()),
							paidAdCreditUsed: t.MaybeEmpty(t.String()),
							date: t.MaybeEmpty(t.String()),
							UtilizationStatus: t.MaybeEmpty(t.String())
						})
					)
				)
			)
		})
	)
});

export type Wso2DMSRes = Static<typeof wso2DMSResSchema>;
