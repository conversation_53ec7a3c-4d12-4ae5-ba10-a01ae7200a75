import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes,
	Wso2CustomerAccountsInArray,
	Wso2CustomerBillingAccountsInArray
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import {
	icpBillingProfile,
	novaBillingProfile
} from '../../../../shared/common';
import { decrypt } from '../../../../shared/encryption/aesGcm';
import type { NovaIcpBillingProfile } from '../../../../shared/schemas/api/novaBillingProfile.schema';
import type {
	VocUserDetailsReq,
	VocUserDetailsRes
} from '../schemas/api/survey.schema';
import {
	type SelectTnpsUserDetails,
	qualtrixTnpsUserDetailsTableSchema
} from '../schemas/db/qualtrixTnpsUserDetails.schema';

class VocCallback {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(this.integrationId);
	}

	async getVocTnpsUserDetails(
		req: VocUserDetailsReq
	): Promise<VocUserDetailsRes> {
		const decryptedCustomerId: string[] = (
			await decrypt(req.encryptedCustomerId)
		).split(';');
		const idType: string = decryptedCustomerId[0];
		const idValue: string = decryptedCustomerId[1];
		const billingAccountNo: string = decryptedCustomerId[2];
		const getWso2UserIntegration = this.mwIntegration.Wso2UserIntegration;

		const wso2CustomerAccReq: Wso2CustomerAccountReq = {
			idType: idType,
			idValue: idValue
		};
		const wso2CustomerAccRes: Wso2CustomerAccountRes =
			await getWso2UserIntegration.getWso2CustomerAccount(
				wso2CustomerAccReq,
				LightweightFlagEnum.YES
			);

		//Find the right account
		let novaIcp: Wso2CustomerAccountsInArray | undefined;
		let novaIcpBA: Wso2CustomerBillingAccountsInArray | undefined;
		if (wso2CustomerAccRes?.Response?.CustomerAccounts) {
			for (const ca of wso2CustomerAccRes.Response.CustomerAccounts) {
				if (
					ca &&
					(ca.SystemName === 'NOVA' || ca.SystemName === 'ICP') &&
					ca.BillingAccounts &&
					ca.BillingAccounts.length > 0
				) {
					novaIcp = ca;
					for (const ba of ca.BillingAccounts) {
						if (
							ba?.AccountNumber === billingAccountNo &&
							ba?.AccountStatus !== 'Terminated' &&
							ba?.AccountStatus !== 'Suspended'
						) {
							novaIcpBA = ba;
							break;
						}
					}
					break;
				}
			}
		}

		//Set user details
		let systemName = '';
		let serviceNumber = '';
		let productName = '';
		if (novaIcp && novaIcpBA) {
			const wso2ServiceAccountReq: Wso2ServiceAccountReq = {
				idType: idType,
				idValue: idValue,
				SystemName: novaIcp?.SystemName ?? '',
				BillingAccountNo: novaIcpBA?.AccountNumber ?? ''
			};
			const wso2ServiceAccountRes: Wso2ServiceAccountRes =
				(await getWso2UserIntegration.getWso2ServiceAccount(
					wso2ServiceAccountReq,
					LightweightFlagEnum.YES
				)) as Wso2ServiceAccountRes;

			if (wso2ServiceAccountRes?.Response?.ServiceAccount) {
				for (const sa of wso2ServiceAccountRes.Response.ServiceAccount) {
					if (sa?.Products && sa.Products.length > 0) {
						for (const product of sa.Products) {
							if (product?.ProductName === 'Internet') {
								productName = sa?.ProdPromName ?? '';
								serviceNumber = product?.SerialNumber;
								break;
							}
							break;
						}
						break;
					}
				}
			}
			systemName = novaIcp?.SystemName ?? '';
			const billingProfile: NovaIcpBillingProfile =
				systemName === SystemNameEnum.ICP
					? await icpBillingProfile(
							this.integrationId,
							idType,
							idValue,
							novaIcpBA.AccountNumber ?? '',
							wso2CustomerAccRes
						)
					: await novaBillingProfile(
							this.integrationId,
							novaIcpBA.AccountNumber ?? ''
						);

			//Check if the user has already submitted the survey
			//If the user has not already submitted the survey, update the table
			const tnpsUserDetails: SelectTnpsUserDetails[] =
				await this.getTnpsUserDetails(idType, idValue);

			let surveyEligible = false;
			if (
				tnpsUserDetails.length > 0 &&
				tnpsUserDetails[0].SubmissionDate === null
			) {
				surveyEligible = true;
			}
			if (surveyEligible) {
				await this.updateTnpsUserDetails(
					idType,
					idValue,
					billingProfile.AccountContactNo
				);
			}

			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Response: {
					Response: {
						RequestId: req.request.requestId,
						Status: 'Success',
						StatusCode: '0'
					},
					ResponseId: req.responseId,
					CustomerUniqueId: idValue,
					Journey: 'Myunifi',
					Activity: 'General',
					CustomerName: novaIcp?.Name ?? '',
					ServiceNumber: serviceNumber,
					CustomerEmail: billingProfile.AccountEmail,
					Package: productName,
					CustomerSourceSystem: systemName
				}
			};
		}

		throw new UE_ERROR(
			'Account not found. Unable to proceed without a valid account, please ensure your details are correct or contact our support team for assistance.',
			StatusCodeEnum.NOT_FOUND_ERROR
		);
	}

	private async getTnpsUserDetails(
		idType: string,
		idValue: string
	): Promise<SelectTnpsUserDetails[]> {
		return await this.db
			.select()
			.from(qualtrixTnpsUserDetailsTableSchema)
			.where(
				and(
					eq(qualtrixTnpsUserDetailsTableSchema.IdType, idType),
					eq(qualtrixTnpsUserDetailsTableSchema.IdValue, idValue)
				)
			)
			.groupBy(qualtrixTnpsUserDetailsTableSchema.SubmissionDate)
			.limit(1)
			.execute();
	}

	private async updateTnpsUserDetails(
		idType: string,
		idValue: string,
		mobileNumber: string
	): Promise<SelectTnpsUserDetails[]> {
		return await this.db
			.update(qualtrixTnpsUserDetailsTableSchema)
			.set({
				SubmissionDate: new Date(),
				SubmissionStatus: 'Submitted',
				MobileNumber: mobileNumber
			})
			.where(
				and(
					eq(qualtrixTnpsUserDetailsTableSchema.IdType, idType),
					eq(qualtrixTnpsUserDetailsTableSchema.IdValue, idValue)
				)
			)
			.returning();
	}
}

export default VocCallback;
