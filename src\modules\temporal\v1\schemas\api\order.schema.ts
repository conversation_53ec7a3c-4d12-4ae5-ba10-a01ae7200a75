import { type Static, t } from 'elysia';
import { wso2OrderMonitoringResSchema } from '../../../../../integration/wso2/order/schemas/api/wso2OrderMonitoring.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const { OrderMonitoringResponse } =
	wso2OrderMonitoringResSchema.properties.Response.properties;

export const orderDetailsBodyReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456'] }),
	Params: t.Array(t.String({ examples: ['IdType', 'SmartDevice'] })),
	IsOrderable: t.<PERSON>({ examples: [true] })
});

export type OrderDetailsBodyReq = Static<typeof orderDetailsBodyReqSchema>;

export const orderDetailsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Record(t.String(), t.String())
	},
	{
		description: 'Order Details retrieved.'
	}
);
/**
 * This endpoint allows clients to request specific data fields by providing
 * an array of "Params" in the request body. This flexibility enables clients
 * to retrieve only the information they need, reducing unnecessary data transfer.
 *
 * Example 1: Requesting only "IdType"
 * Request body: { "Params": ["IdType"] }
 * Response: { "Response": { "IdType": "New NRIC" } }
 *
 * Example 2: Requesting both "IdType" and "SmartDevice"
 * Request body: { "Params": ["IdType", "SmartDevice"] }
 * Response: { "Response": { "IdType": "New NRIC", "SmartDevice": "false" } }
 *
 * Example 3: Requesting only "SmartDevice"
 * Request body: { "Params": ["SmartDevice"] }
 * Response: { "Response": { "SmartDevice": "false" } }
 */
export type OrderDetailsRes = Static<typeof orderDetailsResSchema>;

export const orderUpdateReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456'] }),
	OrderStatus: t.String({ examples: ['SUBMITTED', 'CANCELLED'] }),
	OrderProgress: t.String({ examples: ['PROCESSING', 'MSR FAILED'] })
});

export type OrderUpdateReq = Static<typeof orderUpdateReqSchema>;

export const updateOrderDetailsReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456'] }),
	Type: t.String({ examples: ['Status', 'Others'] }),
	Details: t.Record(t.String(), t.Unknown()),
	IsOrderable: t.Boolean({ examples: [true] })
});
export type OrderUpdateDetailsReq = Static<typeof updateOrderDetailsReqSchema>;

export const getNovaStatusReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456'] })
});
export type GetNovaStatusReq = Static<typeof getNovaStatusReqSchema>;

export const getNovaStatusResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: OrderMonitoringResponse
});

export type GetNovaStatusRes = Static<typeof getNovaStatusResSchema>;
