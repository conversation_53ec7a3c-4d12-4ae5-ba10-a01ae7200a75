import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type DecisionLookupRes,
	decisionLookupReqSchema,
	decisionLookupResSchema
} from '../schemas/api/decision.schema';
import Decision from '../services/decision.service';

const decisionsV1Routes = new Elysia({ prefix: '/decisions' })
	.resolve(() => {
		return {
			Decision: new Decision(randomUUID())
		};
	})
	.post(
		'/lookup',
		async (ctx): Promise<DecisionLookupRes> => {
			return await ctx.Decision.lookupDecision(ctx.body);
		},
		{
			body: decisionLookupReqSchema,
			response: {
				200: decisionLookupResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description: 'Lookup Decision',
				tags: ['Temporal']
			}
		}
	);

export default decisionsV1Routes;
