import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getCache, setCache } from '../../../config/cache.config';
import { getDbInstance } from '../../../config/db.config';
import { envConfig } from '../../../config/env.config';
import { pinoLog } from '../../../config/pinoLog.config';
import { CacheKeyEnum } from '../../../enum/cacheKey.enum';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { IdTypeEnum } from '../../../enum/user.enum';
import { LightweightFlagEnum } from '../../../enum/wso2.enum';
import { UE_ERROR } from '../../../middleware/error';
import { getMyTimeZoneDate } from '../../../shared/common';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getApimToken } from '../helper/apimToken.helper';
import type { Wso2BodyReq } from '../helper/schemas/api/wso2Base.schema';
import type { Wso2AccountValidationRes } from './schemas/api/wso2AccountValidation.schema';
import type {
	Wso2AnnualBillStatementReq,
	Wso2AnnualBillStatementRes
} from './schemas/api/wso2AnnualBillStatement.schema';
import type { Wso2ConciseBARes } from './schemas/api/wso2ConciseBA.schema';
import type {
	Wso2ConciseCustInfoReq,
	Wso2ConciseCustInfoRes
} from './schemas/api/wso2ConciseCustInfo.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from './schemas/api/wso2CustomerAccount.schema';
import type { Wso2DMSReq, Wso2DMSRes } from './schemas/api/wso2DMS.schema';
import type {
	Wso2ExpDiscountReq,
	Wso2ExpDiscountRes
} from './schemas/api/wso2ExpDiscount.schema';
import type { Wso2LightWeightBillingDetailsRes } from './schemas/api/wso2LightweightBillingDetails.schema';
import type {
	Wso2NovaBillingProfileReq,
	Wso2NovaBillingProfileRes
} from './schemas/api/wso2NovaBillingProfile.schema';
import type {
	Wso2OutstandingAmountReq,
	Wso2OutstandingAmountRes
} from './schemas/api/wso2OutstandingAmount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from './schemas/api/wso2ServiceAccount.schema';
import type {
	Wso2UpdateBillingProfileReq,
	Wso2UpdateBillingProfileRes
} from './schemas/api/wso2UpdateBillingProfile.schema';
import type {
	Wso2GetIbillBillingDetailsReq,
	Wso2GetIbillBillingDetailsRes
} from './schemas/api/wso2iBillBillingDetails.schema';
import { wso2ConsumerAccountsTableSchema } from './schemas/db/wso2ConsumerAccounts.schema';
import { wso2ServiceAccountsTableSchema } from './schemas/db/wso2ServiceAccounts.schema';
import { wso2SmeAccountsTableSchema } from './schemas/db/wso2SmeAccounts.schema';

class Wso2UserIntegration {
	private integrationId: string;
	private db: NodePgDatabase;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
	}

	isExceededOneHour = (date: Date): boolean => {
		const oneHourAgo = getMyTimeZoneDate().getTime() - 60 * 60 * 1000; // Subtract 1 hour (in milliseconds)
		return date.getTime() < oneHourAgo; // True if date is older than 1 hour
	};

	/**
	 * @param [useCache=true] This flag is used to retrieve the cached data from redis. If the data is not found in the cache table, it will be fetched from the WSO2 API. For order submission case, you should retrieve the latest data from the WSO2 API, thus, the useCache flag should be set to false.
	 * @returns Wso2CustomerAccountRes
	 */
	async getWso2CustomerAccount(
		bodyRequest: Wso2CustomerAccountReq,
		lightWeightFlag: LightweightFlagEnum,
		useCache = true
	): Promise<Wso2CustomerAccountRes> {
		try {
			const cacheName = `${CacheKeyEnum.WSO2_CUSTOMER_ACCOUNTS}-${lightWeightFlag}-${bodyRequest.idValue}`;
			const cache = await getCache(cacheName).catch(() => null);
			if (cache && useCache) {
				return JSON.parse(cache) as Wso2CustomerAccountRes;
			}

			if (useCache) {
				// retrieve from db if not found in cache
				const wso2CustomerAcc = await this.getWso2CustomerAccountFromDb(
					bodyRequest.idType,
					bodyRequest.idValue,
					lightWeightFlag
				);

				if (
					(
						wso2CustomerAcc.Wso2CustomerAccountRes?.Response
							?.CustomerAccounts ?? []
					).length > 0
				) {
					// get the latest data from WSO2 for update db purpose
					if (
						wso2CustomerAcc.UpdatedAt &&
						this.isExceededOneHour(wso2CustomerAcc.UpdatedAt)
					) {
						this.getWso2CustomerAccountFromEnterprise(
							bodyRequest,
							lightWeightFlag
						);
					}

					return wso2CustomerAcc.Wso2CustomerAccountRes as Wso2CustomerAccountRes;
				}
			}

			// get the latest data from WSO2 for add to db purpose
			const wso2CustomerAccRes: Wso2CustomerAccountRes =
				await this.getWso2CustomerAccountFromEnterprise(
					bodyRequest,
					lightWeightFlag
				);

			return wso2CustomerAccRes;
		} catch (err) {
			throw new UE_ERROR(
				'Failed to get WSO2 customer account',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: err }
			);
		}
	}

	private async getWso2CustomerAccountFromEnterprise(
		bodyRequest: Wso2CustomerAccountReq,
		lightWeightFlag: string
	): Promise<Wso2CustomerAccountRes> {
		const cacheName = `${CacheKeyEnum.WSO2_CUSTOMER_ACCOUNTS}-${lightWeightFlag}-${bodyRequest.idValue}`;
		const url: string = envConfig().WSO2_RETRIEVE_CUSTOMER_ACCOUNTS;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				LightWeightFlag: lightWeightFlag,
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);

		if (!res.ok) {
			const wso2CustomerAcc = await this.getWso2CustomerAccountFromDb(
				bodyRequest.idType,
				bodyRequest.idValue,
				lightWeightFlag
			);

			return wso2CustomerAcc.Wso2CustomerAccountRes as Wso2CustomerAccountRes;
		}

		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		const parsedBody = resBody as Wso2CustomerAccountRes;

		if (
			parsedBody.Response?.CustomerAccounts &&
			parsedBody.Response.CustomerAccounts.length > 0
		) {
			// save to db for backup
			await this.insertWso2CustomerAccountToDb(
				bodyRequest.idType,
				bodyRequest.idValue,
				lightWeightFlag,
				parsedBody
			);
			await setCache(cacheName, JSON.stringify(resBody), 3600); // 1 hour
		}

		return parsedBody;
	}

	private async getWso2CustomerAccountFromDb(
		idType: string,
		idValue: string,
		lightweightFlag: string
	): Promise<{
		Wso2CustomerAccountRes: Wso2CustomerAccountRes | null;
		UpdatedAt: Date | null;
	}> {
		let wso2CustomerAccounts = null;

		if (idType === IdTypeEnum.BRN || idType === IdTypeEnum.NON_BRN) {
			wso2CustomerAccounts = await this.db
				.select()
				.from(wso2SmeAccountsTableSchema)
				.where(
					and(
						eq(wso2SmeAccountsTableSchema.IdType, idType),
						eq(wso2SmeAccountsTableSchema.IdValue, idValue)
					)
				)
				.catch(error => {
					throw new UE_ERROR(
						'Unable to retrieve SME Account from database',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: error }
					);
				});
		} else {
			wso2CustomerAccounts = await this.db
				.select()
				.from(wso2ConsumerAccountsTableSchema)
				.where(
					and(
						eq(wso2ConsumerAccountsTableSchema.IdType, idType),
						eq(wso2ConsumerAccountsTableSchema.IdValue, idValue)
					)
				)
				.catch(error => {
					throw new UE_ERROR(
						'Unable to retrieve Consumer Account from database',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: error }
					);
				});
		}

		if (wso2CustomerAccounts.length > 0) {
			return lightweightFlag === LightweightFlagEnum.YES
				? {
						Wso2CustomerAccountRes: wso2CustomerAccounts[0].LightweightData,
						UpdatedAt: wso2CustomerAccounts[0].UpdatedAt
					}
				: {
						Wso2CustomerAccountRes: wso2CustomerAccounts[0].NonLightweightData,
						UpdatedAt: wso2CustomerAccounts[0].UpdatedAt
					};
		}

		return {
			Wso2CustomerAccountRes: {
				Status: {},
				Response: {
					CustomerAccounts: []
				}
			},
			UpdatedAt: null
		};
	}

	private async insertWso2CustomerAccountToDb(
		idType: string,
		idValue: string,
		lightWeightFlag: string,
		resBody: Wso2CustomerAccountRes
	) {
		const insertOrUpdate = (
			table:
				| typeof wso2SmeAccountsTableSchema
				| typeof wso2ConsumerAccountsTableSchema,
			data: {
				IdType: string;
				IdValue: string;
				LightweightData?: Wso2CustomerAccountRes;
				NonLightweightData?: Wso2CustomerAccountRes;
			}
		) => {
			return this.db
				.insert(table)
				.values(data)
				.onConflictDoUpdate({
					target: [table.IdType, table.IdValue],
					set: {
						...data,
						UpdatedAt: getMyTimeZoneDate()
					}
				});
		};

		const getTable = (idType: string) => {
			return idType === IdTypeEnum.BRN || idType === IdTypeEnum.NON_BRN
				? wso2SmeAccountsTableSchema
				: wso2ConsumerAccountsTableSchema;
		};

		const data = {
			IdType: idType,
			IdValue: idValue,
			...(lightWeightFlag === LightweightFlagEnum.YES
				? { LightweightData: resBody }
				: { NonLightweightData: resBody })
		};

		await insertOrUpdate(getTable(idType), data);
	}

	/**
	 * @param bodyRequest Wso2ServiceAccountReq - The request payload for the WSO2 service account.
	 * @param lightWeightFlag LightweightFlagEnum - Indicates whether to use the lightweight version (YES or NO).
	 * @param [isEnabledErrorException=true] - If true, an error will be thrown when the WSO2 API returns an error. Set to false to suppress errors, especially useful for chained enterprise API calls.
	 * @param [useCache=true] - If true, attempts to fetch cached data from Redis. If not found, data is retrieved from the WSO2 API. Set to false (e.g., during order submission) to always fetch the latest data.
	 * @returns Wso2ServiceAccountRes
	 */

	async getWso2ServiceAccount(
		bodyRequest: Wso2ServiceAccountReq,
		lightWeightFlag: LightweightFlagEnum,
		isEnabledErrorException = true,
		useCache = true
	): Promise<Wso2ServiceAccountRes> {
		const cacheName = `${CacheKeyEnum.WSO2_SERVICE_ACCOUNTS}-${lightWeightFlag}-${bodyRequest.idValue}-${bodyRequest.BillingAccountNo}`;
		const cache = await getCache(cacheName).catch(() => null);
		if (cache && useCache) {
			return JSON.parse(cache) as Wso2ServiceAccountRes;
		}

		// retrieve from db if not found in cache
		const wso2ServiceAccounts = await this.getWso2ServiceAccountFromDb(
			bodyRequest.SystemName,
			bodyRequest.BillingAccountNo,
			lightWeightFlag
		);

		if (wso2ServiceAccounts.Wso2ServiceAccountRes) {
			// get the latest data from WSO2 for update db purpose
			if (
				wso2ServiceAccounts.UpdatedAt &&
				this.isExceededOneHour(wso2ServiceAccounts.UpdatedAt)
			) {
				this.getWso2ServiceAccountFromEnterprise(
					bodyRequest,
					lightWeightFlag,
					false,
					false
				);
			}
			return wso2ServiceAccounts.Wso2ServiceAccountRes as Wso2ServiceAccountRes;
		}

		// get the latest data from WSO2 for add to db purpose
		const wso2ServiceAccountRes =
			await this.getWso2ServiceAccountFromEnterprise(
				bodyRequest,
				lightWeightFlag,
				isEnabledErrorException,
				useCache
			);

		return wso2ServiceAccountRes;
	}

	private async getWso2ServiceAccountFromEnterprise(
		bodyRequest: Wso2ServiceAccountReq,
		lightWeightFlag: string,
		isEnabledErrorException = true,
		useCache = true
	): Promise<Wso2ServiceAccountRes> {
		const cacheName = `${CacheKeyEnum.WSO2_SERVICE_ACCOUNTS}-${lightWeightFlag}-${bodyRequest.idValue}-${bodyRequest.BillingAccountNo}`;
		const url: string = envConfig().WSO2_RETRIEVE_SERVICE_ACCOUNTS;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				LightWeightFlag: lightWeightFlag,
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException
		});

		if (!res.ok) {
			if (useCache) {
				const wso2ServiceAccounts = await this.getWso2ServiceAccountFromDb(
					bodyRequest.SystemName,
					bodyRequest.BillingAccountNo,
					lightWeightFlag
				);

				if (wso2ServiceAccounts.Wso2ServiceAccountRes) {
					return wso2ServiceAccounts.Wso2ServiceAccountRes as Wso2ServiceAccountRes;
				}
			}

			if (isEnabledErrorException)
				throw new UE_ERROR(
					'No data available in DB and WSO2 Service Account throw error',
					StatusCodeEnum.WSO2_ERROR,
					{
						integrationId: this.integrationId
					}
				);

			return null;
		}

		const resBody = await res.json().catch(error => {
			if (isEnabledErrorException)
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.WSO2_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			return null;
		});
		const parsedBody = resBody as Wso2ServiceAccountRes;

		await this.insertWso2ServiceAccountToDb(
			bodyRequest,
			lightWeightFlag,
			resBody
		);
		await setCache(cacheName, JSON.stringify(resBody), 3600); // 1 hour
		return parsedBody;
	}

	private async getWso2ServiceAccountFromDb(
		systemName: string,
		billingAccountNo: string,
		lightweightFlag: string
	): Promise<{
		Wso2ServiceAccountRes: Wso2ServiceAccountRes | null;
		UpdatedAt: Date | null;
	}> {
		const wso2ServiceAccounts = await this.db
			.select()
			.from(wso2ServiceAccountsTableSchema)
			.where(
				and(
					eq(wso2ServiceAccountsTableSchema.SystemName, systemName),
					eq(wso2ServiceAccountsTableSchema.BillingAccountNo, billingAccountNo)
				)
			)
			.catch(error => {
				pinoLog.error({ message: 'No data found in database', error });
				return [];
			});

		if (wso2ServiceAccounts.length > 0) {
			return lightweightFlag === LightweightFlagEnum.YES
				? {
						Wso2ServiceAccountRes: wso2ServiceAccounts[0].LightweightData,
						UpdatedAt: wso2ServiceAccounts[0].UpdatedAt
					}
				: {
						Wso2ServiceAccountRes: wso2ServiceAccounts[0].NonLightweightData,
						UpdatedAt: wso2ServiceAccounts[0].UpdatedAt
					};
		}

		return { Wso2ServiceAccountRes: null, UpdatedAt: null };
	}

	private async insertWso2ServiceAccountToDb(
		bodyRequest: Wso2ServiceAccountReq,
		lightWeightFlag: string,
		resBody: Wso2ServiceAccountRes
	) {
		if (lightWeightFlag === LightweightFlagEnum.YES) {
			await this.db
				.insert(wso2ServiceAccountsTableSchema)
				.values({
					SystemName: bodyRequest.SystemName,
					IdType: bodyRequest.idType,
					IdValue: bodyRequest.idValue,
					BillingAccountNo: bodyRequest.BillingAccountNo,
					LightweightData: resBody
				})
				.onConflictDoUpdate({
					target: [
						wso2ServiceAccountsTableSchema.BillingAccountNo,
						wso2ServiceAccountsTableSchema.SystemName
					],
					set: {
						LightweightData: resBody,
						UpdatedAt: getMyTimeZoneDate()
					}
				});
		} else {
			await this.db
				.insert(wso2ServiceAccountsTableSchema)
				.values({
					SystemName: bodyRequest.SystemName,
					IdType: bodyRequest.idType,
					IdValue: bodyRequest.idValue,
					BillingAccountNo: bodyRequest.BillingAccountNo,
					NonLightweightData: resBody
				})
				.onConflictDoUpdate({
					target: [
						wso2ServiceAccountsTableSchema.BillingAccountNo,
						wso2ServiceAccountsTableSchema.SystemName
					],
					set: {
						NonLightweightData: resBody,
						UpdatedAt: getMyTimeZoneDate()
					}
				});
		}
	}

	async getWso2LightWeightBillingDetails(
		bodyRequest: Wso2ServiceAccountReq,
		isEnabledErrorException = true,
		retries = 3
	): Promise<Wso2LightWeightBillingDetailsRes> {
		const cacheName = `${CacheKeyEnum.WSO2_LIGHTWEIGHT_BILLING_DETAILS}-${bodyRequest.BillingAccountNo}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2LightWeightBillingDetailsRes;
		}
		const url: string = envConfig().WSO2_RETRIEVE_BILLING_DETAILS;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE',
				LightWeightFlag: LightweightFlagEnum.YES
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException,
			retries
		});

		if (!res.ok) {
			if (isEnabledErrorException)
				throw new UE_ERROR(
					'Lightweight billing details throw error',
					StatusCodeEnum.WSO2_ERROR,
					{
						integrationId: this.integrationId
					}
				);

			return null;
		}

		const resBody = await res.json().catch(error => {
			if (isEnabledErrorException)
				throw new UE_ERROR(
					'Failed to parse JSON response',
					StatusCodeEnum.WSO2_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);

			return null;
		});
		await setCache(cacheName, JSON.stringify(resBody), 3600); // 60 minutes

		return resBody as Wso2LightWeightBillingDetailsRes;
	}

	async getWso2ConciseBADetails(accountNo: string): Promise<Wso2ConciseBARes> {
		const cacheName = `${CacheKeyEnum.WSO2_CONCISE_BA_DETAILS}-${accountNo}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2ConciseBARes;
		}
		let url: string = envConfig().WSO2_RETRIEVE_CONCISE_ACCOUNT_DETAILS;
		url = url.concat('?billingAccountNo=', accountNo);

		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			}
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'Concise BA details throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes
		return resBody as Wso2ConciseBARes;
	}

	async getWso2AnnualBillStatement(
		bodyRequest: Wso2AnnualBillStatementReq,
		isEnabledErrorException = true
	): Promise<Wso2AnnualBillStatementRes> {
		const url: string = envConfig().WSO2_PDF_ANNUAL_BILL_STATEMENT;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException
		});

		const resBody = await res.text();
		if (!res.ok) {
			if (isEnabledErrorException)
				throw new UE_ERROR(
					'Annual bill statement throw error',
					StatusCodeEnum.WSO2_ERROR,
					{
						integrationId: this.integrationId
					}
				);

			return null;
		}

		return JSON.parse(resBody) as Wso2AnnualBillStatementRes;
	}

	async getWso2BAVerification(
		accountNo: string
	): Promise<Wso2AccountValidationRes> {
		const url: string = envConfig().WSO2_RETRIEVE_BILL_LINK;

		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify({ BillingAccountNo: accountNo })
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'BA Verification throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2AccountValidationRes;
	}

	async getWso2BAVerificationByMonth(
		accountNo: string,
		month: string
	): Promise<Wso2AccountValidationRes> {
		const url: string = envConfig().WSO2_RETRIEVE_BILL_LINK;

		const token: string = await getApimToken();

		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify({ BillingAccountNo: accountNo, BillMonth: month })
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'BA Verification by month throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2AccountValidationRes;
	}

	async getWso2NovaBillingProfile(
		bodyRequest: Wso2NovaBillingProfileReq,
		isEnabledErrorException = true
	): Promise<Wso2NovaBillingProfileRes | null> {
		const cacheName = `${CacheKeyEnum.WSO2_NOVA_BILLING_PROFILE}-${bodyRequest.RetrieveBillingProfileRequest.BillingAccountNo}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2NovaBillingProfileRes;
		}
		const url: string = envConfig().WSO2_NOVA_BILLING_PROFILE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body, {
			isEnabledErrorException,
			retries: 1
		});
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2NovaBillingProfileRes;

		if (!res.ok || parsedBody.Status?.Code?.toLowerCase() === 'nok') {
			if (isEnabledErrorException)
				throw new UE_ERROR(
					'NOVA billing profile throw error',
					StatusCodeEnum.WSO2_ERROR,
					{
						integrationId: this.integrationId
					}
				);

			return null;
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes

		return parsedBody;
	}

	async getWso2OutstandingAmount(
		bodyRequest: Wso2OutstandingAmountReq
	): Promise<Wso2OutstandingAmountRes> {
		const url: string = envConfig().WSO2_OUTSTANDING_AMOUNT;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'Outstanding amount throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as Wso2OutstandingAmountRes;
	}

	async getWso2UpdateBillingProfile(
		bodyRequest: Wso2UpdateBillingProfileReq
	): Promise<Wso2UpdateBillingProfileRes> {
		const url: string = envConfig().WSO2_UPDATE_BILLING_PROFILE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		const parsedBody = resBody as Wso2UpdateBillingProfileRes;

		if (!res.ok || parsedBody.Status.Type === 'NOK') {
			throw new UE_ERROR(
				'Update billing profile throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return parsedBody;
	}

	async getWso2IbillBillingDetails(
		bodyRequest: Wso2GetIbillBillingDetailsReq
	): Promise<Wso2GetIbillBillingDetailsRes> {
		const url: string = envConfig().WSO2_IBILL_BILLING_DETAILS;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'BA Verification throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody as Wso2GetIbillBillingDetailsRes;
	}

	async getWso2ConciseCustInfo(
		bodyRequest: Wso2ConciseCustInfoReq
	): Promise<Wso2ConciseCustInfoRes> {
		const cacheName = `${CacheKeyEnum.WSO2_CONCISE_CUST_INFO}-${bodyRequest.kciRequest.serviceId}-${bodyRequest.kciRequest.account}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2ConciseCustInfoRes;
		}
		const url: string = envConfig().WSO2_CONCISE_CUST_INFO;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Concise Customer Info throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes
		return resBody as Wso2ConciseCustInfoRes;
	}

	async getWso2DmsCreditScore(bodyRequest: Wso2DMSReq): Promise<Wso2DMSRes> {
		const url: string = envConfig().WSO2_DMS_CREDIT_SCORE;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'DMS'
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR('DMS throw error', StatusCodeEnum.WSO2_ERROR, {
				integrationId: this.integrationId
			});
		}

		return resBody as Wso2DMSRes;
	}

	async getWso2ExpiryDiscount(
		bodyRequest: Wso2ExpDiscountReq
	): Promise<Wso2ExpDiscountRes> {
		const cacheName = `${CacheKeyEnum.WSO2_EXPIRY_DISCOUNT}-${bodyRequest.AccountNo}`;
		const cache = await getCache(cacheName);
		if (cache) {
			return JSON.parse(cache) as Wso2ExpDiscountRes;
		}
		const url: string = envConfig().WSO2_EXP_DISCOUNT;
		const token: string = await getApimToken();
		const body: Wso2BodyReq = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'Expiry Discount throw error',
				StatusCodeEnum.WSO2_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		await setCache(cacheName, JSON.stringify(resBody), 900); // 15 minutes
		return resBody as Wso2ExpDiscountRes;
	}
}

export default Wso2UserIntegration;
