import { type Static, t } from 'elysia';
import { StateEnum } from '../../../../../enum/address.enum';
import { bizCCRegistrationResSchema } from '../../../../../integration/biz/schemas/api/cloudConnect.schema';
import { bizECommerceRegistrationResSchema } from '../../../../../integration/biz/schemas/api/eCommerce.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const cloudConnectRegistrationReqSchema = t.Object({
	PackageName: t.String({ examples: ['Enterprise'] }),
	PlanName: t.String({ examples: ['Unifi Cloud Storage - RM94/mth'] }),
	MonthlyFee: t.Number(),
	TenantName: t.String({ examples: ['Test Company.Sdn.Bhd'] }),
	ContactIdNumber: t.Optional(t.String()),
	TenantPhoneNumber: t.String(),
	AdminEmail: t.String({ examples: ['<EMAIL>'] }),
	FirstName: t.String({ examples: ['Lily'] }),
	LastName: t.String({ examples: ['Cahill'] }),
	CompanyOwnerName: t.String({ examples: ['Enterprise'] }),
	TenantRegistrationNumber: t.String({ examples: ['2-3AGE4985'] }),
	TenantTierName: t.String({ examples: ['Retail'] }),
	DomainName: t.String({ examples: ['testCompany'] }),
	CustomerIDType: t.String({ examples: ['Company wihtout BRN'] }),
	SegmentGroup: t.String({ examples: ['SME'] }),
	SegmentSubGroup: t.String({ examples: ['Business_Entity'] }),
	IndustryCode: t.String({ examples: ['RET'] }),
	RelationshipCode: t.String({ examples: ['External'] }),
	ContractingParty: t.String({ examples: ['Inside_Malaysia'] }),
	ForeignTelco: t.String({ examples: ['No', 'Yes'] }),
	PreferredLanguage: t.String({ examples: ['English'] }),
	BillEmailAddress: t.String({ examples: ['<EMAIL>'] }),
	AddressType: t.String({ examples: ['Landed', 'High-Rise'] }),
	UnitNumber: t.String({ examples: ['1'] }),
	FloorNumber: t.String({ examples: ['1'] }),
	Block: t.String({ examples: ['Block A'] }),
	BuildingName: t.String({ examples: ['Anggun'] }),
	StreetName: t.String({ examples: ['Anggun 3'] }),
	StreetType: t.String({ examples: ['JALAN'] }),
	Section: t.String({ examples: ['Seksyen 5'] }),
	PostCode: t.String({ examples: ['40000'] }),
	City: t.String({ examples: ['Shah Alam'] }),
	State: t.Enum(StateEnum),
	Country: t.String({ examples: ['Malaysia'] }),
	ServiceId: t.String({ examples: ['test@unifi'] }),
	CommissionToId: t.String({ examples: ['A234'] }),
	CommissionToName: t.String({ examples: ['Test Company'] })
});
export type CloudConnectRegistrationReq = Static<
	typeof cloudConnectRegistrationReqSchema
>;

export const cloudConnectRegistrationResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: bizCCRegistrationResSchema
	},
	{
		description: 'Cloud Connect registration successfully submitted.'
	}
);

export type CloudConnectRegistrationRes = Static<
	typeof cloudConnectRegistrationResSchema
>;

export const eCommerceRegistrationReqSchema = t.Object({
	CompanyName: t.String({ examples: ['Company A'] }),
	CustomerIdType: t.String({ examples: ['1'] }),
	OwnerName: t.Optional(t.String({ example: 'John Doe' })),
	Block: t.Optional(t.String()),
	EmailStreetType: t.Optional(t.String()),
	SegmentSubGroup: t.Number(),
	IndustryCode: t.Number(),
	RelationshipCode: t.Number(),
	ContractingParty: t.Number(),
	ForeignTelco: t.Number(),
	CustomerIdNumber: t.String({ examples: ['*********-X'] }),
	FirstName: t.String({ examples: ['Lily'] }),
	LastName: t.String({ examples: ['Cahill'] }),
	ContactIdNumber: t.Optional(t.String({ example: '' })),
	PreferredLanguage: t.Number(),
	MobilePhone: t.String({ examples: ['***********'] }),
	BillEmailAddress: t.String({ examples: ['<EMAIL>'] }),
	AddressType: t.Number(),
	UnitNumber: t.String({ examples: ['123'] }),
	FloorNumber: t.String({ examples: ['1'] }),
	BuildingName: t.String({ examples: ['Anggun'] }),
	StreetType: t.String({ examples: ['1'] }),
	StreetName: t.String({ examples: ['Anggun 3/5E'] }),
	Section: t.String({ examples: ['Seksyen 5'] }),
	PostCode: t.String({ examples: ['40000'] }),
	City: t.String({ examples: ['Shah Alam'] }),
	State: t.Enum(StateEnum),
	Country: t.Number(),
	TrainingPlan: t.String({ example: '' }),
	ExistingTmCustomer: t.Number(),
	Waiver: t.Number(),
	CommissionToId: t.Optional(t.String({ examples: ['yyy'] })),
	CommissionToName: t.Optional(t.String({ examples: ['Test Company'] })),
	PackCode: t.String({ examples: ['FREEMIUM'] }),
	BusinessModel: t.Number(),
	ServiceId: t.String({ examples: ['<EMAIL>'] }),
	Email: t.String({ examples: ['<EMAIL>'] }),
	Telephone: t.String({ examples: ['***********'] }),
	OrderNumber: t.String({ examples: ['UCSB-1FD124WF'] }),
	PlanName: t.String({ examples: ['Freemium Pro RM100/month'] }),
	MonthlyFee: t.Number()
});
export type ECommerceRegistrationReq = Static<
	typeof eCommerceRegistrationReqSchema
>;

export const eCommerceRegistrationResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: bizECommerceRegistrationResSchema
	},
	{
		description: 'eCommerce registration successfully submitted.'
	}
);

export type ECommerceRegistrationRes = Static<
	typeof eCommerceRegistrationResSchema
>;

const smeEmailSchema = t.Object({
	RefNumber: t.String(),
	PlanName: t.String(),
	MonthlyFee: t.String(),
	CompanyName: t.String(),
	OwnerName: t.String(),
	PicName: t.String(),
	PicIdNumber: t.String(),
	SubDomain: t.String(),
	ContactNumber: t.String(),
	IdNumber: t.String(),
	IdType: t.String(),
	EmailAddress: t.String(),
	UnitNo: t.String(),
	FloorNo: t.String(),
	Block: t.String(),
	Building: t.String(),
	StreetType: t.String(),
	StreetName: t.String(),
	Section: t.String(),
	PostCode: t.String(),
	City: t.String(),
	State: t.String(),
	Country: t.String()
});
export type SmeEmail = Static<typeof smeEmailSchema>;

const creditUtilizationSchema = t.Object({
	UtilId: t.MaybeEmpty(t.String()),
	Item: t.MaybeEmpty(t.String()),
	AdCreditUtilized: t.MaybeEmpty(t.String()),
	FreeAdCreditUsed: t.MaybeEmpty(t.String()),
	PaidAdCreditUsed: t.MaybeEmpty(t.String()),
	Date: t.MaybeEmpty(t.String()),
	UtilizationStatus: t.MaybeEmpty(t.String())
});

export type CreditUtilization = Static<typeof creditUtilizationSchema>;

const creditPurchaseSchema = t.Object({
	Product: t.MaybeEmpty(t.String()),
	PurchaseId: t.MaybeEmpty(t.String()),
	AdCreditAmount: t.MaybeEmpty(t.String()),
	PurchaseDate: t.MaybeEmpty(t.String()),
	ExpiryDate: t.MaybeEmpty(t.String()),
	Balance: t.MaybeEmpty(t.String()),
	Type: t.MaybeEmpty(t.String()),
	OrderNumber: t.MaybeEmpty(t.String())
});

export type CreditPurchase = Static<typeof creditPurchaseSchema>;

export const dmsCreditScoreReqSchema = t.Object({
	ServiceId: t.String({ example: 'test@unifi', minLength: 1 })
});
export type DmsCreditScoreReq = Static<typeof dmsCreditScoreReqSchema>;

export const dmsCreditScoreResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			ProductName: t.String(),
			Service: t.Object({
				DmsServiceId: t.String(),
				ServiceStatus: t.String(),
				ServiceStartDate: t.String(),
				ServiceEndDate: t.String(),
				CampaignManagerProfile: t.Object({
					FirstName: t.String(),
					LastName: t.String(),
					Email: t.String()
				}),
				AdCreditUtilization: t.Array(t.MaybeEmpty(creditUtilizationSchema)),
				AdCreditPurchase: t.Array(t.MaybeEmpty(creditPurchaseSchema))
			})
		})
	},
	{
		description: 'DMS credit score successfully retrieved.'
	}
);

export type DmsCreditScoreRes = Static<typeof dmsCreditScoreResSchema>;
