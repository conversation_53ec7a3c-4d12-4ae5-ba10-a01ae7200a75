import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

export const nonOrderableJSONDataSchema = t.Any();

export type NonOrderableJSONData = Static<typeof nonOrderableJSONDataSchema>;

export const nonOrderableTxnHistoryTableSchema = pgTable(
	'non_orderable_txn_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		OrderId: integer('order_id').generatedByDefaultAsIdentity({
			startWith: 1000
		}),
		OrderPrefix: text('order_prefix').notNull(),
		BillingAccountNo: text('billing_account_no').notNull(),
		IdType: text('id_type').notNull(),
		IdValue: text('id_value').notNull(),
		FullName: text('full_name').notNull(),
		Email: text('email').notNull(),
		MobileNo: text('mobile_no').notNull(),
		ServiceId: text('service_id').notNull(),
		Category: text('category').notNull(),
		ProductName: text('product_name').notNull(),
		SystemName: text('system_name').notNull(),
		Source: text('source').notNull(),
		Segment: text('segment').notNull(),
		OrderStatus: text('order_status').notNull(),
		OrderData: json('order_data').$type<NonOrderableJSONData>(),
		SubOrderData: json('sub_order_data').$type<NonOrderableJSONData>(),
		ErrorMessage: text('error_message'),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	}
);

export type SelectNonOrderableTxnHistory =
	typeof nonOrderableTxnHistoryTableSchema.$inferSelect;
