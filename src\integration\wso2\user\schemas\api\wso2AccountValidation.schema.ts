import { type Static, t } from 'elysia';

export const wso2AccountValidationResSchema = t.Object({
	Status: t.Object({
		Type: t.String(),
		Code: t.String(),
		Message: t.Nullable(t.String())
	}),
	Response: t.Object({
		SystemName: t.Optional(t.String()),
		CustomerIdType: t.Optional(t.String()),
		CustomerIdNumber: t.Optional(t.String()),
		AmountToBePaid: t.Optional(t.String()),
		BillMonth: t.Optional(t.String()),
		BillNo: t.Optional(t.String()),
		BillName: t.Optional(t.String()),
		BillDueDate: t.Optional(t.String()),
		TotalCurrent: t.Optional(t.String()),
		OverduePayment: t.Optional(t.String()),
		TotalDue: t.Optional(t.String()),
		TotalOutstanding: t.Optional(t.String()),
		TotalPreviousPayment: t.Optional(t.String()),
		TotalPrevious: t.Optional(t.String()),
		TotalPrevAdj: t.Optional(t.String()),
		RetrieveBillURLResponse: t.Object({
			AccountNo: t.String(),
			AccountStatus: t.Optional(t.String()),
			Invoice: t.MaybeEmpty(t.Array(t.MaybeEmpty(t.Object({}))))
		})
	})
});

export type Wso2AccountValidationRes = Static<
	typeof wso2AccountValidationResSchema
>;
