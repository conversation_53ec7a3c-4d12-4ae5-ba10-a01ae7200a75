import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type CreateTroikaRes,
	type UpdateTroikaNbaRes,
	createTroikaReqSchema,
	createTroikaResSchema,
	queryTroikaDemandStatusReqSchema,
	updateTroikaNbaReqSchema,
	updateTroikaReqSchema,
	updateTroikaResNbaSchema
} from '../schemas/api/demand.schema';
import Demand from '../services/demand.service';

const demandV1Routes = new Elysia({ prefix: '/demand' })
	.resolve(() => ({
		Demand: new Demand(randomUUID())
	}))
	.post(
		'/troika',
		async (ctx): Promise<CreateTroikaRes> => {
			return await ctx.Demand.createTroikaTicket(ctx.body);
		},
		{
			body: createTroikaReqSchema,
			response: {
				200: createTroikaResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Create Troika Demand Ticket <br><br> <b>Target System:</b> Troika',
				tags: ['Temporal']
			}
		}
	)
	.put(
		'/troika',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.Demand.updateTroikaTicket(ctx.body);
		},
		{
			body: updateTroikaReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Update Troika Demand Ticket <br><br> <b>Target System:</b> Troika',
				tags: ['Temporal']
			}
		}
	)
	.put(
		'/troika/nba',
		async (ctx): Promise<UpdateTroikaNbaRes> => {
			return await ctx.Demand.troikaNbaUpdate(ctx.body);
		},
		{
			body: updateTroikaNbaReqSchema,
			response: {
				200: updateTroikaResNbaSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Update Troika Demand <br><br> <b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			}
		}
	)
	.get(
		'/troika/query-troika-demand-status',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.Demand.queryTroikaDemandStatus(ctx.body);
		},
		{
			body: queryTroikaDemandStatusReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Query Troika Demand Status <br><br> <b>Target System:</b> TROIKA',
				tags: ['Temporal']
			}
		}
	);

export default demandV1Routes;
