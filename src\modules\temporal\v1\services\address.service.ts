import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2GetAddressByIDReq,
	Wso2GetAddressByIDRes,
	Wso2GraniteAddressDetails,
	Wso2GraniteAddressRes
} from '../../../../integration/wso2/address/schema/api/wso2GraniteAddress.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { SelectOrderableTxnHistory } from '../../../order/v1/schemas/db/orderable.schema';
import type {
	AddressCheckNigRes,
	GetGraniteAddressByKeywordAndStateReq
} from '../schemas/api/address.schema';
import Order from './order.service';

class Address {
	private integrationId: string;
	private mwIntegaration: MwIntegration;
	private order: Order;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegaration = new MwIntegration(integrationId);
		this.order = new Order(this.integrationId);
	}

	async addressCheckNig(orderId: string): Promise<AddressCheckNigRes> {
		let response = null;
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(orderId);
		if (!orderTxn) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.BAD_REQUEST_ERROR);
		}
		if (orderTxn.OrderStatus === 'CANCEL') {
			throw new UE_ERROR(
				'Order is Cancelled',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (orderTxn.OrderData.ReadNig) {
			response = orderTxn.OrderData.ReadNig;
		} else {
			const addressId = orderTxn.Address?.AddressId ?? null;
			if (addressId === null || addressId === '') {
				throw new UE_ERROR(
					'Address ID Missing',
					StatusCodeEnum.BAD_REQUEST_ERROR,
					{ integrationId: this.integrationId }
				);
			}

			const addressByIdRes = await this.getGraniteAddressByAddressId(addressId);
			if (addressByIdRes?.Response) {
				response = addressByIdRes.Response;
			} else {
				const lat =
					orderTxn.Address?.Latitude ??
					orderTxn.OrderData?.SmartMapInstallationAddress?.Location?.Lat ??
					'';
				const lon =
					orderTxn.Address?.Longitude ??
					orderTxn.OrderData?.SmartMapInstallationAddress?.Location?.Lon ??
					'';

				const addressByCoordinateRes = await this.getGraniteAddressByCoordinate(
					lat,
					lon,
					addressId
				);

				if (addressByCoordinateRes?.Response) {
					response = addressByCoordinateRes.Response;
				} else {
					const addressByKeywordStateRes =
						await this.getGraniteAddressByKeywordState({
							Unit: orderTxn.Address?.UnitNo ?? '',
							Building: orderTxn.Address?.BuildingName ?? '',
							Street: orderTxn.Address?.StreetName ?? '',
							State: orderTxn.Address?.State ?? '',
							Postcode: orderTxn.Address?.Postcode ?? '',
							AddressId: addressId
						});
					if (addressByKeywordStateRes?.Response) {
						response = addressByKeywordStateRes.Response;
					}
				}
			}
			if (response) {
				orderTxn.OrderData.ReadNig = response;
				await this.order.updateOrderableTxn(orderTxn);
			}
		}
		if (response) {
			return {
				Success: true,
				IntegrationId: this.integrationId,
				Code: StatusCodeEnum.ACCEPTED,
				Response: response as Wso2GraniteAddressDetails
			};
		}

		throw new UE_ERROR(
			'Failed to get Granite Address',
			StatusCodeEnum.UNKNOWN_ERROR,
			{ integrationId: this.integrationId }
		);
	}

	async getGraniteAddressByAddressId(
		addressId: string
	): Promise<AddressCheckNigRes> {
		try {
			const wso2GraniteAddressByAddressId: Wso2GetAddressByIDReq = {
				AddressId: addressId
			};

			const wso2GraniteAddressByAddressIdRes: Wso2GetAddressByIDRes =
				await this.mwIntegaration.Wso2AddressIntegration.getGraniteAddressByAddressId(
					wso2GraniteAddressByAddressId
				);

			return {
				Success: true,
				IntegrationId: this.integrationId,
				Code: StatusCodeEnum.OK,
				Response: wso2GraniteAddressByAddressIdRes.addressDetails[0]
			};
		} catch (error: unknown) {
			throw new UE_ERROR(
				'Failed to get Granite Address By Address Id',
				StatusCodeEnum.UNKNOWN_ERROR,
				{
					integrationId: this.integrationId,
					response: error
				}
			);
		}
	}
	async getGraniteAddressByCoordinate(
		latitude: string,
		longitude: string,
		addressId: string
	): Promise<AddressCheckNigRes> {
		try {
			let resultAddressObj = null;

			const queryByCoordinate: Wso2GraniteAddressRes =
				await this.mwIntegaration.Wso2AddressIntegration.getGraniteAddressByCoordinates(
					{
						lat: latitude,
						lng: longitude
					}
				);

			resultAddressObj = this.findAddressById(
				queryByCoordinate.data,
				addressId
			);

			return {
				Success: !!resultAddressObj,
				IntegrationId: this.integrationId,
				Code: resultAddressObj ? 200 : StatusCodeEnum.UNKNOWN_ERROR,
				Response: resultAddressObj as Wso2GraniteAddressDetails
			};
		} catch (error: unknown) {
			throw new UE_ERROR(
				'Failed to get Granite Address By Coordinate',
				StatusCodeEnum.UNKNOWN_ERROR,
				{
					integrationId: this.integrationId,
					response: error
				}
			);
		}
	}

	async getGraniteAddressByKeywordState(
		address: GetGraniteAddressByKeywordAndStateReq
	): Promise<AddressCheckNigRes> {
		try {
			let resultAddressObj = null;

			const {
				Unit: installationAddressUnit,
				Building: installationAddressBuilding,
				Street: installationAddressStreetName,
				Postcode: installationAddressPostCode,
				AddressId: addressId,
				State: state
			} = address;

			if (addressId) {
				const keywords: string[] = [];

				if (
					installationAddressBuilding &&
					installationAddressBuilding !== '-'
				) {
					keywords.push(
						[
							installationAddressUnit,
							installationAddressBuilding,
							installationAddressStreetName,
							installationAddressPostCode
						]
							.filter(Boolean)
							.join(', ')
					);
					keywords.push(
						[
							installationAddressBuilding,
							installationAddressStreetName,
							installationAddressPostCode
						]
							.filter(Boolean)
							.join(', ')
					);
					keywords.push(
						[installationAddressBuilding, installationAddressPostCode]
							.filter(Boolean)
							.join(', ')
					);
					keywords.push(
						[installationAddressBuilding, installationAddressStreetName]
							.filter(Boolean)
							.join(', ')
					);
				} else if (installationAddressUnit && installationAddressUnit !== '-') {
					keywords.push(
						[
							installationAddressUnit,
							installationAddressStreetName,
							installationAddressPostCode
						]
							.filter(Boolean)
							.join(', ')
					);
					keywords.push(
						[installationAddressUnit, installationAddressStreetName]
							.filter(Boolean)
							.join(', ')
					);
					keywords.push(
						[installationAddressUnit, installationAddressPostCode]
							.filter(Boolean)
							.join(', ')
					);
				}

				keywords.push(
					`${installationAddressStreetName}, ${installationAddressPostCode}`
				);

				for (const keyword of keywords) {
					const queryByKeyword: Wso2GraniteAddressRes =
						await this.mwIntegaration.Wso2AddressIntegration.getGraniteAddressByKeywordAndState(
							{
								query: keyword,
								state
							}
						);

					resultAddressObj = this.findAddressById(
						queryByKeyword.data,
						addressId
					);
					if (resultAddressObj) {
						break;
					}
				}
			}

			return {
				Success: !!resultAddressObj,
				IntegrationId: this.integrationId,
				Code: resultAddressObj ? 200 : StatusCodeEnum.UNKNOWN_ERROR,
				Response: resultAddressObj as Wso2GraniteAddressDetails
			};
		} catch (error: unknown) {
			console.log(error);
			throw new UE_ERROR(
				'Failed to get Granite Address By Keyword and State',
				StatusCodeEnum.UNKNOWN_ERROR,
				{
					integrationId: this.integrationId,
					response: error
				}
			);
		}
	}

	findAddressById = (
		addressObj: Wso2GraniteAddressDetails[],
		id: string
	): Wso2GraniteAddressDetails | null => {
		const formattedId = id.replace('G', '');
		let result = null;
		for (const addObj of addressObj) {
			const address_id = String(addObj.RESOURCE_INST_ID) ?? '';
			if (address_id?.includes(formattedId)) {
				if (addObj.ADDR_SERVICE_CATEGORY === 'FTTH') {
					return addObj;
				}
				result = addObj;
			}
		}
		return result;
	};
}

export default Address;
