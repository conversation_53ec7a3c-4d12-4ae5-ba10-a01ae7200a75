import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const createSlofOrderPreLoginResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Record(t.String(), t.String())
	},
	{
		description: 'SLOF Order Details retrieved.'
	}
);

export type CreateSlofOrderPreLoginRes = Static<
	typeof createSlofOrderPreLoginResSchema
>;

export const createSlofOrderPreLoginReqSchema = t.Object({
	OrderPrefix: t.String(),
	BillingAccountNo: t.Optional(t.Nullable(t.String())),
	IdType: t.Optional(t.String()),
	IdValue: t.Optional(t.String()),
	FullName: t.Optional(t.Nullable(t.String())),
	Email: t.Optional(t.Nullable(t.String())),
	MobileNo: t.Optional(t.Nullable(t.String())),
	ServiceId: t.Optional(t.Nullable(t.String())),
	Category: t.String(),
	ProductName: t.Optional(t.Nullable(t.String())),
	SystemName: t.String(),
	Source: t.Optional(t.Nullable(t.String())),
	Segment: t.Optional(t.Nullable(t.String())),
	OrderData: t.Any(), // Any type for order data as it is dynamic
	SubOrderData: t.Optional(t.Nullable(t.Any())) // Any type for sub-order data as it is dynamic
});

export type CreateSlofOrderPreLoginReq = Static<
	typeof createSlofOrderPreLoginReqSchema
>;

export const createSlofOrderPostLoginResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Record(t.String(), t.String())
	},
	{
		description: 'SLOF Order Details retrieved.'
	}
);

export type CreateSlofOrderPostLoginRes = Static<
	typeof createSlofOrderPostLoginResSchema
>;

export const createSlofOrderPostLoginReqSchema = t.Object({
	OrderPrefix: t.String(),
	BillingAccountNo: t.String(),
	FullName: t.Optional(t.Nullable(t.String())),
	Email: t.Optional(t.Nullable(t.String())),
	MobileNo: t.Optional(t.Nullable(t.String())),
	ServiceId: t.String(),
	Category: t.String(),
	ProductName: t.Optional(t.Nullable(t.String())),
	SystemName: t.String(),
	Source: t.Optional(t.Nullable(t.String())),
	Segment: t.Optional(t.Nullable(t.String())),
	OrderData: t.Any(), // Any type for order data as it is dynamic
	SubOrderData: t.Optional(t.Nullable(t.Any())) // Any type for sub-order data as it is dynamic
});

export type CreateSlofOrderPostLoginReq = Static<
	typeof createSlofOrderPostLoginReqSchema
>;
