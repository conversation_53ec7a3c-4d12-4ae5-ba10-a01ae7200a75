import { type Static, t } from 'elysia';

/**
 * Nested schema: fulfilment > item
 */
export const werasTransactionItemSchema = t.Object({
	id: t.Number(),
	name: t.String(),
	description: t.String(),
	category: t.Number(),
	brand: t.Number(),
	inventory_channel: t.String(),
	redemption_flow: t.String(),
	redemption_scope: t.String(),
	game_flag: t.Number(),
	status: t.String(),
	segment: t.String(),
	additional_description: t.Nullable(t.String()),
	rm_value: t.Nullable(t.Number()),
	point_value: t.Nullable(t.Number()),
	code_value: t.Nullable(t.String()),
	start_date: t.String(),
	end_date: t.String(),
	expiry_date: t.Nullable(t.String()),
	image: t.String(),
	csvfile: t.Nullable(t.String()),
	fast_track: t.Number(),
	tnc: t.String(),
	download_url: t.Nullable(t.String()),
	highlighted: t.Number(),
	exclude: t.Number(),
	barcode: t.String(),
	qrcode: t.String(),
	display: t.String(),
	created_by: t.Number(),
	modified_by: t.Number(),
	deleted_by: t.Nullable(t.Number()),
	created_at: t.String(),
	updated_at: t.String(),
	deleted_at: t.Nullable(t.String()),
	barcode_link: t.Nullable(t.String()),
	qr_code_link: t.Nullable(t.String())
});

/**
 * Nested schema: fulfilment
 */
export const werasTransactionFulfilmentSchema = t.Object({
	id: t.Number(),
	redemption_id: t.Number(),
	event_id: t.Nullable(t.Number()),
	item_id: t.Number(),
	item_quantity: t.Number(),
	points_redeemed: t.Number(),
	voucher_code_id: t.Number(),
	status: t.String(),
	address_1: t.Nullable(t.String()),
	address_2: t.Nullable(t.String()),
	address_3: t.Nullable(t.String()),
	city: t.Nullable(t.String()),
	state: t.Nullable(t.String()),
	country: t.Nullable(t.String()),
	postal_code: t.Nullable(t.String()),
	contact_number: t.Nullable(t.String()),
	shipped_at: t.Nullable(t.String()),
	shipped_by: t.Nullable(t.String()),
	tracking_number: t.Nullable(t.String()),
	tracking_url: t.Nullable(t.String()),
	created_by: t.Number(),
	modified_by: t.Number(),
	created_at: t.String(),
	updated_at: t.String(),
	item: werasTransactionItemSchema
});

/**
 * Data in transaction.data[]
 */
export const werasTransactionRecordSchema = t.Object({
	name: t.String(),
	description: t.String(),
	fulfilment_id: t.Number(),
	redemption_id: t.Number(),
	status: t.String(),
	created_at: t.String(),
	expiry_at: t.String(),
	payment: t.Nullable(t.Unknown()),
	fulfilment: werasTransactionFulfilmentSchema
});

/**
 * Schema for `data.transaction` in response
 */
export const werasTransactionListSchema = t.Object({
	current_page: t.String(),
	data: t.Array(werasTransactionRecordSchema),
	first_page_url: t.String(),
	from: t.String(),
	last_page: t.String(),
	last_page_url: t.String(),
	next_page_url: t.Nullable(t.String()),
	path: t.String(),
	per_page: t.String(),
	prev_page_url: t.Nullable(t.String()),
	to: t.String(),
	total: t.String()
});

/**
 * Schema for `data` in WerasGetTransactionsResponse
 */
export const werasGetTransactionsDataSchema = t.Object({
	error: t.Optional(t.String()),
	message: t.Optional(t.String()),
	transaction: werasTransactionListSchema
});

/**
 * Top-level WerasGetTransactionsResponse
 */
export const werasGetTransactionsResSchema = t.Object({
	status: t.Boolean(),
	code: t.Number(),
	data: werasGetTransactionsDataSchema
});

export type WerasGetTransactionsRes = Static<
	typeof werasGetTransactionsResSchema
>;
