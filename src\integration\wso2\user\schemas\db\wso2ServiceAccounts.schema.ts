import {
	integer,
	json,
	pgTable,
	text,
	timestamp,
	uniqueIndex
} from 'drizzle-orm/pg-core';
import type { Wso2ServiceAccountRes } from '../api/wso2ServiceAccount.schema';

export const wso2ServiceAccountsTableSchema = pgTable(
	'wso2_service_accounts',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		IdType: text('id_type').notNull(),
		IdValue: text('id_value').notNull(),
		SystemName: text('system_name').notNull(),
		BillingAccountNo: text('billing_account_no').notNull(),
		LightweightData: json('lightweight_data').$type<Wso2ServiceAccountRes>(),
		NonLightweightData: json(
			'non_lightweight_data'
		).$type<Wso2ServiceAccountRes>(),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	},
	table => [
		uniqueIndex('serviceAccountIdUniqueIndex').on(
			table.SystemName,
			table.BillingAccountNo
		)
	]
);
