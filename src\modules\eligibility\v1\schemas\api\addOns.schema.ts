import { type Static, t } from 'elysia';
import { AddOnsRequestCategoryEnum } from '../../../../../enum/addOns.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const addOnsEligibilityReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: ['fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-']
	}),
	Category: t.Enum(AddOnsRequestCategoryEnum),
	ProductName: t.String({
		examples: ['TP-Link Smart Hub with Chime', 'unifi Plus Box']
	}),
	Otts: t.Array(
		t.Object({
			OttMerchantId: t.Number({ examples: [38, 25] }),
			OttName: t.String({
				examples: [
					'Disney+ Hotstar Monthly (Premium)',
					'TVBAnywhere+ Channel Zone'
				]
			})
		}),
		{
			description:
				'List of OTT merchant IDs. This is used to determine the eligibility for OTT add-ons.'
		}
	)
});

export type AddOnsEligibilityReq = Static<typeof addOnsEligibilityReqSchema>;

export const addOnsEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsEligible: t.Boolean(),
			EligibleQuantity: t.Optional(t.Number()),
			Title: t.Nullable(t.String()),
			Reason: t.Nullable(t.String()),
			ReasonCode: t.Nullable(t.String()),
			CTAButtonText: t.Nullable(t.String()),
			CTAUrl: t.Nullable(t.String()),
			TransactionId: t.Optional(t.Number()),
			EligibleOtt: t.Optional(
				t.Object({
					OttPlanId: t.String(),
					Otts: t.Array(
						t.Object({
							OttName: t.String(),
							OttPrice: t.Number(),
							OttMerchantId: t.Number(),
							OttProductId: t.String(),
							OttOmgId: t.Number(),
							OttLoginType: t.String(),
							OttIconPath: t.String()
						})
					)
				})
			)
		})
	},
	{
		description:
			"Customer's add ons subscription eligibility successfully retrieved."
	}
);

export type AddOnsEligibilityRes = Static<typeof addOnsEligibilityResSchema>;

const reportingParams = t.Object({
	category: t.Enum(AddOnsRequestCategoryEnum),
	serviceId: t.String(),
	decryptedBillAccNo: t.String(),
	serviceStartDate: t.Nullable(t.String()),
	cbpr: t.Nullable(t.String())
});

export type ReportingParams = Static<typeof reportingParams>;
