import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';

import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';

import {
	temporalNotificationMediumParamSchema,
	temporalNotificationRequestSchema
} from '../schemas/api/notification.schema';

import NotificationService from '../services/notification.service';

const notificationV1Routes = new Elysia({ prefix: '/notification' })
	.resolve(() => ({
		NotificationService: new NotificationService(randomUUID())
	}))
	.post(
		'/:channel',
		async (ctx): Promise<BaseResponse> => {
			const { channel } = ctx.params;
			return ctx.NotificationService.dispatchNotification(channel, ctx.body);
		},
		{
			body: temporalNotificationRequestSchema,
			params: temporalNotificationMediumParamSchema,
			response: {
				200: baseResponseSchema,
				404: baseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description: `Trigger a Netcore notification via the selected medium.
- Use 'email' to send transactional email immediately
- Use 'activity' to send event to Netcore CE to trigger email via campaign rules`,
				tags: ['Temporal']
			}
		}
	);

export default notificationV1Routes;
