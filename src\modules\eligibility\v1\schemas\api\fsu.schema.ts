import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const fsuEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsEligibleForUpgrade: t.<PERSON>(),
			EligibilityMessage: t.Nullable(
				t.String({ examples: ['Eligible for FSU'] })
			),
			EligibilitySpeed: t.Nullable(t.String({ examples: ['100Mbps'] }))
		})
	},
	{ description: "Customer's FSU eligibility successfully retrieved." }
);

export type FsuEligibilityRes = Static<typeof fsuEligibilityResSchema>;
