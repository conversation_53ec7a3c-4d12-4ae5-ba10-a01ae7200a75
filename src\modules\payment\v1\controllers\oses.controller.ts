import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type OsesHistoryRes,
	osesHistoryReqSchema,
	osesHistoryResSchema
} from '../schemas/api/osesHistory.schema';
import {
	type OsesStatusRes,
	osesStatusReqSchema,
	osesStatusResSchema
} from '../schemas/api/osesStatus.schema';
import Oses from '../services/oses.service';

export const osesV1Routes = new Elysia({ prefix: '/oses' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			Oses: new Oses(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/history',
		async (ctx): Promise<OsesHistoryRes> => {
			return await ctx.Oses.getPaymentHistory(ctx.query);
		},
		{
			detail: {
				description:
					"Get a customer's OSES payment transaction history. <br><br> <b>Table:</b> oses_txn_history",
				tags: ['Payment']
			},
			headers: baseHeaderSchema,
			query: osesHistoryReqSchema,
			response: {
				200: osesHistoryResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/status',
		async (ctx): Promise<OsesStatusRes> => {
			return await ctx.Oses.getPaymentStatus(ctx.body);
		},
		{
			detail: {
				description:
					'Get OSES payment status for non-login customer (Guest) based on merchant txn id. <br><br> <b>Table:</b> oses_txn_history',
				tags: ['Payment']
			},
			body: osesStatusReqSchema,
			response: {
				200: osesStatusResSchema,
				500: errorBaseResponseSchema
			}
		}
	);
