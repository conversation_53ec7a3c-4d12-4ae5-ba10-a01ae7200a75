import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const netcoreEmailTxnHistoryTableSchema = pgTable(
	'netcore_email_txn_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		TxnId: text('txn_id').notNull(),
		TxnType: text('txn_type').notNull(),
		RecipientEmail: text('recipient_email').notNull(),
		RequestBody: text('request_body').notNull(),
		ResponseBody: text('response_body').notNull(),
		IsEmailSent: boolean('is_email_sent').notNull(),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	}
);
