import { type Static, t } from 'elysia';
import { wso2TroikaResResourceSchema } from '../../../../../integration/wso2/troika/schemas/api/troika';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const createTroikaReqSchema = t.Object({
	OrderId: t.String(),
	DemandCategory: t.String()
});

export type CreateTroikaReq = Static<typeof createTroikaReqSchema>;

export const createTroikaResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: wso2TroikaResResourceSchema
});

export type CreateTroikaRes = Static<typeof createTroikaResSchema>;

export const updateTroikaReqSchema = t.Object({
	OrderId: t.String({ examples: ['UNFH-123456'] }),
	DemandAction: t.String({ examples: ['CANCEL', 'REMARK', 'ATTACHMENT'] }),
	Remark: t.Optional(t.String()),
	FileName: t.Optional(t.String()),
	Attachment: t.Optional(t.String())
});

export type UpdateTroikaReq = Static<typeof updateTroikaReqSchema>;

export const updateTroikaNbaReqSchema = t.Object({
	troikaId: t.String({ examples: ['NJ-1-1000018808'] }),
	demandId: t.String({ examples: ['UNFH-123456'] }),
	nbaResult: t.String({ examples: ['BROWNFIELD', 'GREENFIELD'] }),
	fdp: t.Optional(t.String()),
	portNumber: t.Optional(t.String()),
	rfsDate: t.Optional(t.String()),
	demandStatus: t.String({ examples: ['OPEN', 'PENDING ORDER CREATION'] }),
	reasonMessage: t.Optional(t.String()),
	ndRemark: t.Optional(t.String()),
	lov: t.Optional(t.String()),
	address: t.Optional(t.String()),
	addressId: t.Optional(t.String())
});

export type UpdateTroikaNbaReq = Static<typeof updateTroikaNbaReqSchema>;

export const updateTroikaResNbaSchema = t.Object({
	NBAUpdate: t.Object({
		RespHeader: t.Object({
			DateTimeResp: t.String(),
			MessageID: t.Optional(t.String()),
			ErrorCode: t.String(),
			ErrorMsg: t.String()
		})
	})
});

export type UpdateTroikaNbaRes = Static<typeof updateTroikaResNbaSchema>;

//Query Troika Demand Status Req
export const queryTroikaDemandStatusReqSchema = t.Object({
	OrderId: t.String()
});

export type QueryTroikaDemandStatusReq = Static<
	typeof queryTroikaDemandStatusReqSchema
>;
