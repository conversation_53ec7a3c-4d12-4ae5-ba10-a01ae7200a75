import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type CreateSRRes,
	createSRReqSchema,
	createSRResSchema
} from '../schemas/api/serviceRequest.schema';
import ServiceRequest from '../services/serviceRequest.service';

const serviceRequestV1Routes = new Elysia()
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			ServiceRequest: new ServiceRequest(randomUUID(), idTokenInfo)
		};
	})
	.post(
		'/sr',
		async (ctx): Promise<CreateSRRes> => {
			const res = await ctx.ServiceRequest.createSR(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Create service request (SR) which includes service termination. <br><br> <b>Backend:</b> NOVA SIEBEL, ICP, TMFORCE <br> <b>Table: </b> service_request_history',
				tags: ['Record']
			},
			body: createSRReqSchema,
			response: {
				201: createSRResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default serviceRequestV1Routes;
