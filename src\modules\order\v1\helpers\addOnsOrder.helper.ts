import { eq, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../config/db.config';
import { envConfig } from '../../../../config/env.config';
import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { EmailEnum } from '../../../../enum/notification.enum';
import { OrderTypeEnum, ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type { NotificationEmailRequestAddon } from '../../../../integration/emailTemplate/schemas/api/sendEmailNotification';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import type {
	Wso2MwCreateOrderReq,
	Wso2MwCreateOrderSubmitRes
} from '../../../../integration/wso2/order/schemas/api/wso2CreateAddOnsOrder';
import type {
	Wso2ServiceAccountOli,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate } from '../../../../shared/common';
import { addonsCatalogueTableSchema } from '../../../catalogue/v1/schemas/db/addOnsCatalogue.schema';
import type { SelectTvPackCatalogue } from '../../../catalogue/v1/schemas/db/tvPackCatalogue.schema';
import type { AddOnsOrderData } from '../schemas/addOnsOrderData.schema';
import type {
	AddOnsOrderReq,
	AddOnsOrderRes,
	BillingAddress
} from '../schemas/api/addOnsOrder.schema';
import {
	type OrderableProgress,
	orderableTxnHistoryTableSchema
} from '../schemas/db/orderable.schema';
import {
	type SelectSiebelProductMap,
	siebelProductMapTableSchema
} from '../schemas/db/siebelProductMap.schema';
import AddOnsDeviceHelper from './addOnsDevice.helper';
import AddOnsTvPackHelper from './addOnsTvPack.helper';

class AddOnsOrderHelper {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private addOnsTvPackHelper: AddOnsTvPackHelper;
	private addOnsDeviceHelper: AddOnsDeviceHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.addOnsTvPackHelper = new AddOnsTvPackHelper(integrationId);
		this.addOnsDeviceHelper = new AddOnsDeviceHelper(integrationId);
	}

	async submitOrderTvPack(
		orderId: string,
		req: AddOnsOrderReq,
		wso2SARes: Wso2ServiceAccountRes
	): Promise<AddOnsOrderRes> {
		if (req.Products.length > 1) {
			throw new UE_ERROR(
				'Only one product is allowed for TV Add-On',
				StatusCodeEnum.CONFLICT,
				{
					integrationId: this.integrationId,
					response: 'ADDON-0001'
				}
			);
		}

		/**
		 * There are two scenarios for TV Pack:
		 * 1. The user is not subscribed to any TV Pack.
		 * 2. The user is already subscribed and is only allowed to perform an upgrade/swap.
		 */
		const orderProgress: OrderableProgress = [];
		const tvPack: SelectTvPackCatalogue =
			await this.addOnsTvPackHelper.getTvPackByProductName(
				req.Products[0].Id,
				req.Products[0].ProductName
			);
		const siebelProductMap: SelectSiebelProductMap[] =
			await this.getSiebelProductMap();

		const order = this.addOnsTvPackHelper.addExistingServiceLineItem(
			orderId,
			req,
			wso2SARes,
			tvPack
		);
		const hasTvPack = this.addOnsTvPackHelper.hasHyppTvMoli(order);
		const serviceId = this.addOnsTvPackHelper.getServiceId(order);
		const iptvId = serviceId.replace('@unifi', '');
		if (!hasTvPack) {
			const reservationResult = await this.addOnsTvPackHelper.reserveIptvId(
				iptvId,
				req.CustomerInfo.AccountNo
			);

			orderProgress.push({
				Status: ProgressStatusEnum.RESERVED_IPTV,
				Timestamp: `${getMyTimeZoneDate().toISOString()}`
			});

			const residentialHsiMoli =
				this.addOnsTvPackHelper.getResidentialHsiMoli(order);

			if (!residentialHsiMoli) {
				throw new UE_ERROR(
					'Residential HSI Moli not found. Wrong order constructed. Please contact developer.',
					StatusCodeEnum.CONFLICT,
					{
						integrationId: this.integrationId,
						response: 'ADDON-0002'
					}
				);
			}

			this.addOnsTvPackHelper.addHyppTvMoli(
				reservationResult,
				order,
				residentialHsiMoli,
				req.CustomerInfo.AccountNo,
				req.Products[0].ProductName
			);
		}

		const existTvPackPartNum: string =
			this.addOnsTvPackHelper.getCurrentTvPackPartNo(wso2SARes);
		const currentCommitmentOli: Wso2ServiceAccountOli =
			this.addOnsTvPackHelper.getTvPackCommitmentOli(wso2SARes);

		// Add new service line item for the new TV Pack
		this.addOnsTvPackHelper.addNewServiceLineItem(
			hasTvPack,
			existTvPackPartNum,
			req.Products[0],
			order,
			tvPack,
			siebelProductMap,
			currentCommitmentOli
		);

		const wso2Req: Wso2MwCreateOrderReq = {
			OrderSubmitPortalRetailRequest: {
				OrderSubmitPortalRetailReq: {
					Order: order
				}
			}
		};

		const wso2Res: Wso2MwCreateOrderSubmitRes =
			await this.mwIntegration.Wso2OrderIntegration.createAddonOrder(wso2Req);

		const siebelOrderStatus: string =
			wso2Res.Response.PortalMessage.StatusMessageFromSiebel;

		if (siebelOrderStatus === 'Order Details sent to NOVA_Siebel') {
			orderProgress.push({
				Status: ProgressStatusEnum.SUBMITTED,
				Timestamp: `${getMyTimeZoneDate().toISOString()}`
			});

			const isEmailSent: boolean = await this.sendEmailNotification(
				wso2SARes,
				orderId,
				req,
				iptvId
			);

			orderProgress.push(
				{
					Status: isEmailSent
						? ProgressStatusEnum.EMAIL_SENT
						: ProgressStatusEnum.EMAIL_FAILED,
					Timestamp: `${getMyTimeZoneDate().toISOString()}`
				},
				{
					Status: ProgressStatusEnum.INPROGRESS,
					Timestamp: `${getMyTimeZoneDate().toISOString()}`
				}
			);
		} else {
			orderProgress.push({
				Status: ProgressStatusEnum.FAILED,
				Timestamp: `${getMyTimeZoneDate().toISOString()}`
			});
		}

		await this.updateAddOnsOrder(
			orderId,
			siebelOrderStatus,
			orderProgress,
			wso2Req
		);

		return {
			Success: true,
			Code: 201,
			IntegrationId: this.integrationId,
			Response: {
				Status: wso2Res.Response.PortalMessage.Status,
				OrderNo: orderId,
				IptvId: iptvId
			}
		};
	}

	async submitOrderDevice(data: AddOnsOrderData): Promise<AddOnsOrderRes> {
		const { orderId, req, wso2SARes, reservationNo, activityId } = data;

		const orderProgress: OrderableProgress = [];

		const siebelProductMap: SelectSiebelProductMap[] =
			await this.getSiebelProductMap();

		// Existing product
		const order = this.addOnsDeviceHelper.addExistingServiceLineItem(
			req,
			wso2SARes,
			orderId
		);

		// New product
		this.addOnsDeviceHelper.addNewServiceLineItem(
			req,
			order,
			wso2SARes,
			siebelProductMap,
			reservationNo,
			activityId
		);

		const mwCreateOrderRequest: Wso2MwCreateOrderReq = {
			OrderSubmitPortalRetailRequest: {
				OrderSubmitPortalRetailReq: {
					Order: order
				}
			}
		};

		const mwCreateOrderSubmitResponse: Wso2MwCreateOrderSubmitRes =
			(await this.mwIntegration.Wso2OrderIntegration.createAddonOrder(
				mwCreateOrderRequest
			)) as Wso2MwCreateOrderSubmitRes;

		const siebelOrderStatus =
			mwCreateOrderSubmitResponse.Response.PortalMessage
				.StatusMessageFromSiebel;

		if (siebelOrderStatus === 'Order Details sent to NOVA_Siebel') {
			orderProgress.push({
				Status: ProgressStatusEnum.SUBMITTED,
				Timestamp: `${getMyTimeZoneDate().toISOString()}`
			});

			const isEmailSent = await this.sendEmailNotification(
				wso2SARes,
				orderId,
				req
			);

			orderProgress.push(
				{
					Status: isEmailSent
						? ProgressStatusEnum.EMAIL_SENT
						: ProgressStatusEnum.EMAIL_FAILED,
					Timestamp: `${getMyTimeZoneDate().toISOString()}`
				},
				{
					Status: ProgressStatusEnum.INPROGRESS,
					Timestamp: `${getMyTimeZoneDate().toISOString()}`
				}
			);
		} else {
			orderProgress.push({
				Status: ProgressStatusEnum.FAILED,
				Timestamp: `${getMyTimeZoneDate().toISOString()}`
			});
		}

		await this.updateAddOnsOrder(
			orderId,
			siebelOrderStatus,
			orderProgress,
			mwCreateOrderRequest
		);

		const res: AddOnsOrderRes = {
			Success: true,
			Code: 201,
			IntegrationId: this.integrationId,
			Response: {
				Status: mwCreateOrderSubmitResponse.Response.PortalMessage.Status,
				OrderNo: orderId
			}
		};

		return res;
	}

	private async getSiebelProductMap(): Promise<SelectSiebelProductMap[]> {
		return await this.db
			.select()
			.from(siebelProductMapTableSchema)
			.catch(err => {
				throw new UE_ERROR(String(err), StatusCodeEnum.UE_INTERNAL_SERVER, {
					integrationId: this.integrationId,
					response: 'ADDON-0007'
				});
			});
	}

	private async sendEmailNotification(
		serviceAccounts: Wso2ServiceAccountRes,
		orderId: string,
		req: AddOnsOrderReq,
		iptvId?: string
	): Promise<boolean> {
		const product = req.Products[0];

		const getAddressLine = (
			addr: BillingAddress,
			isHighRise: boolean
		): [string, string] => {
			if (isHighRise) {
				return [
					[addr.UnitLot, addr.FloorNo].filter(Boolean).join(', '),
					[addr.BuildingName, addr.StreetName, addr.Section]
						.filter(Boolean)
						.join(' , ')
				];
			}
			return [
				[addr.UnitLot, addr.StreetName].filter(Boolean).join(' , '),
				addr.Section || ''
			];
		};

		const [billingAddress1, billingAddress2] = getAddressLine(
			req.CustomerInfo.BillingAddress,
			req.CustomerInfo.BillingAddress.AddressType.toUpperCase() === 'HIGH RISE'
		);

		const notificationEmailRequestAddon: NotificationEmailRequestAddon = {
			CustName: req.CustomerInfo.FullName,
			CustContactNo: req.CustomerInfo.ContactNo,
			BillingContactNo: req.CustomerInfo.BillingContactNo,
			CustEmail: req.CustomerInfo.Email,
			BillingEmail: req.CustomerInfo.BillingEmail,
			OrderNumber: orderId,
			ProductName: product.ProductName,
			PlanName: product.ProductName,
			Quantity: product.TotalQuantity,
			Price: '',
			billingAddress1,
			billingAddress2,
			billingCity: req.CustomerInfo.BillingAddress.City,
			billingPostcode: req.CustomerInfo.BillingAddress.Postcode,
			billingState: req.CustomerInfo.BillingAddress.State,
			country: 'Malaysia',
			ShippingCustName: req.CustomerInfo.FullName,
			shippingAddress1: '',
			shippingAddress2: '',
			shippingCity: '',
			shippingPostcode: '',
			shippingState: '',
			tvPackName: product.ProductName,
			iptvId
		};

		const tmAddr =
			serviceAccounts?.Response?.ServiceAccount?.[0]?.[
				'TmCutAssetMgmt-ServiceMeterIntegration'
			]?.TmCutAddressIntegration;
		if (tmAddr) {
			const section = tmAddr.Section || '';
			const isHighRise = tmAddr.TMAddressType?.toUpperCase() === 'HIGH-RISE';

			const shippingLine1 = isHighRise
				? [tmAddr.ApartmentNumber, tmAddr.TMFloorNo].filter(Boolean).join(' , ')
				: [
						tmAddr.ApartmentNumber,
						[tmAddr.StreetType, tmAddr.StreetName].filter(Boolean).join(' ')
					]
						.filter(Boolean)
						.join(' , ');

			const shippingLine2 = isHighRise
				? [tmAddr.TMBuildingName, tmAddr.StreetName, section]
						.filter(Boolean)
						.join(' , ')
				: section;

			Object.assign(notificationEmailRequestAddon, {
				shippingAddress1: shippingLine1,
				shippingAddress2: shippingLine2,
				shippingPostcode: tmAddr.PostalCode,
				shippingCity: tmAddr.City,
				shippingState: tmAddr.State
			});
		}

		// Set price string
		const totalPrice = (
			Number(product.MonthlyCommitment) * Number(product.TotalQuantity)
		).toFixed(2);
		if (
			req.Category === AddOnsRequestCategoryEnum.MESH_WIFI_6 &&
			product.ProductName.includes('OTC')
		) {
			notificationEmailRequestAddon.Price = `RM${totalPrice}/One time charge`;
		} else if (
			req.Category === AddOnsRequestCategoryEnum.SMART_HOME &&
			product.ContractTerm === '2'
		) {
			notificationEmailRequestAddon.Price = `RM${product.ProductPrice}`;
		} else if (req.Category === AddOnsRequestCategoryEnum.BLACKNUT) {
			notificationEmailRequestAddon.ControllerAddOn = `${Math.max(
				req.Products.length - 1,
				0
			)}`;
		} else {
			notificationEmailRequestAddon.Price = `RM${totalPrice}/month for ${product.ContractTerm} months`;
		}

		// Add free UPB device for TV Pack
		if (req.Category === AddOnsRequestCategoryEnum.TV_PACK) {
			const [selectedDevice] = await this.db
				.select()
				.from(addonsCatalogueTableSchema)
				.where(
					eq(addonsCatalogueTableSchema.Name, 'Speed Upgrade unifi Plus Box')
				)
				.execute();
			notificationEmailRequestAddon.freeUPBDetails = {
				deviceDescription: selectedDevice.Summary,
				deviceImage: selectedDevice.ImageUrl,
				price: `RM${selectedDevice.MonthlyCommitment}/month for ${selectedDevice.ContractTerm} months`
			};
		}

		for (const product of req.Products ?? []) {
			notificationEmailRequestAddon.deviceList?.push({
				deviceName: product.ProductName,
				quantity: product.TotalQuantity,
				price: product.ProductName.includes('OTC')
					? `RM${product.ProductPrice}/One time charge`
					: `RM${product.MonthlyCommitment}/month for ${product.ContractTerm} months`
			});
		}

		// Determine email URL
		const env = envConfig();
		const getUrlForCategory = (category: AddOnsRequestCategoryEnum): string => {
			switch (category) {
				case AddOnsRequestCategoryEnum.SME_SMART_DEVICE:
					return env.CONFIRMATION_SME_ADDON_NOTIFICATION_EMAIL_URL;
				case AddOnsRequestCategoryEnum.MESH_WIFI:
					return env.CONFIRMATION_MESH_NOTIFICATION_EMAIL_URL;
				case AddOnsRequestCategoryEnum.BLACKNUT:
					return env.CONFIRMATION_CLOUD_GAMING_NOTIFICATION_EMAIL_URL;
				case AddOnsRequestCategoryEnum.TV_PACK:
					if (product.ProductName.includes('Varnam'))
						return env.CONFIRMATION_VARNAM_NOTIFICATION_EMAIL_URL;
					if (product.ProductName.includes('Aneka'))
						return env.CONFIRMATION_ANEKA_NOTIFICATION_EMAIL_URL;
					if (product.ProductName.includes('Ruby'))
						return env.CONFIRMATION_RUBY_NOTIFICATION_EMAIL_URL;
					return env.CONFIRMATION_ULTIMATE_NOTIFICATION_EMAIL_URL;
				default:
					return env.CONFIRMATION_UPB_NOTIFICATION_EMAIL_URL;
			}
		};

		const emailUrl = getUrlForCategory(req.Category);

		const emailSubject =
			req.Category === AddOnsRequestCategoryEnum.TV_PACK
				? EmailEnum.SAVE_ORDER_EMAIL_SUBJECT_TV_PACK
				: req.Category === AddOnsRequestCategoryEnum.SME_SMART_DEVICE
					? EmailEnum.SAVE_ORDER_EMAIL_SUBJECT_SMART_DEVICE
					: EmailEnum.SAVE_ORDER_EMAIL_SUBJECT;

		const emailBody: string =
			await this.mwIntegration.EmailTemplateIntegration.getEmailBodyTemplateWithBodyRequest(
				emailUrl,
				notificationEmailRequestAddon
			);

		const wso2EmailReq: Wso2EmailReq = {
			to: req.CustomerInfo.BillingEmail,
			from: EmailEnum.ADDON_EMAIL_FROM,
			subject: emailSubject,
			body: emailBody
		};

		return await this.mwIntegration
			.getWso2NotificationIntegration()
			.getWso2SendEmail(wso2EmailReq, orderId, OrderTypeEnum.ADD_ONS);
	}

	private async updateAddOnsOrder(
		orderId: string,
		siebelOrderStatus: string,
		orderProgress: OrderableProgress,
		subOrderData: Wso2MwCreateOrderReq
	) {
		await this.db
			.update(orderableTxnHistoryTableSchema)
			.set({
				OrderStatus: orderProgress[orderProgress.length - 1].Status,
				SiebelOrderStatus: siebelOrderStatus,
				OrderProgress: sql`order_progress::jsonb || ${JSON.stringify(
					orderProgress
				)}::jsonb`,
				SubOrderData: subOrderData,
				UpdatedAt: getMyTimeZoneDate()
			})
			.where(eq(orderableTxnHistoryTableSchema.OrderId, orderId))
			.catch(err => {
				throw new UE_ERROR(
					`Failed to update order progress: ${String(err)}`,
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: 'ADDON-0026'
					}
				);
			});
	}
}

export default AddOnsOrderHelper;
