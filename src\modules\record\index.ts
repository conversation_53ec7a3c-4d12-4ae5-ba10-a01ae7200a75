import Elysia from 'elysia';
import activityTrackerV1Routes from './v1/controllers/activityTracker.controller';
import addOnsV1Routes from './v1/controllers/addOns.controller';
import easyfixV1Routes from './v1/controllers/easyfix.controller';
import miniOrderTrackerV1Routes from './v1/controllers/miniOrderTracker.controller';
import orderTrackerV1Routes from './v1/controllers/orderTracker.controller';
import rebateV1Routes from './v1/controllers/rebate.controller';
import serviceRequestV1Routes from './v1/controllers/serviceRequest.controller';
import { surveyV1Routes } from './v1/controllers/survey.controller';
import vocCallbackV1Routes from './v1/controllers/vocCallback.controller';

export const privateRecordV1Routes = new Elysia({ prefix: '/v1/record' })
	.use(activityTrackerV1Routes)
	.use(orderTrackerV1Routes)
	.use(rebateV1Routes)
	.use(serviceRequestV1Routes)
	.use(addOnsV1Routes);

export const protectedRecordV1Routes = new Elysia({ prefix: '/v1/record' })
	.use(surveyV1Routes)
	.use(vocCallbackV1Routes)
	.use(easyfixV1Routes)
	.use(miniOrderTrackerV1Routes);
