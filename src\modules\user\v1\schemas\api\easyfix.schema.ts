import { type Static, t } from 'elysia';
import { wso2NovaCreateCustomerSchema } from '../../../../../integration/wso2/record/schemas/api/wso2NovaCreateCustomer.schema';
import { wso2NovaRetrieveCustomerResSchema } from '../../../../../integration/wso2/record/schemas/api/wso2NovaRetrieveCustomer.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const easyfixNovaCreateCustomerResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: wso2NovaCreateCustomerSchema
	},
	{
		description: 'Customer details created successfully.'
	}
);

export type EasyfixNovaCreateCustomerRes = Static<
	typeof easyfixNovaCreateCustomerResSchema
>;

export const easyfixNovaRetrieveCustomerResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: wso2NovaRetrieveCustomerResSchema
	},
	{
		description: 'Customer details retrieved successfully.'
	}
);

export type EasyfixNovaRetrieveCustomerRes = Static<
	typeof easyfixNovaRetrieveCustomerResSchema
>;
