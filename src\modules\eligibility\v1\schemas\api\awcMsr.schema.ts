import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

const csProductDetailsSchema = t.Object({
	OrderType: t.String({ examples: ['NEW INSTALL'] }),
	CyberSecurityPackPlanName: t.String({
		examples: ['Unifi Cloud Storage Enterprise - RM94.40/mth']
	}),
	CyberSecurityPackPlanFee: t.Number({ examples: [94.4] }),
	CyberSecurityPackPlanMonthlyFee: t.Number({ examples: [94.4] }),
	CybersecurityPackID: t.String({ examples: ['FREEMIUM123'] }),
	CyberSecurityPackQuantity: t.Number({ examples: [1] }),
	CyberSecurityCampaignID: t.Optional(
		t.String({
			examples: ['FREEMIUM'],
			description: 'Can be found in the CybersecurityPackID'
		})
	),
	OldPack: t.Optional(
		t.String({ examples: ['FREEMIUM'], description: 'Previously Owned Pack' })
	),
	ServiceID: t.Optional(
		t.String({ examples: ['test@unifi'], description: '' })
	),
	SiebelOrderNo: t.Optional(
		t.String({ examples: ['1-ABC12DF345'], description: '' })
	)
});

export type CsProductDetails = Static<typeof csProductDetailsSchema>;

export const csAwcMsrEligibilityReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		examples: ['fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-']
	}),
	ProductDetails: t.Array(csProductDetailsSchema)
});

export type CSAWCMSREligibilityReq = Static<
	typeof csAwcMsrEligibilityReqSchema
>;

export const dmsAwcMsrEligibilityReqSchema = t.Object({
	PurchaseType: t.String({ example: 'Paid', minLength: 1 })
});

export type DmsAWCMSREligibilityReq = Static<
	typeof dmsAwcMsrEligibilityReqSchema
>;

export const awcMsrEligibilityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsMSRCheckPassed: t.Boolean(),
			IsAWCCheckPassed: t.Boolean(),
			IsSubmitToSlof: t.Boolean()
		})
	},
	{ description: "Customer's eligibility successfully retrieved." }
);

export type AWCMSREligibilityRes = Static<typeof awcMsrEligibilityResSchema>;
