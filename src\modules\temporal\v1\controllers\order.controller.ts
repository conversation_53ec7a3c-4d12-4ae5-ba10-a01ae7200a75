import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import Elysia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { baseHeaderSchema } from '../../../../shared/schemas/api/headers.schema';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import {
	type AddOnsEligibilityRes,
	addOnsEligibilityResSchema
} from '../../../eligibility/v1/schemas/api/addOns.schema';
import {
	type AddOnsOrderRes,
	addOnsOrderReqSchema,
	addOnsOrderResSchema
} from '../../../order/v1/schemas/api/addOnsOrder.schema';
import {
	type CheckStockByListRes,
	checkStockByListResSchema
} from '../../../order/v1/schemas/api/checkStockByList.schema';
import {
	type AddonsAppointmentRes,
	addOnsOrderReserveResSchema,
	addonsAppointmentResSchema,
	temporalAddOnsOrderReqSchema
} from '../schemas/api/addons.schema';
import {
	type GetNovaStatusRes,
	type OrderDetailsRes,
	getNovaStatusReqSchema,
	getNovaStatusResSchema,
	orderDetailsBodyReqSchema,
	orderDetailsResSchema,
	orderUpdateReqSchema,
	updateOrderDetailsReqSchema
} from '../schemas/api/order.schema';
import { AddOns } from '../services/addon.service';
import Order from '../services/order.service';

export const protectedOrdersV1Routes = new Elysia({ prefix: '/order' })
	.resolve(() => {
		const integrationId = randomUUID();
		const order = new Order(integrationId);
		return {
			Order: order,
			AddOns: new AddOns(integrationId, order)
		};
	})
	.post(
		'/details',
		async (ctx): Promise<OrderDetailsRes> => {
			const orderDetailRes = await ctx.Order.getOrderDetails(ctx.body);

			ctx.set.status = orderDetailRes.Code;

			return orderDetailRes;
		},
		{
			body: orderDetailsBodyReqSchema,
			response: {
				200: orderDetailsResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Get Order Details for Temporal <br><br> <b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			}
		}
	)
	.put(
		'/status',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.Order.updateOrderStatus(ctx.body);
		},
		{
			body: orderUpdateReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Update Order from Temporal workflow <br><br> <b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			}
		}
	)
	.put(
		'/order-update',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.Order.updateOrderDetails(ctx.body);
		},
		{
			body: updateOrderDetailsReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Update Order from Temporal workflow <br><br> <b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addons/check-stock',
		async (ctx): Promise<CheckStockByListRes> => {
			return await ctx.AddOns.checkStock(ctx.body);
		},
		{
			body: temporalAddOnsOrderReqSchema,
			response: {
				200: checkStockByListResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					'Check stock availability by SKU item number from MMAG. This API checks the stock status of a given item.<br><br><b>Backend:</b> MMAG',
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addon/eligibility',
		async (ctx): Promise<AddOnsEligibilityRes> => {
			return await ctx.AddOns.checkEligibility(ctx.body);
		},
		{
			body: temporalAddOnsOrderReqSchema,
			response: {
				200: addOnsEligibilityResSchema,
				500: errorBaseResponseSchema,
				404: errorBaseResponseSchema
			},
			detail: {
				description:
					"Get customer's addons subscription eligibility. <br><br> <b>Backend System:</b> NOVA SIEBEL & OMG",
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addon/reserve',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.AddOns.reserveStock(ctx.body);
		},
		{
			body: temporalAddOnsOrderReqSchema,
			response: {
				200: addOnsOrderReserveResSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Reserve addon order device reservation. <br><br> <b>Backend System:</b> MMAG',
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addon/unreserve',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.AddOns.unreserveStock(ctx.body);
		},
		{
			body: temporalAddOnsOrderReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Unreserve addon order device reservation. <br><br> <b>Backend System:</b> MMAG',
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addon/nova-submissions',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.Order.submitNova(ctx.body);
		},
		{
			body: temporalAddOnsOrderReqSchema,
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Submit Addons Order to NOVA. <br><br> <b>Backend System:</b> NOVA SIEBEL',
				tags: ['Temporal']
			}
		}
	)
	.post(
		'/addon/appointment-slot',
		async (ctx): Promise<AddonsAppointmentRes> => {
			const res = await ctx.AddOns.bookAppointment(ctx.body);
			return res;
		},
		{
			body: temporalAddOnsOrderReqSchema,
			response: {
				200: addonsAppointmentResSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Book order appointment. <br><br> <b>Backend System:</b> SWIFT',
				tags: ['Temporal']
			}
		}
	)

	.post(
		'/addon/appointment-reschedule',
		async (ctx): Promise<AddonsAppointmentRes> => {
			const res = await ctx.AddOns.bookAppointmentReschedule(ctx.body);
			return res;
		},
		{
			body: temporalAddOnsOrderReqSchema,
			response: {
				200: addonsAppointmentResSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Book order appointment. <br><br> <b>Backend System:</b> SWIFT',
				tags: ['Temporal']
			}
		}
	)

	.get(
		'/nova/status',
		async (ctx): Promise<GetNovaStatusRes> => {
			return await ctx.Order.getOmgNovaOrderStatusByOrderId(ctx.query);
		},
		{
			query: getNovaStatusReqSchema,
			response: {
				200: getNovaStatusResSchema,
				500: errorBaseResponseSchema,
				404: baseResponseSchema
			},
			detail: {
				description:
					'Get the status of an order from the NOVA system. <br><br> <b>Backend System:</b> NOVA',
				tags: ['Temporal']
			}
		}
	);

export const privateOrdersV1Routes = new Elysia({ prefix: '/order' })
	.use(bearer())
	.resolve(async ctx => {
		const integrationId = randomUUID();
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			Order: new Order(integrationId, idTokenInfo)
		};
	})
	.post(
		'/addon',
		async (ctx): Promise<AddOnsOrderRes> => {
			const res = await ctx.Order.saveAddOnOrder(ctx.headers, ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Submit orderable add-ons order such as Smart Device, Smart Home, TV Pack, etc.<br><br><b>Table:</b> orderable_txn_history',
				tags: ['Temporal']
			},
			headers: baseHeaderSchema,
			body: addOnsOrderReqSchema,
			response: {
				201: addOnsOrderResSchema,
				500: errorBaseResponseSchema
			}
		}
	);
