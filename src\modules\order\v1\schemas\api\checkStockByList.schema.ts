import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const checkStockByListReqSchema = t.Object({
	PartnerIds: t.Array(t.String({ examples: ['2TC42EG1X'] }))
});

export type CheckStockByListReq = Static<typeof checkStockByListReqSchema>;

const stockInventorySchema = t.Object({
	BranchCode: t.String({ examples: ['10102'] }),
	BranchName: t.String({ examples: ['webe DMP ON-LINE STOCKS'] }),
	QtyOnHand: t.Number({ examples: [25] }),
	QtyReserved: t.Number({ examples: [0] }),
	QtyAvailable: t.Number({ examples: [25] })
});

export type StockInventory = Static<typeof stockInventorySchema>;

const checkStockByListObjSchema = t.Object({
	PartnerId: t.String({ example: '2TC42EG1X' }),
	StockStatus: t.String({
		examples: ['Available', 'Restocking In Progress', 'Selling Fast']
	}),
	Message: t.String({
		examples: [
			'Stock data fetched successfully.',
			'No inventory data available for item SKU: 2TC42EG1X'
		]
	}),
	Inventory: t.Array(stockInventorySchema)
});

export type CheckStockByListObj = Static<typeof checkStockByListObjSchema>;

export const checkStockByListResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Array(checkStockByListObjSchema)
});

export type CheckStockByListRes = Static<typeof checkStockByListResSchema>;
