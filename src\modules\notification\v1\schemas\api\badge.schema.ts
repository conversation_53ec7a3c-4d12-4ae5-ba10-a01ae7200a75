import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

const billingAccountBadgeObjSchema = t.Object({
	EncryptedBillAccNo: t.String({
		example: 'fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-'
	}),
	Badges: t.Array(
		t.Object({
			BadgeMessage: t.String({
				example: 'Activate your streaming app.Don’t miss out!',
				description: 'Message to be displayed on the badge.'
			}),
			BadgeType: t.String({
				example: 'info',
				description:
					'Type of the badge notification. It can be persistent or informational.'
			})
		})
	)
});

export type BillingAccountBadgeObj = Static<
	typeof billingAccountBadgeObjSchema
>;

export const badgeByBillingAccountResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		BillingAccounts: t.Array(billingAccountBadgeObjSchema)
	})
});

export type BadgeByBillingAccountRes = Static<
	typeof badgeByBillingAccountResSchema
>;
