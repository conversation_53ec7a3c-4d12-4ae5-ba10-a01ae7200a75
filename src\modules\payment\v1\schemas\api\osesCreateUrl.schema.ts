import { type Static, t } from 'elysia';
import { OsesPaymentTypeEnum } from '../../../../../enum/payment.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const osesBillingAccountReqSchema = t.Object({
	BillingAccountNo: t.String({ examples: ['**********'] }),
	Amount: t.String({ examples: ['1000'] }),
	BillNo: t.Optional(
		t.String({
			examples: ['**********'],
			description: 'Required if IsKCIPayment is set to true'
		})
	)
});

export type OsesBillingAccountReq = Static<typeof osesBillingAccountReqSchema>;

export const createOsesUrlReqSchema = t.Object({
	PayerEmail: t.String({ format: 'email', examples: ['<EMAIL>'] }),
	PayerName: t.String({ default: 'Anonymous' }),
	RedirectUrl: t.String({
		format: 'uri',
		examples: ['https://www.test.com/api/v1/payment/callback']
	}),
	IsKCIPayment: t.Boolean(),
	IsBillReadiness: t.Boolean(),
	PaymentType: t.Enum(OsesPaymentTypeEnum),
	BillingAccounts: t.Array(osesBillingAccountReqSchema)
});

export type CreateOsesUrlReq = Static<typeof createOsesUrlReqSchema>;

export const createOsesUrlResObjSchema = t.Object({
	LANG: t.String({ examples: ['en'] }),
	MERCHANTID: t.String({ examples: ['MYUNIFIBILL'] }),
	MERCHANT_TRANID: t.String({ examples: [**********] }),
	CURRENCYCODE: t.String({ examples: ['MYR'] }),
	AMOUNT: t.String({ examples: ['1000'] }),
	CUSTNAME: t.String({ examples: ['John Doe'] }),
	CUSTEMAIL: t.String({ format: 'email', examples: ['<EMAIL>'] }),
	RETURN_URL: t.String({
		examples: ['https://www.test.com/api/v1/payment/callback']
	}),
	SIGNATURE: t.String({ examples: ['asdgfho3214lda124ig321'] }),
	TOTAL_CHILD_TRANSACTION: t.String({ examples: ['1'] }),
	CHILD_TRANSACTION: t.String({ examples: ['1'] }),
	OSES_URL: t.String({
		examples: ['https://www.test.com/api/v1/payment/callback']
	}),
	PYMT_IND: t.String({ examples: ['tokenization'] }),
	PYMT_CRITERIA: t.String({ examples: ['payment;<EMAIL>'] }),
	DESCRIPTION: t.String({ examples: ['Payment MyUnifi'] }),
	REDIRECT_URL: t.Optional(t.String())
});

export type CreateOsesUrlResObj = Static<typeof createOsesUrlResObjSchema>;

export const createOsesUrlResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: createOsesUrlResObjSchema
	},
	{
		description:
			"Customer's payment transaction request information successfully constructed. Front end can use this information to construct URL for redirection to TM OSES page."
	}
);

export type CreateOsesUrlRes = Static<typeof createOsesUrlResSchema>;

export const osesChildTxnObjSchema = t.Optional(
	t.Object({
		Name: t.String({ examples: ['John Doe'] }),
		Email: t.String({ format: 'email', examples: ['<EMAIL>'] }),
		BillingAccountNo: t.String({ examples: ['**********'] }),
		BillNo: t.Optional(t.String({ examples: ['1234'] })),
		GrossAmount: t.String({ examples: ['1.00'] }),
		GbtAmount: t.String({ examples: ['1.00'] }),
		NettAmount: t.String({ examples: ['1.00'] }),
		MiscAmount: t.String({ examples: ['1.00'] }),
		RevenueCode: t.String({ examples: ['101'] }),
		SystemName: t.String({ examples: ['NOVA'] })
	})
);

export type OsesChildTxnObj = Static<typeof osesChildTxnObjSchema>;

export const osesChildTxnSchema = t.Array(osesChildTxnObjSchema);

export type OsesChildTxn = Static<typeof osesChildTxnSchema>;

export const pgOsesChildTxnObjSchema = t.Object({
	sub_merchant_id: t.String(),
	sub_order_id: t.String(),
	gross_amount: t.String(),
	gbt_amount: t.String(),
	nett_amount: t.String(),
	misc_amount: t.String(),
	account_no: t.String(),
	revenue_code: t.String()
});

export type PgOsesChildTxnObj = Static<typeof pgOsesChildTxnObjSchema>;

export const pgOsesChildTxnSchema = t.Object({
	txn: t.Object({
		child_txn: t.Array(pgOsesChildTxnObjSchema)
	})
});

export type PgOsesChildTxn = Static<typeof pgOsesChildTxnSchema>;
