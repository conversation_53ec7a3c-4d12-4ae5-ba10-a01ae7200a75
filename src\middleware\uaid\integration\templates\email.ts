export function sendTacTemplate(tacNumber: string): string {
	return `
		<html lang="en" style="background-color: black; color: white;">
			<head>
				<title>MyUnifi App - OTP</title>
			</head>
			<body>
                <p>You recently requested a OTP for your MyUnifi App account.</p>
                <p>Here's your OTP:</p> 
				<h3>${tacNumber}</h3>
				<p>This OTP is valid for 5 mins</p>
			</body>
		</html>
	`;
}

export function sendEmailRetrievalTemplate(
	email: string,
	maskedIdNumber: string
): string {
	return `
		<html lang="en" style="background-color: black; color: white;">
			<head>
				<title>Forgot ID - Credential Retrieval</title>
			</head>
			<body>
				<h2>Hello ${email}</h2>
                <p>This email serves as the credential of the Unifi UniVerse App account registered under ID Number ${maskedIdNumber}</p>
				<br /><br />
				<p>Your virtual Unifi assistant,<br />maya</p>
			</body>
		</html>
	`;
}
