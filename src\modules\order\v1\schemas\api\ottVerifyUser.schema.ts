import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const ottVerifyUserReqSchema = t.Object({
	AccountType: t.String({
		description: 'Type of account (e.g., Broadband)',
		minLength: 1,
		example: 'Broadband'
	}),
	AccountId: t.String({
		description: 'Internet Account ID',
		minLength: 1,
		example: 'jsuhdk77@unifi'
	}),
	OttMerchantId: t.Number({
		description: 'Unique ID of the OTT merchant',
		minimum: 1,
		example: 1001
	}),
	OttUserId: t.String({
		description: 'Login Id for OTT activation',
		minLength: 1,
		examples: ['<EMAIL>', 'jsuhdk77@unifi', '***********']
	})
});

export type OttVerifyUserReq = Static<typeof ottVerifyUserReqSchema>;

export const ottVerifyUserResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			IsAvailable: t.<PERSON>({
				example: false
			})
		})
	},
	{
		description: 'Indicates whether the OTT login ID is available or not'
	}
);

export type OttVerifyUserRes = Static<typeof ottVerifyUserResSchema>;
