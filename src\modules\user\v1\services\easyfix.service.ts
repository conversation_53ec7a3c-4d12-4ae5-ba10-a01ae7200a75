import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2NovaCreateCustomer } from '../../../../integration/wso2/record/schemas/api/wso2NovaCreateCustomer.schema';
import type {
	Wso2NovaRetrieveCustomerReq,
	Wso2NovaRetrieveCustomerRes
} from '../../../../integration/wso2/record/schemas/api/wso2NovaRetrieveCustomer.schema';
import type {
	EasyfixNovaCreateCustomerRes,
	EasyfixNovaRetrieveCustomerRes
} from '../schemas/api/easyfix.schema';

class Easyfix {
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async createNovaCustomer(
		req: Wso2NovaCreateCustomer
	): Promise<EasyfixNovaCreateCustomerRes> {
		const wso2Res: Wso2NovaCreateCustomer =
			await this.mwIntegration.Wso2RecordIntegration.wso2NovaCreateCustomer(
				req
			);
		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: wso2Res
		};
	}

	async getNovaCustomer(
		req: Wso2NovaRetrieveCustomerReq
	): Promise<EasyfixNovaRetrieveCustomerRes> {
		const wso2Res: Wso2NovaRetrieveCustomerRes =
			await this.mwIntegration.Wso2RecordIntegration.wso2NovaRetrieveCustomer(
				req
			);
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: wso2Res
		};
	}
}

export default Easyfix;
