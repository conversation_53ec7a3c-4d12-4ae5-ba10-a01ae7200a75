import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

//Activate request
export const ottOrderSchema = t.Object({
	OttMerchantId: t.Integer({ example: 38 }),
	OttProductId: t.String({ example: '42' }),
	OttOmgId: t.Integer({ example: 141 }),
	OttLoginType: t.String({ example: 'mobile' }),
	OttUserId: t.String({ example: 'johndoe@ott' }),
	OttName: t.String({ example: '<PERSON>' })
});

export type OttOrder = Static<typeof ottOrderSchema>;

export const ottActivateReqSchema = t.Object({
	OttPlanId: t.String({ example: 'P39' }),
	AccountType: t.String({ example: 'Broadband' }),
	AccountId: t.String({ example: 'johndoe@unifi' }),
	EncryptedBillAccNo: t.String({
		example: 'fh2490348gh9=43tqhjuwg9i8ht9qh3e0=-'
	}),
	OttOrder: t.Array(ottOrderSchema),
	CustName: t.String({ example: 'John Doe' }),
	CustEmail: t.String({ example: 'johndoe@gmail' }),
	TvPackName: t.String({ example: 'Aneka Pack' }),
	IptvId: t.String({ example: 'johndoe@iptv' })
});

export type OttActivateReq = Static<typeof ottActivateReqSchema>;

export const ottActivateResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: t.Object({
		OrderRefNo: t.String({ examples: ['OTT-698964'] })
	})
});

export type OttActivateRes = Static<typeof ottActivateResSchema>;
