import { type Static, t } from 'elysia';

const wso2CustomerAccountReqSchema = t.Object({
	idType: t.String(),
	idValue: t.String()
});

export type Wso2CustomerAccountReq = Static<
	typeof wso2CustomerAccountReqSchema
>;

const wso2TmComInvoiceProfileIntegration = t.Optional(
	t.Object({
		Id: t.Optional(t.String()),
		BankLanguageCode: t.Optional(t.String()),
		BillCycle: t.Optional(t.String()),
		BillFrequency: t.Optional(t.String()),
		BillType: t.Optional(t.String()),
		City: t.Optional(t.String()),
		EmailBillTo: t.Optional(t.String()),
		MediaType: t.Optional(t.String()),
		Name: t.Optional(t.String()),
		PaymentMethod: t.Optional(t.String()),
		TMSalesPTT: t.Optional(t.String()),
		AddressId: t.Optional(t.String()),
		TMForeignAddressFlag: t.Optional(t.String()),
		ApartmentNumber: t.Optional(t.String()),
		StreetName: t.Optional(t.String()),
		Section: t.Optional(t.String()),
		TMAddressType: t.Optional(t.String()),
		TMStreetType: t.Optional(t.String()),
		PostalCode: t.Optional(t.String()),
		TMBuildingName: t.Optional(t.String()),
		TMFloorNo: t.Optional(t.String()),
		State: t.Optional(t.String()),
		Country: t.Optional(t.String())
	})
);

export type Wso2TmComInvoiceProfileIntegration = Static<
	typeof wso2TmComInvoiceProfileIntegration
>;

const wso2PreferredCustomerContactDetails = t.Optional(
	t.Object({
		ContactId: t.Optional(t.String()),
		TMContactCellPhone: t.Optional(t.String()),
		TMContactJobTitle: t.Optional(t.String()),
		TMContactEmailAddress: t.Optional(t.String()),
		TMContactFax: t.Optional(t.String()),
		TMContactHomePhone: t.Optional(t.String()),
		TMContactName: t.Optional(t.String()),
		TMContactPreferredContactMethod: t.Optional(t.String()),
		TMContactWorkPhone: t.Optional(t.String()),
		TMCustomerId: t.Optional(t.String()),
		TMCustomerIdType: t.Optional(t.String())
	})
);

export type Wso2PreferredCustomerContactDetails = Static<
	typeof wso2PreferredCustomerContactDetails
>;

export const wso2CustomerBillingAccountsInArraySchema = t.Optional(
	t.Object({
		Id: t.Optional(t.String()),
		AccountNumber: t.Optional(t.String()),
		AccountStatus: t.Optional(t.String()),
		AccountTypeCode: t.Optional(t.String()),
		CurrencyCode: t.Optional(t.String()),
		BillingName: t.Optional(t.String()),
		Type: t.Optional(t.String()),
		ApartmentNumber: t.Optional(t.String()),
		FloorNumber: t.Optional(t.String()),
		Section: t.Optional(t.String()),
		StreetType: t.Optional(t.String()),
		StreetName: t.Optional(t.String()),
		BuildingName: t.Optional(t.String()),
		City: t.Optional(t.String()),
		State: t.Optional(t.String()),
		Country: t.Optional(t.String()),
		PostalCode: t.Optional(t.String()),
		ListOfTmComInvoiceProfileIntegration: t.Optional(
			t.Array(wso2TmComInvoiceProfileIntegration)
		),
		InvoiceProfileIntegration: t.Optional(
			t.Object({
				Id: t.Optional(t.String()),
				EmailBillTo: t.Optional(t.String()),
				PaymentMethod: t.Optional(t.String()),
				BillType: t.Optional(t.String()),
				MediaType: t.Optional(t.String())
			})
		)
	})
);

export type Wso2CustomerBillingAccountsInArray = Static<
	typeof wso2CustomerBillingAccountsInArraySchema
>;

export const wso2CustomerBillingAccountsObjSchema = t.Optional(
	t.Array(wso2CustomerBillingAccountsInArraySchema)
);

export type Wso2CustomerBillingAccountsObj = Static<
	typeof wso2CustomerBillingAccountsObjSchema
>;

export const wso2CustomerAccountsInArraySchema = t.Optional(
	t.Object({
		ReturnCode: t.Optional(t.String()),
		ReturnMessage: t.Optional(t.String()),
		SystemName: t.Optional(t.String()),
		AccountNo: t.Optional(t.String()),
		TMLarsEnrollFlag: t.Optional(t.String()),
		TMCustomerIDNumber: t.Optional(t.String()),
		TMPrimaryCustomerIDType: t.Optional(t.String()),
		TMGender: t.Optional(t.String()),
		CurrencyCode: t.Optional(t.String()),
		TMNationality: t.Optional(t.String()),
		TMRace: t.Optional(t.String()),
		Name: t.Optional(t.String()),
		PreferredCommunicationMethod: t.Optional(t.String()),
		Status: t.Optional(t.String()),
		TMContactCellPhone: t.Optional(t.String()),
		TMContactEmailAddress: t.Optional(t.String()),
		TMSegmentCode: t.Optional(t.String()),
		TMSegmentGroup: t.Optional(t.String()),
		TMValueSegment: t.Optional(t.String()),
		Type: t.Optional(t.String()),
		TMCAMobilePhone: t.Optional(t.String()),
		TMCategory: t.Optional(t.String()),
		TMDateofBirth: t.Optional(t.String()),
		TMSegmentSubGroup: t.Optional(t.String()),
		TMMembershipNo: t.Optional(t.String()),
		TMRewardNo: t.Optional(t.String()),
		TMMemberCategory: t.Optional(t.String()),
		TMMemberStatus: t.Optional(t.String()),
		PreferredCustomerContactDetails: wso2PreferredCustomerContactDetails,
		BillingAccounts: wso2CustomerBillingAccountsObjSchema
	})
);

export type Wso2CustomerAccountsInArray = Static<
	typeof wso2CustomerAccountsInArraySchema
>;

export const wso2CustomerAccountsObjSchema = t.Optional(
	t.Array(wso2CustomerAccountsInArraySchema)
);

export type Wso2CustomerAccountsObj = Static<
	typeof wso2CustomerAccountsObjSchema
>;

export const wso2CustomerAccountResObjSchema = t.Optional(
	t.Object({
		ManagedAccount: t.Optional(t.String()),
		CustomerAccounts: wso2CustomerAccountsObjSchema
	})
);

export const wso2CustomerAccountResSchema = t.Object({
	Status: t.Object({
		Type: t.Optional(t.String()),
		Code: t.Optional(t.String()),
		Message: t.Optional(t.String())
	}),
	Response: wso2CustomerAccountResObjSchema
});

export type Wso2CustomerAccountRes = Static<
	typeof wso2CustomerAccountResSchema
>;
