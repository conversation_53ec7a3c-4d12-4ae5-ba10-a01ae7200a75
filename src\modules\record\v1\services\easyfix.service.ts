import { randomUUID } from 'node:crypto';
import { format } from 'date-fns';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2CreateSRICP } from '../../../../integration/wso2/record/schemas/api/wso2CreateSRIcp.schema';
import type {
	Wso2CreateSRNovaReq,
	Wso2CreateSRNovaRes
} from '../../../../integration/wso2/record/schemas/api/wso2CreateSRNova.schema';
import type {
	Wso2EasyfixTnpsSurveyReq,
	Wso2EasyfixTnpsSurveyRes
} from '../../../../integration/wso2/record/schemas/api/wso2EasyfixTnpsSurvey.schema';
import type { Wso2NovaCTT } from '../../../../integration/wso2/record/schemas/api/wso2NovaCTT.schema';
import type {
	Wso2RetrieveNTTReq,
	Wso2RetrieveNTTRes
} from '../../../../integration/wso2/record/schemas/api/wso2RetrieveNTT.schema';
import type {
	Wso2SRDetailReq,
	Wso2SRDetailRes
} from '../../../../integration/wso2/record/schemas/api/wso2SRDetail.schema';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate, isValidDate } from '../../../../shared/common';
import type {
	CreateSRICPRes,
	CreateSRNovaRes,
	EasyFixActivityListReq,
	EasyFixActivityListRes,
	EasyFixCreateNovaTTRes,
	EasyFixGetNetworkTTRes,
	SubmitTnpsSurveyRes
} from '../schemas/api/easyfix.schema';

class Easyfix {
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(this.integrationId);
	}

	async getActivityList(
		body: EasyFixActivityListReq
	): Promise<EasyFixActivityListRes> {
		if (
			!isValidDate(body.SearchStartDate) ||
			!isValidDate(body.SearchEndDate)
		) {
			throw new UE_ERROR(
				'Invalid start or end date! Kindly follow pattern yyyy-MM-dd',
				StatusCodeEnum.BAD_REQUEST_ERROR
			);
		}

		const getWso2RecordIntegration = this.mwIntegration.Wso2RecordIntegration;
		const getWso2UserIntegration = this.mwIntegration.Wso2UserIntegration;
		const icpFormattedStartDate: string = format(
			body.SearchStartDate,
			'MM/dd/yyyy'
		);
		const icpFormattedEndDate: string = format(
			body.SearchEndDate,
			'MM/dd/yyyy'
		);
		const searchPeriod: string = `[Created]>='${icpFormattedStartDate}' AND [Created]<='${icpFormattedEndDate}'`;
		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: body.IdType,
			idValue: body.IdValue
		};

		const wso2Res: Wso2CustomerAccountRes =
			await getWso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.YES
			);

		const novaAccountNo: string =
			wso2Res.Response?.CustomerAccounts?.find(
				ca => ca && ca.SystemName === SystemNameEnum.NOVA
			)?.AccountNo ?? '';

		const wso2SRReq: Wso2SRDetailReq = {
			icpIDType: body.IdType,
			icpIDValue: body.IdValue,
			novaAccountNo: novaAccountNo,
			novaBillingAccountNo: '',
			SearchSpecByPeriod: searchPeriod
		};

		const wso2SRRes: Wso2SRDetailRes =
			await getWso2RecordIntegration.getWso2SRDetails(wso2SRReq);

		if (wso2SRRes.Response?.SRDetailsReturn) {
			for (const sr of wso2SRRes.Response.SRDetailsReturn) {
				sr.CustomerComments = 'You have logged a complaint report.';
				sr.Status =
					sr.Status === 'Close' ||
					sr.Status === 'Closed' ||
					sr.Status === 'Cancelled'
						? 'Closed'
						: 'Open';
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				SRActivityList: wso2SRRes.Response?.SRDetailsReturn ?? [],
				TTActivityList: wso2SRRes.Response?.TTDetailsReturn ?? []
			}
		};
	}

	async createNovaTT(body: Wso2NovaCTT): Promise<EasyFixCreateNovaTTRes> {
		const wso2Res: Wso2NovaCTT =
			await this.mwIntegration.Wso2RecordIntegration.wso2NovaCreateTT(body);
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: wso2Res
		};
	}

	async getNetworkTT(
		requestID: string,
		serviceNo: string,
		serviceType: string | undefined
	): Promise<EasyFixGetNetworkTTRes> {
		const wso2Req: Wso2RetrieveNTTReq = {
			RequestID: '',
			ServiceNoVobb: '',
			ServiceNoDell: ''
		};
		wso2Req.RequestID = requestID;
		if (serviceType !== undefined && serviceType === 'ServiceId') {
			wso2Req.ServiceNoVobb = serviceNo;
			wso2Req.ServiceNoDell = serviceNo;
		} else {
			if (serviceNo.length === 8) {
				if (serviceNo.startsWith('08')) {
					wso2Req.ServiceNoDell = `${serviceNo.substring(
						0,
						4
					)}00${serviceNo.substring(4)}`;
				} else {
					const serviceNoDellNew = `00${serviceNo}`;
					wso2Req.ServiceNoDell = `${serviceNoDellNew.substring(
						0,
						4
					)}0${serviceNoDellNew.substring(4)}`;
				}
			}

			if (serviceNo.length === 9) {
				wso2Req.ServiceNoVobb = `6${serviceNo}`;
				if (serviceNo.startsWith('08')) {
					wso2Req.ServiceNoDell = `${serviceNo.substring(
						0,
						4
					)}00${serviceNo.substring(4)}`;
				} else {
					const serviceNoDellNew = `0${serviceNo}`;
					wso2Req.ServiceNoDell = `${serviceNoDellNew.substring(
						0,
						4
					)}0${serviceNoDellNew.substring(4)}`;
				}
			}

			if (serviceNo.length === 10) {
				wso2Req.ServiceNoVobb = `6${serviceNo}`;
				wso2Req.ServiceNoDell = `0${serviceNo}`;
			}
		}

		const wso2Res: Wso2RetrieveNTTRes =
			await this.mwIntegration.Wso2RecordIntegration.wso2RetrieveNTT(wso2Req);

		if (
			wso2Res?.errorCode ||
			wso2Res?.errorMessage ||
			wso2Res?.ErrorCode ||
			wso2Res?.ErrorMessage
		) {
			throw new UE_ERROR(
				'WSO2 Network Trouble Ticket throwing error, kindly retry again later',
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId
				}
			);
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: wso2Res
		};
	}

	async createNovaSR(req: Wso2CreateSRNovaReq): Promise<CreateSRNovaRes> {
		const wso2Res: Wso2CreateSRNovaRes =
			await this.mwIntegration.Wso2RecordIntegration.wso2CreateSRNova(req);

		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: {
				Id:
					wso2Res?.ListOfTmEaiSttCreateSr?.TmServiceRequestIntegration?.Id ??
					'',
				SRNumber:
					wso2Res?.ListOfTmEaiSttCreateSr?.TmServiceRequestIntegration
						?.SRNumber ?? '',
				Status:
					wso2Res?.ListOfTmEaiSttCreateSr?.TmServiceRequestIntegration
						?.Status ?? ''
			}
		};
	}

	async createICPSR(req: Wso2CreateSRICP): Promise<CreateSRICPRes> {
		const wso2Res: Wso2CreateSRICP =
			await this.mwIntegration.Wso2RecordIntegration.wso2CreateSRICP(req);

		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: wso2Res
		};
	}

	async submitTnpsSurvey(
		req: Wso2EasyfixTnpsSurveyReq
	): Promise<SubmitTnpsSurveyRes> {
		req.requestHeader.requestId = randomUUID();
		req.surveyAPIRequest.surveyDetails.surveyCreatedDateTime = format(
			getMyTimeZoneDate(),
			'dd-MMM-yy hh:mm:ss.SSS aaa'
		);

		const wso2EasyfixTnpsSurveyRes: Wso2EasyfixTnpsSurveyRes =
			await this.mwIntegration.Wso2RecordIntegration.wso2EasyfixTnpsSurvey(req);
		return {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			IntegrationId: this.integrationId,
			Response: wso2EasyfixTnpsSurveyRes
		};
	}
}

export default Easyfix;
