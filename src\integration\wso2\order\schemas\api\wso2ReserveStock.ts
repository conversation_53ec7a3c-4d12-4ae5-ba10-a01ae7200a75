import { type Static, t } from 'elysia';

// Schema for the ReserveOrderRequest object
const wso2ItemsSchema = t.Object({
	Item_SKU: t.Optional(t.String()),
	Item_Name: t.Optional(t.String()),
	Item_Type: t.Optional(t.String()),
	Rate_Plan: t.Optional(t.String()),
	Item_Line_ID: t.Optional(t.String()),
	Item_Qty: t.Optional(t.String())
});

const wso2ReserveRequestSchema = t.Object({
	Frontend_Order_Id: t.String(),
	Items: t.Optional(wso2ItemsSchema),
	ItemsExt: t.Optional(t.Array(wso2ItemsSchema))
});

const wso2ReserveOrderRequestSchema = t.Object({
	Request: wso2ReserveRequestSchema
});

// Schema for the ReserveOrderResponse object
const wso2ReserveResponseSchema = t.Object({
	status: t.String(),
	code: t.Number(),
	message: t.String(),
	Reservation_No: t.String(),
	Frontend_Order_Id: t.String()
});

const wso2ReserveOrderResponseSchema = t.Object({
	response: wso2ReserveResponseSchema
});

export type Wso2Items = Static<typeof wso2ItemsSchema>;
export type Wso2ReserveReq = Static<typeof wso2ReserveRequestSchema>;
export type Wso2ReserveStockReq = Static<typeof wso2ReserveOrderRequestSchema>;
export type Wso2ReserveRes = Static<typeof wso2ReserveResponseSchema>;
export type Wso2ReserveStockRes = Static<typeof wso2ReserveOrderResponseSchema>;
