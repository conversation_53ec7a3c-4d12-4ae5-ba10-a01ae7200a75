# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependency directories
node_modules/
./src/middlewares/udid/node_modules/

# TypeScript cache

\*.tsbuildinfo

# Optional npm cache directory

.npm

# dotenv environment variable files

.env
.env.*
environments-*/
!environments-*.zip

# IDE settings
.vscode
.idea/
*.iws
*.iml
*.ipr

# bun
bun.lock

# all cache
**/cache

# production
/build

**/*.trace
**/*.zip
**/*.tar.gz
**/*.tgz
**/*.log
package-lock.json
**/*.bun

# Docker
Dockerfile

# Drizzle files
migrations

# Docker compose
docker-compose.yaml
bun.lockb
