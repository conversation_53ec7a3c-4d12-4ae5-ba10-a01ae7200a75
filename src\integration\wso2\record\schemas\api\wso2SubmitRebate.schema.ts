import { type Static, t } from 'elysia';

const wso2SubmitRebateReqSchema = t.Object({
	TMTaxPeriod: t.String(),
	TMServiceID: t.String(),
	TMRequestAmount: t.String(),
	TMRemarks: t.String(),
	TMProduct: t.String(),
	TMCurrentMonthAdjustmentFlag: t.String(),
	TMCTTNum: t.String(),
	TMBulkStatus: t.String(),
	TMBillingAccountNumber: t.String(),
	TMAdjustmentType: t.String(),
	TMAdjustmentReason: t.String(),
	TMAdjustmentLevel: t.String(),
	TMAdjustmentCode: t.String(),
	Id: t.String()
});

export type Wso2SubmitRebateReq = Static<typeof wso2SubmitRebateReqSchema>;

export const wso2SubmitRebateResSchema = t.Object({
	Status: t.MaybeEmpty(
		t.Object({
			Type: t.MaybeEmpty(t.String()),
			Code: t.MaybeEmpty(t.String()),
			Message: t.MaybeEmpty(t.String()),
			Description: t.MaybeEmpty(t.String())
		})
	)
});

export type Wso2SubmitRebateRes = Static<typeof wso2SubmitRebateResSchema>;
