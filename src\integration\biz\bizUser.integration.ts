import { envConfig } from '../../config/env.config';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import { fetchApi } from '../helper/fetchApi.helper';
import type {
	BizAccessToken,
	BizCCRegistrationReq,
	BizCCRegistrationRes
} from './schemas/api/cloudConnect.schema';
import type {
	BizECommerceLoginRes,
	BizECommerceRegistrationReq,
	BizECommerceRegistrationRes
} from './schemas/api/eCommerce.schema';

class BizUserIntegration {
	integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getCloudConnectAccessToken(): Promise<BizAccessToken> {
		const url = envConfig().CLOUD_CONNECT_CLIENT_LOGIN;
		const clientId = envConfig().CLOUD_CONNECT_CLIENT_ID;
		const clientSecret = `${process.env.CLOUD_CONNECT_CLIENT_SECRET}`;
		const body = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ clientId, clientSecret })
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.E_COMMERCE_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'Cloud Connect Login throw error',
				StatusCodeEnum.CLOUD_CONNECT_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as BizAccessToken;
	}

	async cloudConnectRegistration(
		bodyRequest: BizCCRegistrationReq,
		accessToken: string
	): Promise<BizCCRegistrationRes> {
		const url = envConfig().CLOUD_CONNECT_TENANT_REGISTER;
		const body = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.E_COMMERCE_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});
		if (!res.ok) {
			throw new UE_ERROR(
				'Cloud Connect Register Throw Error',
				StatusCodeEnum.CLOUD_CONNECT_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as BizCCRegistrationRes;
	}

	async eCommerceSSOLogin(): Promise<BizECommerceLoginRes> {
		const url = envConfig().ECOMMERCE_LOGIN;
		const username = envConfig().ECOMMERCE_USERNAME;
		const password = `${process.env.ECOMMERCE_PASSWORD}`;
		const body = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ username, password })
		};
		const res = await fetchApi(this.integrationId, url, body);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.E_COMMERCE_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'eCommerce Login Throw Error',
				StatusCodeEnum.E_COMMERCE_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as BizECommerceLoginRes;
	}

	async eCommerceRegistration(
		body: BizECommerceRegistrationReq,
		accessToken: string
	): Promise<BizECommerceRegistrationRes> {
		const url = envConfig().ECOMMERCE_REGISTER;
		const bodyRequest = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				'x-bpid': `${Math.random().toString(36).slice(2)}`,
				'x-boid': `${Math.random().toString(36).slice(2)}`,
				'x-calling-application': 'UE'
			},
			body: JSON.stringify(body)
		};
		const res = await fetchApi(this.integrationId, url, bodyRequest);
		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.E_COMMERCE_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		if (!res.ok) {
			throw new UE_ERROR(
				'eCommerce Register Throw Error',
				StatusCodeEnum.E_COMMERCE_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		return resBody as BizECommerceRegistrationRes;
	}
}

export default BizUserIntegration;
