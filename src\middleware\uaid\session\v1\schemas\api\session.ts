import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../../shared/schemas/api/responses.schema';
import { actionResponseSchema } from '../../../../util/schemas/responses';

export const createSessionResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			SessionId: t.String({
				examples: [
					'0446d64e4cc03e07302ad70259c04dc642c2dcfa65dae6bea02dfe190f7b40c5'
				]
			})
		})
	},
	{ description: 'Session Id is successfully created.' }
);

export type CreateSessionRes = Static<typeof createSessionResSchema>;

export const deleteSessionResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: actionResponseSchema
	},
	{ description: 'Successfully Delete session.' }
);

export type DeleteSessionRes = Static<typeof deleteSessionResSchema>;
