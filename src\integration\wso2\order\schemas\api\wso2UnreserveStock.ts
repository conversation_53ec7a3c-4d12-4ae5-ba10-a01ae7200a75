import { type Static, t } from 'elysia';

// Schema for the UnreserveOrderRequest object

const wso2UnreserveStockReqSchema = t.Object({
	Request: t.Object({
		Reservation_No: t.String(),
		Frontend_Order_Id: t.String(),
		Item_Line_ID: t.String(),
		Reason: t.String(),
		Unreserved_By: t.String()
	})
});

export type Wso2UnreserveStockReq = Static<typeof wso2UnreserveStockReqSchema>;

const wso2UnreserveStockResSchema = t.Object({
	response: t.Object({
		status: t.String(),
		code: t.Number(),
		message: t.String(),
		Reservation_No: t.String(),
		Item_Line_ID: t.String(),
		Frontend_Order_Id: t.String()
	})
});

export type Wso2UnreserveStockRes = Static<typeof wso2UnreserveStockResSchema>;
