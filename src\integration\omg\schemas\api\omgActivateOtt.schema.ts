import { type Static, t } from 'elysia';

// follow the same structure as the omg api schema
export const omgActivateOttReqSchema = t.Object({
	ottPlanId: t.String(),
	accountType: t.String(),
	accountId: t.String(),
	orderRefNo: t.String(),
	ottOrder: t.Array(
		t.Object({
			ottMerchantId: t.Number(),
			ottProductId: t.String(),
			ottOmgId: t.Number(),
			ottLoginType: t.String(),
			ottUserId: t.String()
		})
	)
});

export type OmgActivateOttReq = Static<typeof omgActivateOttReqSchema>;

export const omgActivateOttResSchema = t.Object({
	responseCode: t.String(),
	responseMsg: t.String()
});

export type OmgActivateOttRes = Static<typeof omgActivateOttResSchema>;
