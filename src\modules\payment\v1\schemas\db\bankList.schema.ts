import { integer, json, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const bankListTableSchema = pgTable('bank_list', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	BankName: text('bank_name').notNull(),
	BankCode: integer('bank_code').notNull(),
	Flag: json('flag').$type<string[]>().notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectBankList = typeof bankListTableSchema.$inferSelect;

export type InsertBankList = typeof bankListTableSchema.$inferInsert;
