import { type Static, t } from 'elysia';

export const omgNetflixCancelPlanReqSchema = t.Object({
	accountType: t.String(),
	accountId: t.String(),
	ottTxnId: t.String(),
	netflixTxnId: t.String()
});

export type OmgNetflixCancelPlanReq = Static<
	typeof omgNetflixCancelPlanReqSchema
>;

export const omgNetflixCancelPlanResponseSchema = t.Object({
	responseCode: t.Number(),
	responseMsg: t.String(),
	responseMsgDetail: t.Optional(t.String())
});

export type OmgNetflixCancelPlanRes = Static<
	typeof omgNetflixCancelPlanResponseSchema
>;
