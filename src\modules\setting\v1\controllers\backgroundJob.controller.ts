import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import { Elysia } from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import {
	type BaseResponse,
	baseResponseSchema,
	errorBaseResponseSchema
} from '../../../../shared/schemas/api/responses.schema';
import BackgroundJob from '../services/backgroundJob.service';

const backgroundJobV1Routes = new Elysia({ prefix: '/background-job' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			BackgroundJob: new BackgroundJob(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/taas/account',
		async (ctx): Promise<BaseResponse> => {
			return await ctx.BackgroundJob.triggerTaasServiceDetailsUpdate();
		},
		{
			response: {
				200: baseResponseSchema,
				500: errorBaseResponseSchema
			},
			detail: {
				description:
					'Trigger background job to retrieve service account details from TaaS and update the database.<br><br><b>Backend System:</b> TaaS <br><b>Table:</b> taas_service_details',
				tags: ['Setting']
			}
		}
	);

export default backgroundJobV1Routes;
