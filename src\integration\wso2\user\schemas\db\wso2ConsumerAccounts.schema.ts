import {
	integer,
	json,
	pgTable,
	text,
	timestamp,
	uniqueIndex
} from 'drizzle-orm/pg-core';
import type { Wso2CustomerAccountRes } from '../api/wso2CustomerAccount.schema';

export const wso2ConsumerAccountsTableSchema = pgTable(
	'wso2_consumer_accounts',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
		IdType: text('id_type').notNull(),
		IdValue: text('id_value').notNull(),
		LightweightData: json('lightweight_data').$type<Wso2CustomerAccountRes>(),
		NonLightweightData: json(
			'non_lightweight_data'
		).$type<Wso2CustomerAccountRes>(),
		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	},
	table => [
		uniqueIndex('consumerIdTypeIdValueUniqueIndex').on(
			table.IdType,
			table.IdValue
		)
	]
);
