import { randomUUID } from 'node:crypto';
import bearer from '@elysiajs/bearer';
import <PERSON>sia from 'elysia';
import { getIdTokenInfo } from '../../../../middleware/uaid/util/utils';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type GetAutopayDetailsRes,
	getAutopayDetailsReqSchema,
	getAutopayDetailsResSchema
} from '../schemas/api/autopayDetails.schema';
import {
	type AutopaySettingRes,
	autopaySettingReqSchema,
	autopaySettingResSchema
} from '../schemas/api/autopaySetting.schema';
import {
	type CheckSrStatusRes,
	checkSrStatusReqSchema,
	checkSrStatusResSchema
} from '../schemas/api/autopaySrStatus.schema';
import {
	type BankListRes,
	bankListResSchema
} from '../schemas/api/bankList.schema';
import {
	type AddBankRes,
	addBankReqSchema,
	addBankResSchema
} from '../schemas/api/newBank.schema';
import Autopay from '../services/autopay.service';

const autopayV1Routes = new Elysia({ prefix: '/autopay' })
	.use(bearer())
	.resolve(async ctx => {
		const idTokenInfo = await getIdTokenInfo(ctx.bearer);
		return {
			Autopay: new Autopay(randomUUID(), idTokenInfo)
		};
	})
	.get(
		'/bank-list',
		async (ctx): Promise<BankListRes> => {
			return await ctx.Autopay.getBankList();
		},
		{
			detail: {
				description:
					'Get a list of bank supported by autopay. <br><br> <b>Table:</b> bank_list',
				tags: ['Payment']
			},
			response: {
				200: bankListResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/bank-list',
		async (ctx): Promise<AddBankRes> => {
			const res = await ctx.Autopay.addBank(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Add a bank details for autopay usage. <br><br> <b>Table:</b> bank_list',
				tags: ['Payment']
			},
			body: addBankReqSchema,
			response: {
				201: addBankResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/setting',
		async (ctx): Promise<AutopaySettingRes> => {
			const res = await ctx.Autopay.autopaySetting(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Register, Modify or Terminate the bank card information in autopay setting. The autopay setting is supported for ICP and NOVA system only. <br><br> <b>Backend System:</b> NOVA & ICP <br> <b>Table:</b> autopay_setting_history',
				tags: ['Payment']
			},
			body: autopaySettingReqSchema,
			response: {
				201: autopaySettingResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/sr-status',
		async (ctx): Promise<CheckSrStatusRes> => {
			return await ctx.Autopay.checkSrStatus(ctx.query);
		},
		{
			detail: {
				description:
					'Verify the status of Service Request (SR) before allowing the customer to add, modify or terminate the bank card information in autopay setting. <br><br> <b>Backend System:</b> NOVA & ICP',
				tags: ['Payment']
			},
			query: checkSrStatusReqSchema,
			response: {
				200: checkSrStatusResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.get(
		'/details',
		async (ctx): Promise<GetAutopayDetailsRes> => {
			return await ctx.Autopay.getAutopayDetails(ctx.query.EncryptedBillAccNo);
		},
		{
			detail: {
				description:
					'Get autopay details for the given bill account. <br><br> <b>Backend System:</b> NOVA & ICP <br> <b>Table:</b> autopay_setting_history',
				tags: ['Payment']
			},
			query: getAutopayDetailsReqSchema,
			response: {
				200: getAutopayDetailsResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default autopayV1Routes;
