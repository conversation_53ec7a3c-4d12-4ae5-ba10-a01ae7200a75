import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const decisionLookupReqSchema = t.Object({
	DecisionName: t.String(),
	Inputs: t.Record(t.String(), t.Any())
});

export type DecisionLookupReq = Static<typeof decisionLookupReqSchema>;

export const decisionLookupResSchema = t.Object({
	...baseResponseSchema.properties,
	Result: t.Optional(t.Record(t.String(), t.Any()))
});

export type DecisionLookupRes = Static<typeof decisionLookupResSchema>;
