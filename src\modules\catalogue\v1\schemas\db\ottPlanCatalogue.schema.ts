import {
	boolean,
	integer,
	json,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

const ottDetailsSchema = t.Object({
	Id: t.String()
});

export type OttDetails = Static<typeof ottDetailsSchema>;

export const ottDetailsListSchema = t.Array(ottDetailsSchema);

export type OttDetailsList = Static<typeof ottDetailsListSchema>;

export const ottPlanCatalogueTableSchema = pgTable('ott_plan_catalogue', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	PlanId: text('plan_id').notNull().unique(),
	PlanType: text('plan_type').notNull(),
	AllowActivation: boolean('allow_activation').notNull(),
	OttPlanName: text('ott_plan_name').notNull(),
	OttBundleImageUrl: text('ott_bundle_image_url'),
	OttSwapGroup: text('ott_swap_group'),
	OttSelectionCustChoice: json('ott_selection_cust_choice')
		.$type<OttDetailsList>()
		.notNull(),
	OttSelectionFixed: json('ott_selection_fixed')
		.$type<OttDetailsList>()
		.notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectOttPlanCatalogue =
	typeof ottPlanCatalogueTableSchema.$inferSelect;
