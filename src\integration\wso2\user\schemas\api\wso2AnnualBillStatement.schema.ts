import { type Static, t } from 'elysia';

const wso2AnnualBillStatementReqSchema = t.Object({
	requestHeader: t.Object({ requestId: t.String(), eventName: t.String() }),
	accountNo: t.String(),
	year: t.String()
});

export type Wso2AnnualBillStatementReq = Static<
	typeof wso2AnnualBillStatementReqSchema
>;

export const wso2AnnualBillStatementResSchema = t.Nullable(
	t.Object({
		responseHeader: t.Object({
			rqUuid: t.String(),
			requestId: t.String(),
			status: t.String(),
			statusCode: t.String(),
			errorCode: t.String(),
			errorMessage: t.Nullable(t.String()),
			errorDetail: t.Nullable(t.String()),
			errorPayload: t.Nullable(t.String())
		}),
		tmArchivalResponse: t.Object({
			returnCode: t.Optional(t.String()),
			returnMessage: t.Optional(t.String()),
			accountNo: t.Optional(t.Union([t.String(), t.Number()])),
			statement: t.Object({
				year: t.Nullable(t.Number()),
				billDate: t.Nullable(t.String()),
				url: t.Nullable(t.String())
			})
		})
	})
);

export type Wso2AnnualBillStatementRes = Static<
	typeof wso2AnnualBillStatementResSchema
>;
