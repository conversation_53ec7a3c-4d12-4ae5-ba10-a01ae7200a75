import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const tnpsRateLovTableSchema = pgTable('tnps_rate_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	BoundType: text('bound_type').notNull(),
	Question: text('question').notNull(),
	Reason: text('reason').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectTnpsRateLov = typeof tnpsRateLovTableSchema.$inferSelect;
