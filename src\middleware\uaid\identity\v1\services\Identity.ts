import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { pinoLog } from '../../../../../config/pinoLog.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import ProfileHelper from '../../../profile/v1/helpers/ProfileHelper';
import type { ProfileData } from '../../../profile/v1/schemas/api/profile';
import { getEncryptedKeyValue, sha256 } from '../../../util/encryption';
import type { EncryptedKeyValueData } from '../../../util/schemas/encryption';
import CredentialHelper from '../helpers/CredentialHelper';
import IdentityHelper from '../helpers/IdentityHelper';
import type { GetCredentials } from '../schemas/api/credential';
import type {
	DeleteProfileRes,
	FetchIdentityReq,
	ForgotLoginIdReq,
	ForgotLoginIdRes,
	IdentityRes,
	SelectIdentityApi,
	SignUpReq,
	SignUpRes,
	UpdateProfileReq,
	UpdateProfileRes
} from '../schemas/api/identity';
import {
	type SelectIdentity,
	identificationDbSchema,
	identityDbSchema
} from '../schemas/models/identity';

class Identity {
	private db: NodePgDatabase;
	private integrationId: string;
	private identityHelper: IdentityHelper;
	private profileHelper: ProfileHelper;
	private credentialHelper: CredentialHelper;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.identityHelper = new IdentityHelper(integrationId);
		this.profileHelper = new ProfileHelper(integrationId);
		this.credentialHelper = new CredentialHelper(integrationId);
	}

	async fetchIdentity(req: FetchIdentityReq): Promise<IdentityRes> {
		const credentialValue: string =
			await this.credentialHelper.filterCredentialValue(
				req.CredentialValue,
				req.CredentialType
			);

		const credentialKey = sha256(
			req.CredentialType,
			credentialValue
		).toString();

		const res: IdentityRes = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {} as SelectIdentityApi
		};

		// get user-profile by credentialKey
		const profileRes: ProfileData =
			await this.profileHelper.getUserProfileByCredential(credentialKey);

		// populate userinfo obj if profile exist
		res.Response = await this.identityHelper.getUserInfoDetails(profileRes);
		return res;
	}

	async updateIdentity(
		userId: string,
		req: UpdateProfileReq
	): Promise<UpdateProfileRes> {
		// check existing profile by UserId
		const userInfo: ProfileData =
			await this.profileHelper.getUserProfileByUserId(userId);

		const existingCredentials =
			await this.credentialHelper.getCredentialsByUserId(
				userInfo[0].mw_identity.UserId
			);

		let isDataResubmited = true;
		let isIdNumberExist = false;

		if (req.Name && userInfo[0].mw_identity.Name !== req.Name) {
			isDataResubmited = false;
			if (userInfo[0].mw_identity.UpdateCount === 3)
				throw new UE_ERROR(
					'Unable to proceed: Maximum of 3 updates per User reached',
					StatusCodeEnum.LIMIT_EXCEEDED_ERROR,
					{ integrationId: this.integrationId, response: null }
				);
		}

		if (req.IdType && req.IdValue) {
			const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
				req.IdValue,
				req.IdType
			);

			if (userInfo[0].mw_identity.IdKey !== IdData.key) {
				const existingIdKey: SelectIdentity[] = await this.db
					.select()
					.from(identityDbSchema)
					.where(eq(identityDbSchema.IdKey, IdData.key))
					.limit(1);

				isIdNumberExist = existingIdKey.length > 0;
				isDataResubmited = false;
			}
		}

		if (req.CredentialType && req.CredentialValue) {
			let IdKey = '';
			if (req.IdNumber) {
				const IdType = userInfo[0].mw_identification?.IdType ?? 'New NRIC';
				IdKey = await this.identityHelper.hashIdNumber(IdType, req.IdNumber);
			}
			if (!req.IdNumber || IdKey !== userInfo[0].mw_identity.IdKey)
				throw new UE_ERROR(
					'ID Number is required/invalid',
					StatusCodeEnum.UNPROCESSABLE_ENTITY,
					{
						integrationId: this.integrationId,
						response: null
					}
				);

			const credential: string =
				await this.credentialHelper.filterCredentialValue(
					req.CredentialValue,
					req.CredentialType
				);
			const credentialKey = sha256(req.CredentialType, credential).toString();

			// run limit-exceeded checks
			if (
				existingCredentials.some(
					cred => cred.Type === req.CredentialType && cred.UpdateCount === 3
				)
			) {
				throw new UE_ERROR(
					'Unable to proceed: Maximum of 3 updates per User reached',
					StatusCodeEnum.LIMIT_EXCEEDED_ERROR,
					{ integrationId: this.integrationId, response: null }
				);
			}

			// run duplicate checks if the submitted credential is different with existing/current
			if (
				!existingCredentials.some(
					cred =>
						cred.Key === credentialKey &&
						cred.Type === req.CredentialType &&
						cred.IsVerified
				)
			) {
				await this.credentialHelper.checkDuplicateCredential(
					req.CredentialType,
					credentialKey
				);
				isDataResubmited = false;
			}
		}

		const res: UpdateProfileRes = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'OK',
				Message:
					'Proceed for TAC Verification to confirm Profile Update process'
			}
		};

		if (isDataResubmited) {
			res.Response.Action = 'NO_CHANGE';
			res.Response.Message =
				'No updates were made as the submitted data is unchanged.';
		}

		if (isIdNumberExist) {
			res.Success = true;
			res.Code = 200;
			res.Response.Action = 'ID_NUMBER_EXISTED';
			res.Response.Message =
				'The ID Number has been taken (registered) by other User';
		}

		return res;
	}

	async deleteIdentity(userId: string): Promise<DeleteProfileRes> {
		// check existing profile by UserId
		const profileRes: ProfileData =
			await this.profileHelper.getUserProfileByUserId(userId);

		try {
			await this.db
				.delete(identityDbSchema)
				.where(eq(identityDbSchema.UserId, userId));

			if (profileRes[0].mw_identity.IdKey) {
				await this.db
					.delete(identificationDbSchema)
					.where(
						eq(identificationDbSchema.IdKey, profileRes[0].mw_identity.IdKey)
					);
			}
		} catch (error) {
			pinoLog.error('Error upon account deletion: ', error);
			throw new UE_ERROR('Failed to delete', StatusCodeEnum.NOT_FOUND_ERROR, {
				integrationId: this.integrationId,
				response: error
			});
		}

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'DELETED',
				Message: 'Identity/profile was successfully deleted'
			}
		};
	}

	async registerNewProfile(req: SignUpReq): Promise<SignUpRes> {
		const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
			req.IdValue,
			req.IdType
		);

		let credentials: GetCredentials[] = [];
		let isIdNumberExisted = false;

		const existedIdNumber: SelectIdentity[] = await this.db
			.select()
			.from(identityDbSchema)
			.where(eq(identityDbSchema.IdKey, IdData.key))
			.limit(1);

		if (existedIdNumber.length > 0) {
			isIdNumberExisted = true;
			credentials = await this.credentialHelper.getCredentialsByIdNumber(
				IdData.key
			);
		}

		const email: string = await this.credentialHelper.filterCredentialValue(
			req.Email,
			'email'
		);
		const emailCredKey = sha256('email', email).toString();
		await this.credentialHelper.checkDuplicateCredential('email', emailCredKey);

		const mobileNumber: string =
			await this.credentialHelper.filterCredentialValue(
				req.MobileNumber,
				'mobile'
			);
		const mobileCredKey = sha256('mobile', mobileNumber).toString();
		await this.credentialHelper.checkDuplicateCredential(
			'mobile',
			mobileCredKey
		);

		const res: SignUpRes = {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'OK',
				Message: 'Proceed for TAC Verification to confirm Sign Up process'
			}
		};

		if (isIdNumberExisted) {
			res.Response.Action = 'ID_NUMBER_EXISTED';
			res.Response.Message =
				'The ID Number has been taken/registered by other User';
			res.Response.Credentials = credentials;
		}

		return res;
	}

	async forgotLoginId(req: ForgotLoginIdReq): Promise<ForgotLoginIdRes> {
		const IdData: EncryptedKeyValueData = await getEncryptedKeyValue(
			req.IdValue,
			req.IdType
		);

		const credentials: GetCredentials[] =
			await this.credentialHelper.getCredentialsByIdNumber(IdData.key);

		if (credentials.length === 0)
			throw new UE_ERROR(
				'Profile does not exist',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId, response: null }
			);

		return {
			Success: true,
			Code: 200,
			IntegrationId: this.integrationId,
			Response: {
				Action: 'ID_NUMBER_EXISTED',
				Message: 'List of credentials for the provided ID number.',
				Credentials: credentials
			}
		};
	}

	// POC: UE - UAID Integration by token to decode IdType and IdValue
	// async decodeIdentification(bearerToken: string): Promise<GetId> {
	// 	const encodedToken = bearerToken.replace(/Bearer\s+/i, '');
	// 	return this.identityHelper.decodeIdentificationToken(encodedToken);
	// }
}
export default Identity;
