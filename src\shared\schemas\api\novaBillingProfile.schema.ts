import { type Static, t } from 'elysia';

export const novaAccountBillingAddressSchema = t.Nullable(
	t.Object({
		AddressId: t.Optional(t.String({ examples: ['1'] })),
		AddressType: t.Optional(t.String({ examples: ['LANDED'] })),
		UnitLot: t.<PERSON>(t.String({ examples: ['A-9-38'] })),
		FloorNo: t.Optional(t.String({ examples: ['9'] })),
		BuildingName: t.Optional(
			t.String({ examples: ['FTTH BLOK A KONDO PETAL'] })
		),
		StreetType: t.String({ examples: ['JALAN'] }),
		StreetName: t.String({ examples: ['TANJONG RAKIT 2'] }),
		Section: t.String({ examples: ['TANJONG RAKIT'] }),
		City: t.String({ examples: ['SHAH ALAM'] }),
		Postcode: t.String({ examples: ['68000'] }),
		State: t.String({ examples: ['SELANGOR'] }),
		Country: t.String({ examples: ['MALAYSIA'] })
	})
);

export type NovaAccountBillingAddress = Static<
	typeof novaAccountBillingAddressSchema
>;

const novaExtractedBillingProfileSchema = t.Object({
	AccountName: t.String(),
	AccountEmail: t.String(),
	AccountContactNo: t.String(),
	AccountAddress: novaAccountBillingAddressSchema
});

export type NovaIcpBillingProfile = Static<
	typeof novaExtractedBillingProfileSchema
>;
