import { type Static, t } from 'elysia';

export const wso2EasyfixTnpsSurveyReqSchema = t.Object({
	requestHeader: t.Object({
		eventName: t.String({ examples: ['evOEasyFixInitialSurveyResult'] }),
		requestId: t.Optional(
			t.String({
				examples: ['123e4567-e89b-12d3-a456-************'],
				description: 'Random UUID'
			})
		)
	}),
	surveyAPIRequest: t.Object({
		surveyDetails: t.Object({
			surveyPoint: t.String({ examples: ['Open ticket'] }),
			surveyCreatedDateTime: t.Optional(
				t.String({ examples: ['2024-05-15 12:00:00'] })
			),
			customerName: t.String({ examples: ['JOHN DOE'] }),
			customerEmail: t.String({ examples: ['<EMAIL>'] }),
			customerContactNumber: t.String({ examples: ['+60123456789'] }),
			customerServiceId: t.String({ examples: ['94312806'] }),
			customerUnifiUsername: t.String({ examples: ['test@unifi'] }),
			npsRating: t.String({ examples: ['5'] }),
			npsCategory: t.String({ examples: ['Detractors'] }),
			comment: t.Optional(t.String()),
			surveyAddProperties: t.Object(
				{
					reason1: t.String({ examples: [''] }),
					comment1: t.String({
						examples: ['This is a test comment'],
						description: 'Customer comments normally goes in here.'
					}),
					reason2: t.String({ examples: [''] }),
					comment2: t.String({ examples: [''] }),
					reason3: t.String({ examples: [''] }),
					comment3: t.String({ examples: [''] })
				},
				{
					description:
						'The attributes are required to exist but allowed to have empty strings.'
				}
			)
		})
	})
});

export type Wso2EasyfixTnpsSurveyReq = Static<
	typeof wso2EasyfixTnpsSurveyReqSchema
>;

export const wso2EasyfixTnpsSurveyResSchema = t.Object({
	responseHeader: t.Object({
		rqUuid: t.String({
			examples: ['urn:uuid:123e4567-e89b-12d3-a456-************']
		}),
		requestId: t.String({
			examples: ['123e4567-e89b-12d3-a456-************']
		}),
		status: t.String({ examples: ['Success'] }),
		statusCode: t.String({ examples: ['0'] }),
		errorCode: t.MaybeEmpty(t.String()),
		errorMessage: t.MaybeEmpty(t.String()),
		errorDetail: t.MaybeEmpty(t.String()),
		errorPayload: t.MaybeEmpty(t.String())
	})
});

export type Wso2EasyfixTnpsSurveyRes = Static<
	typeof wso2EasyfixTnpsSurveyResSchema
>;
