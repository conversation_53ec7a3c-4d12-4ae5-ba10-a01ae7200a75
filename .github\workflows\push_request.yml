name: Universal Engine Push Request Workflow

on:
  push:
    branches:
      - '**' # matches all branches
      - '!tags/**' # excludes tags
      - '!refs/tags/**' # excludes tag references

env:
  HUSKY: 0
  PROJECT: 'unifi'
  APP_NAME: 'universal-engine'

# Linux x64 self-hosted runner
jobs:
  validate-branch:
    if: ${{ !contains(github.ref, 'main') }}
    runs-on: [self-hosted, Linux, x64]
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use BunJS
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: 'latest'

      - name: Check Branch Name
        run: |
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          if [[ "$BRANCH_NAME" =~ ${{vars.DEV_BRANCH_REGEX}} ]]; then
            echo "'$BRANCH_NAME' is following standard for short-lived development branch."
            exit 0
          elif [[ "$BRANCH_NAME" =~ ${{vars.ENV_BRANCH_REGEX}} ]]; then
            echo "'$BRANCH_NAME' is following standard for an environment branch."
            exit 0
          elif [[ "$BRANCH_NAME" =~ ${{vars.RELEASE_BRANCH_REGEX}} ]]; then
            echo "'$BRANCH_NAME' is following standard for a release branch."
            exit 0
          elif [[ "$BRANCH_NAME" =~ ${{vars.REVERT_BRANCH_REGEX}} ]]; then
            echo "'$BRANCH_NAME' is following standard for a revert branch."
            exit 0
          else 
            echo "'$BRANCH_NAME' is not following standard branch name."
            exit 1
          fi

      - name: Check for Merge Commits
        if: ${{ (contains(github.ref, 'feature') || contains(github.ref, 'bugfix') || contains(github.ref, 'hotfix'))}}
        run: |
          # Get current branch name
          CURRENT_BRANCH=${GITHUB_REF#refs/heads/}

          # Get default branch name
          DEFAULT_BRANCH=$(git remote show origin | grep 'HEAD branch' | cut -d' ' -f5)
          echo "Default branch is: $DEFAULT_BRANCH"

          # Count merge commits directly
          TOTAL_MERGE_COMMITS=$(git rev-list --count --merges "origin/$DEFAULT_BRANCH..origin/$CURRENT_BRANCH")

          if [ "$TOTAL_MERGE_COMMITS" -eq 0 ]; then
            echo '✅ This branch has no merge commits and is following standard'
            exit 0
          else
            echo "❌ Found $TOTAL_MERGE_COMMITS merge commit(s):"
            git log --merges "origin/$DEFAULT_BRANCH..origin/$CURRENT_BRANCH" --oneline
            exit 1
          fi

      - name: Check Branch Status
        run: |
          COUNTS=$(git rev-list --left-right --count origin/main...origin/${GITHUB_REF#refs/heads/})
          BEHIND=$(echo "$COUNTS" | awk '{print $1}')

          if [ $BEHIND -ne 0 ]; then
            echo "❌ Branch is $BEHIND commit(s) behind main"
            exit 1
          fi

      - name: Quality Check
        run: |
          bun install
          bun lint
          bun precleanup

  build-and-push:
    runs-on: [self-hosted, Linux, x64]
    if: ${{ (startsWith(github.ref, 'refs/heads/sit/') || startsWith(github.ref, 'refs/heads/uat/') || startsWith(github.ref, 'refs/heads/preprod/') || startsWith(github.ref, 'refs/heads/staging/') || startsWith(github.ref, 'refs/heads/release/') || github.ref == 'refs/heads/main')}} 
    needs: [validate-branch]
    environment:
      name: ${{github.ref == 'refs/heads/main' && 'main' || startsWith(github.ref, 'refs/heads/sit/') && 'sit' || startsWith(github.ref, 'refs/heads/uat/') && 'uat' || startsWith(github.ref, 'refs/heads/preprod/') && 'preprod' || startsWith(github.ref, 'refs/heads/staging/') && 'staging' || 'release'}}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Use BunJS
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: 'latest'

      - name: Configure Build Environment
        run: |
          # Get base version from package.json
          BASE_VERSION=$(bun -e "console.log(require('./package.json').version)")

          if [[ "$GITHUB_REF" == "refs/heads/main" ]]; then
            echo "VERSION=${BASE_VERSION}" >> $GITHUB_ENV
          else
            # Extract environment and date from branch name
            BRANCH_PREFIX=$(echo ${GITHUB_REF#refs/heads/} | cut -d'/' -f1)
            BRANCH_DATE=$(echo ${GITHUB_REF#refs/heads/} | grep -oE '[0-9]{4}-[0-9]{2}-[0-9]{2}' || date +'%Y%m%d')

            # Convert date format from YYYY-MM-DD to YYYYMMDD
            BRANCH_DATE=${BRANCH_DATE//-/}

            echo "BRANCH_PREFIX=${BRANCH_PREFIX}" >> $GITHUB_ENV
            echo "VERSION=${BASE_VERSION}-${BRANCH_PREFIX}-${BRANCH_DATE}" >> $GITHUB_ENV
          fi

      - name: Setup Docker environment
        run: |
          # Create docker config with auth
          echo '{
            "auths": {
              "${{ vars.HARBOR_REGISTRY }}": {
                "auth": "'$(echo -n "${{ secrets.HARBOR_ROBOT_TM_USERNAME }}:${{ secrets.HARBOR_ROBOT_TM_PASSWORD }}" | base64)'"
              }
            }
          }' > $HOME/.docker/config.json

          # Set proper permissions
          chmod 600 $HOME/.docker/config.json

      - name: Docker Login
        run: |
          echo '${{ secrets.HARBOR_ROBOT_TM_PASSWORD }}' | docker login ${{ vars.HARBOR_REGISTRY }} \
            -u '${{ secrets.HARBOR_ROBOT_TM_USERNAME }}' \
            --password-stdin

      - name: Clean up Docker
        run: docker system prune -f --volumes

      - name: Delete Old Images
        if: ${{ !contains(github.ref, 'main') }}
        run: |
          # List all images before deletion
          echo "Docker images before deletion:"
          if docker images | grep -q "${{env.APP_NAME}}.*${{ env.BRANCH_PREFIX }}-"; then
            docker images | grep "${{env.APP_NAME}}" | grep "${{ env.BRANCH_PREFIX }}-"
          else
            echo "No ${{ env.BRANCH_PREFIX }} images found"
          fi

          # Delete images by environment
          images_to_delete=$(docker images | grep "${{env.APP_NAME}}" | grep "${{ env.BRANCH_PREFIX }}-" | awk '{print $1":"$2}') || true
          if [ ! -z "$images_to_delete" ]; then
            echo "Deleting images:"
            echo "$images_to_delete"
            echo "$images_to_delete" | xargs docker rmi -f
          fi

          # Verify deletion
          echo "Remaining images:"
          if docker images | grep "${{env.APP_NAME}}" | grep "${{ env.BRANCH_PREFIX }}-"; then
            docker images | grep "${{env.APP_NAME}}" | grep "${{ env.BRANCH_PREFIX }}-"
          else
            echo "No ${{env.APP_NAME}} images remain"
          fi

      - name: Build and Push
        run: |
          # Construct full image names
          VERSION_TAG="${{ vars.HARBOR_REGISTRY }}/${{env.PROJECT}}/${{env.APP_NAME}}:${{ env.VERSION }}"       
      
          echo "Building image: ${VERSION_TAG}"
      
          # Build the image with environment-specific args
          DOCKER_BUILDKIT=1 docker build \
            --progress=plain \
            --platform linux/amd64 \
            --build-arg ENVIRONMENT=${{ vars.ENVIRONMENT }} \
            --build-arg WHITELIST_CREDENTIALS="${{ vars.WHITELIST_CREDENTIALS }}" \
            --build-arg TV_PACK_MONTH_WAIVER="${{ vars.TV_PACK_MONTH_WAIVER }}" \
            --build-arg TV_PACK_WAIVER="${{ vars.TV_PACK_WAIVER }}" \
            --build-arg AUTOTEST_NUM="${{ secrets.AUTOTEST_NUM }}" \
            --build-arg AUTOTEST_TAC="${{ secrets.AUTOTEST_TAC }}" \
            --build-arg AXIOM_DATASET="${{ secrets.AXIOM_DATASET }}" \
            --build-arg AXIOM_TOKEN="${{ secrets.AXIOM_TOKEN }}" \
            --build-arg CLOUD_CONNECT_CLIENT_SECRET="${{ secrets.CLOUD_CONNECT_CLIENT_SECRET }}" \
            --build-arg DYNA_TRACE_NUMBER="${{ secrets.DYNA_TRACE_NUMBER }}" \
            --build-arg DYNA_TRACE_TAC="${{ secrets.DYNA_TRACE_TAC }}" \
            --build-arg ECOMMERCE_PASSWORD="${{ secrets.ECOMMERCE_PASSWORD }}" \
            --build-arg INTERNAL_API_KEY="${{ secrets.INTERNAL_API_KEY }}" \
            --build-arg JWT_SECRET="${{ secrets.JWT_SECRET }}" \
            --build-arg OMG_X_API_KEY="${{ secrets.OMG_X_API_KEY }}" \
            --build-arg OSES_APP_TXN_PASS="${{ secrets.OSES_APP_TXN_PASS }}" \
            --build-arg OSES_CMC_SMS_TXN_PASS="${{ secrets.OSES_CMC_SMS_TXN_PASS }}" \
            --build-arg OSES_DEFAULT_TXN_PASS="${{ secrets.OSES_DEFAULT_TXN_PASS }}" \
            --build-arg OSES_PORTAL_BILL_TXN_PASS="${{ secrets.OSES_PORTAL_BILL_TXN_PASS }}" \
            --build-arg OSES_SMS_TXN_PASS="${{ secrets.OSES_SMS_TXN_PASS }}" \
            --build-arg SALT_KEY="${{ secrets.SALT_KEY }}" \
            --build-arg SCALAR_PASSWORD="${{ secrets.SCALAR_PASSWORD }}" \
            --build-arg SCALAR_USERNAME="${{ secrets.SCALAR_USERNAME }}" \
            --build-arg SECRET_KEY="${{ secrets.SECRET_KEY }}" \
            --build-arg WERAS_CLIENT_SECRET="${{ secrets.WERAS_CLIENT_SECRET }}" \
            --build-arg WERAS_PASSWORD="${{ secrets.WERAS_PASSWORD }}" \
            --build-arg WERAS_SALT_KEY="${{ secrets.WERAS_SALT_KEY }}" \
            --build-arg WERAS_SECRET_KEY="${{ secrets.WERAS_SECRET_KEY }}" \
            --build-arg WSO2_BASIC_AUTH="${{ secrets.WSO2_BASIC_AUTH }}" \
            --build-arg WSO2_KCI_API_KEY="${{ secrets.WSO2_KCI_API_KEY }}" \
            --build-arg X_API_KEY="${{ secrets.X_API_KEY }}" \
            --build-arg TAAS_APIM_BASIC_AUTH="${{ secrets.TAAS_APIM_BASIC_AUTH }}" \
            -t ${VERSION_TAG} .
      
          # Push both tags
          echo "Pushing images to harbor..."
          docker push ${VERSION_TAG}
      
      - name: Display Harbor Docker Images
        if: ${{ !contains(github.ref, 'main') }}
        run: docker images | grep "${{env.APP_NAME}}" | grep "${{ env.BRANCH_PREFIX }}-" || true

      - name: Logout from Dev Harbor
        if: always()
        run: docker logout ${{ vars.HARBOR_REGISTRY }}

# MacOS ARM64 self-hosted runner
# MacOS runner is maintained to support the CI/CD as a backup solution.
# If the Linux runner is not available, the MacOS runner will be used.
# jobs:        
#   validate-branch:
#     runs-on: [self-hosted, macOS, ARM64]
#     if: ${{ !contains(github.ref, 'main') }}
#     steps:
#       - name: Checkout Code
#         uses: actions/checkout@v4
#         with:
#           fetch-depth: 0

#       - name: Use BunJS
#         uses: oven-sh/setup-bun@v1
#         with:
#           bun-version: 'latest'

#       - name: Check Branch Name
#         run: |
#           BRANCH_NAME=${GITHUB_REF#refs/heads/}
#           if [[ "$BRANCH_NAME" =~ ${{vars.DEV_BRANCH_REGEX}} ]]; then
#             echo "'$BRANCH_NAME' is following standard for short-lived development branch."
#             exit 0
#           elif [[ "$BRANCH_NAME" =~ ${{vars.ENV_BRANCH_REGEX}} ]]; then
#             echo "'$BRANCH_NAME' is following standard for an environment branch."
#             exit 0
#           elif [[ "$BRANCH_NAME" =~ ${{vars.RELEASE_BRANCH_REGEX}} ]]; then
#             echo "'$BRANCH_NAME' is following standard for a release branch."
#             exit 0
#           elif [[ "$BRANCH_NAME" =~ ${{vars.REVERT_BRANCH_REGEX}} ]]; then
#             echo "'$BRANCH_NAME' is following standard for a revert branch."
#             exit 0
#           else 
#             echo "'$BRANCH_NAME' is not following standard branch name."
#             exit 1
#           fi

#       - name: Check for Merge Commits
#         if: ${{ (contains(github.ref, 'feature') || contains(github.ref, 'bugfix') || contains(github.ref, 'hotfix'))}}
#         run: |
#           # Get current branch name
#           CURRENT_BRANCH=${GITHUB_REF#refs/heads/}

#           # Get default branch name
#           DEFAULT_BRANCH=$(git remote show origin | grep 'HEAD branch' | cut -d' ' -f5)
#           echo "Default branch is: $DEFAULT_BRANCH"

#           # Count merge commits directly
#           TOTAL_MERGE_COMMITS=$(git rev-list --count --merges "origin/$DEFAULT_BRANCH..origin/$CURRENT_BRANCH")

#           if [ "$TOTAL_MERGE_COMMITS" -eq 0 ]; then
#             echo '✅ This branch has no merge commits and is following standard'
#             exit 0
#           else
#             echo "❌ Found $TOTAL_MERGE_COMMITS merge commit(s):"
#             git log --merges "origin/$DEFAULT_BRANCH..origin/$CURRENT_BRANCH" --oneline
#             exit 1
#           fi

#       - name: Check Branch Status
#         run: |
#           COUNTS=$(git rev-list --left-right --count origin/main...origin/${GITHUB_REF#refs/heads/})
#           BEHIND=$(echo "$COUNTS" | awk '{print $1}')

#           if [ $BEHIND -ne 0 ]; then
#             echo "❌ Branch is $BEHIND commit(s) behind main"
#             exit 1
#           fi

#       - name: Quality Check
#         run: |
#           bun install
#           bun lint

#   build-and-push:
#     runs-on: [self-hosted, macOS, ARM64]
#     if: ${{ (startsWith(github.ref, 'refs/heads/sit/') || startsWith(github.ref, 'refs/heads/uat/') || startsWith(github.ref, 'refs/heads/preprod/') || startsWith(github.ref, 'refs/heads/staging/') || startsWith(github.ref, 'refs/heads/release/') || github.ref == 'refs/heads/main')}} 
#     needs: [validate-branch]    
#     steps:
#       - name: Checkout Code
#         uses: actions/checkout@v4

#       - name: Configure Build Environment
#         run: |
#           # Get base version from package.json
#           BASE_VERSION=$(node -p "require('./package.json').version")

#           if [[ "$GITHUB_REF" == "refs/heads/main" ]]; then
#             echo "ENVIRONMENT=production" >> $GITHUB_ENV
#             echo "VERSION=${BASE_VERSION}" >> $GITHUB_ENV
#             echo "REGISTRY=${{ vars.PROD_REGISTRY }}" >> $GITHUB_ENV
#           else
#             # Extract environment and date from branch name
#             ENVIRONMENT=$(echo ${GITHUB_REF#refs/heads/} | cut -d'/' -f1)
#             BRANCH_DATE=$(echo ${GITHUB_REF#refs/heads/} | grep -oE '[0-9]{4}-[0-9]{2}-[0-9]{2}' || date +'%Y%m%d')

#             # Convert date format from YYYY-MM-DD to YYYYMMDD
#             BRANCH_DATE=${BRANCH_DATE//-/}

#             echo "ENVIRONMENT=${ENVIRONMENT}" >> $GITHUB_ENV
#             echo "VERSION=${BASE_VERSION}-${ENVIRONMENT}-${BRANCH_DATE}" >> $GITHUB_ENV
#             echo "REGISTRY=${{ vars.DEV_REGISTRY }}" >> $GITHUB_ENV
#           fi

#       - name: Setup Docker environment
#         run: |
#           # Create docker config with auth
#           echo '{
#             "auths": {
#               "${{ env.REGISTRY }}": {
#                 "auth": "'$(echo -n "${{ secrets.HARBOR_ROBOT_TM_USERNAME }}:${{ secrets.HARBOR_ROBOT_TM_PASSWORD }}" | base64)'"
#               }
#             }
#           }' > $HOME/.docker/config.json

#           # Set proper permissions
#           chmod 600 $HOME/.docker/config.json

#       - name: Docker Login
#         run: |
#           echo '${{ secrets.HARBOR_ROBOT_TM_PASSWORD }}' | docker login ${{ env.REGISTRY }} \
#             -u '${{ secrets.HARBOR_ROBOT_TM_USERNAME }}' \
#             --password-stdin

#       - name: Clean up Docker
#         run: docker system prune -f --volumes

#       - name: Delete Old Images
#         if: ${{ !contains(github.ref, 'main') }}
#         run: |
#           # List all images before deletion
#           echo "Docker images before deletion:"
#           if docker images | grep -q "${{env.APP_NAME}}.*${{ env.ENVIRONMENT }}-"; then
#             docker images | grep "${{env.APP_NAME}}" | grep "${{ env.ENVIRONMENT }}-"
#           else
#             echo "No ${{ env.ENVIRONMENT }} images found"
#           fi

#           # Delete images by environment
#           images_to_delete=$(docker images | grep "${{env.APP_NAME}}" | grep "${{ env.ENVIRONMENT }}-" | awk '{print $1":"$2}') || true
#           if [ ! -z "$images_to_delete" ]; then
#             echo "Deleting images:"
#             echo "$images_to_delete"
#             echo "$images_to_delete" | xargs docker rmi -f
#           fi

#           # Verify deletion
#           echo "Remaining images:"
#           if docker images | grep "${{env.APP_NAME}}" | grep "${{ env.ENVIRONMENT }}-"; then
#             docker images | grep "${{env.APP_NAME}}" | grep "${{ env.ENVIRONMENT }}-"
#           else
#             echo "No ${{env.APP_NAME}} images remain"
#           fi

#       - name: Build and Push
#         run: |
#           # Construct full image names
#           VERSION_TAG="${{ env.REGISTRY }}/${{env.PROJECT}}/${{env.APP_NAME}}:${{ env.VERSION }}"       

#           echo "Building image: ${VERSION_TAG}"

#           # Build the image with environment-specific args
#           DOCKER_BUILDKIT=1 docker build \
#             --progress=plain \
#             --platform linux/amd64 \
#             --build-arg ENVIRONMENT=${{ env.ENVIRONMENT }} \
#             -t ${VERSION_TAG} .
            
#           # Push both tags
#           echo "Pushing images to harbor..."
#           docker push ${VERSION_TAG}

#       - name: Display Harbor Docker Images
#         if: ${{ !contains(github.ref, 'main') }}
#         run: docker images | grep "${{env.APP_NAME}}" | grep "${{ env.ENVIRONMENT }}-" || true

#       - name: Logout from Dev Harbor
#         if: always()
#         run: docker logout ${{ env.REGISTRY }}
