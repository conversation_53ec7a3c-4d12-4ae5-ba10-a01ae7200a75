import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const addOnsAppointmentSlotReqSchema = t.Object({
	EncryptedBillAccNo: t.String({
		examples: ['AQXXNlX/6xEICGVek3neU1S2TyzXe7JVa2GdVjhw=']
	}),
	AccountNo: t.String({ examples: ['2-3C0OPTX'] }),
	ContactId: t.String({ examples: ['2-3C0OPU4'] }),
	DeviceName: t.String({ examples: ['MESH Wi-Fi 6 RC RM20/24M - 1'] }),
	PartNumber: t.String({ examples: ['PR009185'] })
});

export type AddOnsAppointmentSlotReq = Static<
	typeof addOnsAppointmentSlotReqSchema
>;

export const addOnsAppointmentSlotResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			RNORegion: t.String({ examples: ['ZONE BATU PAHAT'] }),
			OrderId: t.String({ examples: ['MW6-10000'] }),
			AppointmentSlots: t.Array(
				t.Object({
					AppointmentId: t.Nullable(
						t.String({ examples: ['S2A-191212E9E554'] })
					),
					SlotStart: t.Nullable(
						t.String({ examples: ['12/13/2019 10:00:00'] })
					),
					SlotEnd: t.Nullable(t.String({ examples: ['12/13/2019 10:00:00'] }))
				})
			)
		})
	},
	{
		description: 'Appointment slot successfully retrieved.'
	}
);

export type AddOnsAppointmentSlotRes = Static<
	typeof addOnsAppointmentSlotResSchema
>;

export const appointmentSlotHelperResSchema = t.Object({
	OrderId: t.String({
		examples: ['MW6-10000']
	}),
	RNORegion: t.String({
		examples: ['ZONE BATU PAHAT']
	}),
	AppointmentSlots: t.Array(
		t.Object({
			AppointmentId: t.Nullable(t.String({ examples: ['S2A-191212E9E554'] })),
			SlotStart: t.Nullable(t.String({ examples: ['12/13/2019 10:00:00'] })),
			SlotEnd: t.Nullable(t.String({ examples: ['12/13/2019 10:00:00'] }))
		})
	)
});

export type AppointmentSlotHelperRes = Static<
	typeof appointmentSlotHelperResSchema
>;
