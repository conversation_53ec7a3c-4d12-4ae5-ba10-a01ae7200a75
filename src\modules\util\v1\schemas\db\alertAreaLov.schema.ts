import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const alertAreaLovTableSchema = pgTable('alert_area_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Name: text('name').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAlertAreaLov = typeof alertAreaLovTableSchema.$inferSelect;
