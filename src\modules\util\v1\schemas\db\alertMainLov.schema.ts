import {
	boolean,
	integer,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const alertMainTableSchema = pgTable('alert_main', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	AlertTime: timestamp('alert_time', { mode: 'date' }).defaultNow().notNull(),
	AlertTitle: text('alert_title').notNull(),
	AlertCauseId: integer('alert_cause_id').notNull(),
	Ettr: timestamp('ettr', { mode: 'date' }).defaultNow().notNull(),
	AdditionalRemarks: text('additional_remarks').notNull(),
	AlertStatusId: integer('alert_status_id').notNull(),
	Displayed: boolean('displayed').default(true).notNull(),
	CreatedBy: text('created_by').notNull(),
	UpdatedBy: text('updated_by').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAlertMain = typeof alertMainTableSchema.$inferSelect;
