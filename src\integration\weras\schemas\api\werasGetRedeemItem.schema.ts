import { type Static, t } from 'elysia';

export const werasGetRedeemItemReqSchema = t.Object({
	itemId: t.Number({
		description: 'Unique identifier for the item',
		example: 3737
	}),
	customerId: t.Number({
		description: 'Unique identifier for the customer',
		example: ********
	}),
	quantity: t.Number({
		description: 'Quantity of the item to be redeemed',
		example: 1
	}),
	accountId: t.Nullable(
		t.Integer({
			description:
				'Account Unique ID obtained from “Get Customer Bills” API call. This parameter is required for bill rebate redemption.',
			example: 987654
		})
	)
});

export type WerasGetRedeemItemReq = Static<typeof werasGetRedeemItemReqSchema>;

export const werasRedeemItemDataSchema = t.Object({
	error: t.Optional(t.String()),
	message: t.Optional(t.String()),
	reward_name: t.Optional(t.String()),
	itemId: t.Optional(t.Number()),
	points_redeemed: t.Optional(t.Number()),
	points_balance: t.Optional(t.Number()),
	quantity: t.Optional(t.Number()),
	code: t.Optional(t.String()),
	expiry_date: t.Optional(t.String()),
	item_type: t.Optional(t.String())
});

export const werasGetRedeemItemResSchema = t.Object({
	status: t.Boolean(),
	code: t.Number(),
	data: t.Optional(werasRedeemItemDataSchema)
});

export type WerasGetRedeemItemRes = Static<typeof werasGetRedeemItemResSchema>;
