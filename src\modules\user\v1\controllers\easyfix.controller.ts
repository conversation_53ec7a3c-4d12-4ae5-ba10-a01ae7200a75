import { randomUUID } from 'node:crypto';
import { Elysia } from 'elysia';
import { wso2NovaCreateCustomerSchema } from '../../../../integration/wso2/record/schemas/api/wso2NovaCreateCustomer.schema';
import { wso2NovaRetrieveCustomerReqSchema } from '../../../../integration/wso2/record/schemas/api/wso2NovaRetrieveCustomer.schema';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	easyfixNovaCreateCustomerResSchema,
	easyfixNovaRetrieveCustomerResSchema
} from '../schemas/api/easyfix.schema';
import Easyfix from '../services/easyfix.service';

const easyfixV1Routes = new Elysia({ prefix: '/easyfix' })
	.resolve(() => {
		return {
			Easyfix: new Easyfix(randomUUID())
		};
	})
	.post(
		'/nova/customer',
		async ctx => {
			const res = await ctx.Easyfix.createNovaCustomer(ctx.body);
			ctx.set.status = res.Code;
			return res;
		},
		{
			detail: {
				description:
					'Create customer information for NOVA-related issues for Easyfix usage.</br><br><b>Backend System:</b> NOVA SIEBEL',
				tags: ['User']
			},
			body: wso2NovaCreateCustomerSchema,
			response: {
				201: easyfixNovaCreateCustomerResSchema,
				500: errorBaseResponseSchema
			}
		}
	)
	.post(
		'/nova/customer/details',
		async ctx => {
			return ctx.Easyfix.getNovaCustomer(ctx.body);
		},
		{
			detail: {
				description:
					'Retrieve a customer’s Fiber (NOVA) details for Easyfix usage.<br><br><b>Backend System:</b> NOVA SIEBEL',
				tags: ['User']
			},
			body: wso2NovaRetrieveCustomerReqSchema,
			response: {
				200: easyfixNovaRetrieveCustomerResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export default easyfixV1Routes;
