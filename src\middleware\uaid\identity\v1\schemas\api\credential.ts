import { type Static, t } from 'elysia';
import { CredentialTypeEnum } from '../../../../../../enum/user.enum';

const getCredentialsSchema = t.Object({
	Id: t.String(),
	Type: t.String({ examples: ['mobile'] }),
	Key: t.String({
		examples: [
			'680aje0d9cc158d1f1ce112c262d4cdf485a3a6a3cbca712c9a73e216be14957'
		]
	}),
	MaskedValue: t.String({ examples: ['601234****89'] }),
	IsPrimary: t.Nullable(t.<PERSON>()),
	IsUnifiNumber: t.Nullable(t.<PERSON>()),
	IsVerified: t.Nullable(t.<PERSON>()),
	UpdateCount: t.Integer()
});

export type GetCredentials = Static<typeof getCredentialsSchema>;

export const credentialsResSchema = t.Object({
	Id: t.String({
		examples: [
			'0x244417f956ac7c599f191593f7e441a4fafa20a4158fd52e154f1dc4c8ed92'
		]
	}),
	Type: t.String({ examples: ['mobile'] }),
	MaskedValue: t.String({ examples: ['601234****89'] }),
	IsVerified: t.Nullable(t.Boolean({ examples: true }))
});

export const credentialSchema = t.Object({
	Id: t.Optional(
		t.String({
			minLength: 64,
			examples: [
				'6480d9c50338d9d4d92568be7a11aa4de53226d3bb4e5bf63df9652be1eeda0f'
			]
		})
	),
	CredentialType: t.Optional(t.Enum(CredentialTypeEnum)),
	CredentialValue: t.Optional(
		t.String({
			minLength: 10,
			examples: ['0123456789', '<EMAIL>']
		})
	)
});

export type CredentialSchema = Static<typeof credentialSchema>;

export const decryptedCredentialSchema = t.Nullable(
	t.Object({
		Name: t.String({ examples: ['John Doe'] }),
		Email: t.String({ examples: ['<EMAIL>'] }),
		IsEmailVerified: t.Boolean(),
		Mobile: t.String({ examples: ['601199999999'] }),
		IsMobileVerified: t.Boolean()
	})
);

export type DecryptedCredentialInfo = Static<typeof decryptedCredentialSchema>;
