import { randomUUID } from 'node:crypto';
import Elysia from 'elysia';
import { errorBaseResponseSchema } from '../../../../shared/schemas/api/responses.schema';
import {
	type LovListRes,
	type TnpsRateLovRes,
	lovListReqSchema,
	lovListResSchema,
	tnpsRateLovReqSchema,
	tnpsRateLovResSchema
} from '../schemas/api/lov.schema';
import Lov from '../services/lov.service';

export const privateLovV1Routes = new Elysia({ prefix: '/lov' })
	.resolve(() => {
		return {
			Lov: new Lov(randomUUID())
		};
	})
	.get(
		'/list',
		async (ctx): Promise<LovListRes> => {
			return await ctx.Lov.getLovList(ctx.query);
		},
		{
			detail: {
				description:
					'Retrieve list of LOVs. SystemName is required for Type "Terminate" and "Complaint". Kindly refer to request and response section for more details. <br><br> <b>Table: </b> termination_lov, complaint_lov, address_lov',
				tags: ['Util']
			},
			query: lovListReqSchema,
			response: {
				200: lovListResSchema,
				500: errorBaseResponseSchema
			}
		}
	);

export const protectedLovV1Routes = new Elysia({ prefix: '/lov' })
	.resolve(() => {
		return {
			Lov: new Lov(randomUUID())
		};
	})
	.get(
		'/tnps-rate',
		async (ctx): Promise<TnpsRateLovRes> => {
			return await ctx.Lov.getTnpsRateLov(ctx.query.BoundType);
		},
		{
			detail: {
				description:
					'Retrieve list of TNPS question and reason LOVs. Kindly refer to request and response section for more details. <br><br> <b>Table: </b> tnps_rate_lov',
				tags: ['Util']
			},
			query: tnpsRateLovReqSchema,
			response: {
				200: tnpsRateLovResSchema,
				500: errorBaseResponseSchema
			}
		}
	);
