import { format, toZonedTime } from 'date-fns-tz';
import { eq } from 'drizzle-orm';
import { getDbInstance } from '../../../config/db.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { CredentialTypeEnum, IdTypeEnum } from '../../../enum/user.enum';
import { getMyTimeZoneDate } from '../../../shared/common';
import { UE_ERROR } from '../../error';
import type { DecryptedCredentialInfo } from '../identity/v1/schemas/api/credential';
import { credentialDbSchema } from '../identity/v1/schemas/models/credential';
import {
	identificationDbSchema,
	identityDbSchema
} from '../identity/v1/schemas/models/identity';
import {
	sessionDbSchema,
	userAuthDbSchema
} from '../session/v1/schemas/models/session';
import { decrypt } from './encryption';
import type { IdTokenInfo } from './schemas/idTokenInfo.schema';

export function getDatetime(interval: number): string {
	const currentDateTimeInMY = getMyTimeZoneDate();

	// Set expiry time to 5 minutes from now
	const expiredAt = new Date(
		currentDateTimeInMY.getTime() + interval * 60 * 1000
	);

	// Convert expiryAt to Malaysia Time (MYT)
	const timeZone = 'Asia/Kuala_Lumpur';
	const expiredAtInMYT = toZonedTime(expiredAt, timeZone);

	// Format expiryAt as string
	return format(expiredAtInMYT, 'd MMM yyyy, h:mm:ss a', {
		timeZone
	});
}

export function getExpiredDate(interval: number): string {
	const currentDateTimeInMY = getMyTimeZoneDate();

	// Set expiry time to {interval} minutes from now
	const expiredAtWithInterval = new Date(
		currentDateTimeInMY.getTime() + interval * 60 * 1000
	);

	return dateFormatter(expiredAtWithInterval);
}

// Sample output: "2025-03-07 00:52:48"
export function dateFormatter(dateToDisplay: Date): string {
	const myDate = new Date(`${dateToDisplay}`);
	const formatter = new Intl.DateTimeFormat('en-GB', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		second: '2-digit',
		hour12: false,
		timeZone: 'Asia/Kuala_Lumpur'
	});
	const parts = formatter.formatToParts(myDate);

	return `${parts[4].value}-${parts[2].value}-${parts[0].value} ${parts[6].value}:${parts[8].value}:${parts[10].value}`;
}

/**
 * Get ID Token Info
 * - Retrieves the ID Token information from the database.
 * - Decrypts the ID Token value and returns the information.
 */
export async function getIdTokenInfo(
	encodedToken: string
): Promise<IdTokenInfo> {
	// get from cache 1st (if exist)
	// const cacheName = `${CacheKeyEnum.UAID_ID_TOKEN_INFO}-${encodedToken}`;
	// const cache = await getCache(cacheName);
	// if (cache) {
	// 	return JSON.parse(cache) as IdTokenInfo;
	// }

	const [getIdRes] = await getDbInstance()
		.select()
		.from(identificationDbSchema)
		.where(eq(identificationDbSchema.IdKey, encodedToken));

	// If Identification not-exist, exit and return error
	if (getIdRes === undefined) {
		throw new UE_ERROR(
			'Token expired or invalid!',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}

	if (
		getIdRes.IdValue === null ||
		getIdRes.IdType === null ||
		getIdRes.IdKey === null
	) {
		throw new UE_ERROR(
			'Invalid credentials! You are not authorized!',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}

	if (getIdRes.IsIdVerified === null || getIdRes.IsIdVerified === false) {
		throw new UE_ERROR(
			'The user has not verified the service, please verify the service to continue.',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}

	let decryptedIdValue = await decrypt(getIdRes.IdValue, getIdRes.IdKey);

	if (
		getIdRes.IdType === IdTypeEnum.NEW_NRIC &&
		!decryptedIdValue.includes('-')
	)
		decryptedIdValue = decryptedIdValue.replace(
			/^(\d{6})(\d{2})(\d{4})$/,
			'$1-$2-$3'
		);

	const res: IdTokenInfo = {
		IdType: getIdRes.IdType,
		IdValue: decryptedIdValue,
		IsIdVerified: getIdRes.IsIdVerified,
		CreatedAt: `${getIdRes.CreatedAt}`,
		UpdatedAt: `${getIdRes.UpdatedAt}`
	};

	// Set cache for 24 hrs
	// await setCache(cacheName, JSON.stringify(res), 86400);

	return res;
}

/**
 * Get Decrypted Credential Info
 * - Retrieves the Credential information from the database.
 * - Decrypts the Credential value and returns the information.
 */
export async function getDecryptedCredentialInfo(
	sessionId: string
): Promise<DecryptedCredentialInfo> {
	// get from cache 1st (if exist)
	// const cacheName = `${CacheKeyEnum.UAID_ID_TOKEN_INFO}-${encodedToken}`;
	// const cache = await getCache(cacheName);
	// if (cache) {
	// 	return JSON.parse(cache) as IdTokenInfo;
	// }

	const [getUserIdRes] = await getDbInstance()
		.select({ UserId: userAuthDbSchema.UserId })
		.from(sessionDbSchema)
		.innerJoin(
			userAuthDbSchema,
			eq(userAuthDbSchema.Id, sessionDbSchema.UserAuthId)
		)
		.where(eq(sessionDbSchema.Id, sessionId))
		.limit(1);

	// If Session not-exist, exit and return error
	if (getUserIdRes === undefined || getUserIdRes.UserId === null) {
		throw new UE_ERROR(
			'Session expired or invalid!',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}

	const getCredentialRes = await getDbInstance()
		.select({
			Name: identityDbSchema.Name,
			CredentialKey: credentialDbSchema.CredentialKey,
			CredentialType: credentialDbSchema.CredentialType,
			CredentialValue: credentialDbSchema.CredentialValue,
			IsVerified: credentialDbSchema.IsVerified
		})
		.from(credentialDbSchema)
		.innerJoin(
			identityDbSchema,
			eq(credentialDbSchema.UserId, identityDbSchema.UserId)
		)
		.where(eq(identityDbSchema.UserId, getUserIdRes.UserId));

	if (
		getCredentialRes == null ||
		(Array.isArray(getCredentialRes) && getCredentialRes.length === 0)
	) {
		throw new UE_ERROR(
			'Invalid credentials! You are not authorized!',
			StatusCodeEnum.UNAUTHORIZED_ERROR
		);
	}

	const name = getCredentialRes[0].Name ?? '';
	let decryptedEmail = '';
	let decryptedMobile = '';
	let IsEmailVerified = false;
	let IsMobileVerified = false;

	const decryptedCredentials = await Promise.all(
		getCredentialRes.map(async item => {
			let decryptedValue = null;
			if (item.CredentialValue && item.CredentialKey) {
				decryptedValue = await decrypt(
					item.CredentialValue,
					item.CredentialKey
				);
			}

			return {
				...item,
				DecryptedValue: decryptedValue
			};
		})
	);

	decryptedEmail =
		decryptedCredentials.find(
			item => item.CredentialType === CredentialTypeEnum.EMAIL
		)?.DecryptedValue ?? '';
	IsEmailVerified =
		decryptedCredentials.find(
			item => item.CredentialType === CredentialTypeEnum.EMAIL
		)?.IsVerified ?? false;

	decryptedMobile =
		decryptedCredentials.find(
			item => item.CredentialType === CredentialTypeEnum.MOBILE
		)?.DecryptedValue ?? '';
	IsMobileVerified =
		decryptedCredentials.find(
			item => item.CredentialType === CredentialTypeEnum.MOBILE
		)?.IsVerified ?? false;

	const res: DecryptedCredentialInfo = {
		Name: name,
		Email: decryptedEmail,
		IsEmailVerified: IsEmailVerified,
		Mobile: decryptedMobile,
		IsMobileVerified: IsMobileVerified
	};

	// Set cache for 24 hrs
	// await setCache(cacheName, JSON.stringify(res), 86400);

	return res;
}
