import { type Static, t } from 'elysia';

export const bizAccessTokenSchema = t.Object({
	accessToken: t.MaybeEmpty(t.String()),
	expiresInSeconds: t.MaybeEmpty(t.Number())
});
export type BizAccessToken = Static<typeof bizAccessTokenSchema>;

const bizCCRegistrationReqSchema = t.Object({
	packageName: t.String(),
	planName: t.String(),
	monthlyFee: t.Number(),
	tenantName: t.String(),
	contactIdNumber: t.Nullable(t.String()),
	tenantPhoneNumber: t.String(),
	adminEmail: t.String(),
	firstName: t.String(),
	lastName: t.String(),
	companyOwnerName: t.String(),
	tenantRegistrationNumber: t.String(),
	tenantTierName: t.String(),
	domainName: t.String(),
	customerIDType: t.String(),
	segmentGroup: t.String(),
	segmentSubGroup: t.String(),
	industryCode: t.String(),
	brnFormat: t.String(),
	brnType: t.String(),
	relationshipCode: t.String(),
	contractingParty: t.String(),
	foreignTelco: t.String(),
	preferredLanguage: t.String(),
	billEmailAddress: t.String(),
	addressType: t.String(),
	unitNumber: t.String(),
	floorNumber: t.String(),
	block: t.String(),
	buildingName: t.String(),
	streetName: t.String(),
	streetType: t.String(),
	section: t.String(),
	postCode: t.Nullable(t.String()),
	city: t.String(),
	state: t.String(),
	country: t.String(),
	serviceId: t.String(),
	commissionToId: t.String(),
	commissionToName: t.String(),
	orderNumber: t.String()
});
export type BizCCRegistrationReq = Static<typeof bizCCRegistrationReqSchema>;

export const bizCCRegistrationResSchema = t.Object({
	referenceNumber: t.MaybeEmpty(t.String()),
	orderNumber: t.MaybeEmpty(t.String()),
	portalGeneratedOrderNumber: t.MaybeEmpty(t.String()),
	errors: t.MaybeEmpty(t.String())
});
export type BizCCRegistrationRes = Static<typeof bizCCRegistrationResSchema>;
