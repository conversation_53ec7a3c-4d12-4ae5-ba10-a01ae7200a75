import { type Static, t } from 'elysia';

/**
 * Used by Temporal client to submit a previously prepared OTT Activation or Swap Order
 */
export const temporalSubmitOttOrderReqSchema = t.Object({
	OrderId: t.String({
		description: 'Order reference number to be submitted to OMG',
		example: 'OTT-2025052900001'
	})
});

export type TemporalSubmitOttOrderReq = Static<
	typeof temporalSubmitOttOrderReqSchema
>;
