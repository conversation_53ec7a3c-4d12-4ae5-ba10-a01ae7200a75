import {
	boolean,
	decimal,
	integer,
	json,
	pgTable,
	text
} from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

const ottPackageDetailsSchema = t.Array(t.String());
export type OttPackageDetails = Static<typeof ottPackageDetailsSchema>;

export const ottServicesTableSchema = pgTable('ott_services', {
	Id: integer('id').primaryKey(),
	OttName: text('ott_name').notNull(),
	OttIsActive: boolean('ott_is_active').notNull(),
	OttMerchantId: integer('ott_merchant_id').notNull(),
	OttSequence: integer('ott_sequence'),
	OttUniversalLink: text('ott_universal_link'),
	OttIconPath: text('ott_icon_path'),
	OttLoginType: text('ott_login_type'),
	OttLoginInstruction: text('ott_login_instruction'),
	OttDescription: text('ott_description'),
	OttVerificationInstruction: text('ott_verification_instruction'),
	OtVideoUrl: text('ott_video_url'),
	OttActivationLink: text('ott_activation_link'),
	OttPrice: decimal('ott_price', { precision: 10, scale: 2 }),
	OttOmgId: integer('ott_omg_id'),
	OttProductId: text('ott_product_id'),
	OttPackageType: text('ott_package_type'),
	OttPackageDetails: json('ott_package_details').$type<OttPackageDetails>(),
	OttPackageDuration: text('ott_package_duration'),
	TmBundleId: text('tm_bundle_id'),
	TmProductDesc: text('tm_product_desc'),
	NetflixBundleId: text('netflix_bundle_id'),
	NetflixOfferId: text('netflix_offer_id')
});

export type OttServices = typeof ottServicesTableSchema.$inferSelect;
