import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import randomString from 'random-string-gen';
import { getDbInstance } from '../../../../config/db.config';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { TemporalNamespaces } from '../../../../enum/temporal.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { TemporalTriggerWorkflowRes } from '../../../../integration/temporal/schemas/api/temporal.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import {
	type SelectNonOrderableTxnHistory,
	nonOrderableTxnHistoryTableSchema
} from '../../../order/v1/schemas/db/nonOrderableTxnHistory.schema';
import type {
	CreateSlofOrderPostLoginReq,
	CreateSlofOrderPostLoginRes,
	CreateSlofOrderPreLoginReq,
	CreateSlofOrderPreLoginRes
} from '../schemas/api/slof.schema';

class Slof {
	private db: NodePgDatabase;
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo?: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo?: IdTokenInfo) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async createSlofOrderPrelogin(
		body: CreateSlofOrderPreLoginReq
	): Promise<CreateSlofOrderPreLoginRes> {
		const orderData = body.OrderData ?? {};

		if (!orderData.OrderId) {
			orderData.OrderId = `${body.OrderPrefix ?? ''}-${randomString(
				5
			).toUpperCase()}`;
		}

		const [orderTxn]: SelectNonOrderableTxnHistory[] = await this.db
			.insert(nonOrderableTxnHistoryTableSchema)
			.values({
				OrderPrefix: body.OrderPrefix ?? '',
				BillingAccountNo: body.BillingAccountNo ?? '',
				IdType: this.idTokenInfo?.IdType ?? body.IdType ?? '',
				IdValue: this.idTokenInfo?.IdValue ?? body.IdValue ?? '',
				FullName: body.FullName ?? '',
				Email: body.Email ?? '',
				MobileNo: body.MobileNo ?? '',
				ServiceId: body.ServiceId ?? '',
				Category: body.Category ?? '',
				ProductName: body.ProductName ?? '',
				SystemName: body.SystemName ?? '',
				Source: body.Source ?? '',
				Segment: body.Segment ?? '',
				OrderStatus: 'SUBMITTED',
				OrderData: orderData,
				SubOrderData: body.SubOrderData ?? '',
				ErrorMessage: ''
			})
			.returning();

		if (!orderTxn) {
			throw new UE_ERROR(
				'Failed to create non-orderable order',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const triggerRes: TemporalTriggerWorkflowRes =
			await this.mwIntegration.TemporalIntegration.triggerWorkflow({
				OrderId: orderData.OrderId,
				IsOrderable: false,
				Namespace: TemporalNamespaces.IRENEW
			});

		if (!triggerRes?.Success) {
			throw new UE_ERROR(
				'Temporal trigger workflow failed',
				StatusCodeEnum.TEMPORAL_ERROR,
				{ integrationId: this.integrationId, response: triggerRes }
			);
		}

		const response: CreateSlofOrderPreLoginRes = {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			Message: 'Order submitted successfully',
			IntegrationId: this.integrationId,
			Response: Object.fromEntries(
				Object.entries(body).map(([key, value]) => [key, String(value ?? '')])
			)
		};

		return response;
	}
	async createSlofOrderPostlogin(
		body: CreateSlofOrderPostLoginReq
	): Promise<CreateSlofOrderPostLoginRes> {
		const orderData = body.OrderData ?? {};

		if (!orderData.OrderId) {
			orderData.OrderId = `${body.OrderPrefix ?? ''}-${randomString(
				5
			).toUpperCase()}`;
		}

		if (!this.idTokenInfo) {
			throw new UE_ERROR(
				'User authentication required',
				StatusCodeEnum.UNAUTHORIZED_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const [orderTxn]: SelectNonOrderableTxnHistory[] = await this.db
			.insert(nonOrderableTxnHistoryTableSchema)
			.values({
				OrderPrefix: body.OrderPrefix ?? '',
				BillingAccountNo: body.BillingAccountNo,
				IdType: this.idTokenInfo?.IdType,
				IdValue: this.idTokenInfo?.IdValue,
				FullName: body.FullName ?? '',
				Email: body.Email ?? '',
				MobileNo: body.MobileNo ?? '',
				ServiceId: body.ServiceId,
				Category: body.Category ?? '',
				ProductName: body.ProductName ?? '',
				SystemName: body.SystemName ?? '',
				Source: body.Source ?? '',
				Segment: body.Segment ?? '',
				OrderStatus: 'SUBMITTED',
				OrderData: orderData,
				SubOrderData: body.SubOrderData ?? '',
				ErrorMessage: ''
			})
			.returning();

		if (!orderTxn) {
			throw new UE_ERROR(
				'Failed to create non-orderable order',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const triggerRes: TemporalTriggerWorkflowRes =
			await this.mwIntegration.TemporalIntegration.triggerWorkflow({
				OrderId: orderData.OrderId,
				IsOrderable: false,
				Namespace: TemporalNamespaces.IRENEW
			});

		if (!triggerRes?.Success) {
			throw new UE_ERROR(
				'Temporal trigger workflow failed',
				StatusCodeEnum.TEMPORAL_ERROR,
				{ integrationId: this.integrationId, response: triggerRes }
			);
		}

		const response: CreateSlofOrderPostLoginRes = {
			Success: true,
			Code: StatusCodeEnum.CREATED,
			Message: 'Order submitted successfully',
			IntegrationId: this.integrationId,
			Response: Object.fromEntries(
				Object.entries(body).map(([key, value]) => [key, String(value ?? '')])
			)
		};

		return response;
	}
}

export default Slof;
