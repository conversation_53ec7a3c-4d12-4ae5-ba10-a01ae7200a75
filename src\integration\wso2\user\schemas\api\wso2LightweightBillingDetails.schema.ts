import { type Static, t } from 'elysia';

export const wso2BillingTrendSchema = t.Optional(
	t.MaybeEmpty(
		t.Array(
			t.Partial(
				t.Object({
					BillDate: t.MaybeEmpty(t.String()),
					TotalDue: t.MaybeEmpty(t.Number()),
					TotalPaid: t.Maybe<PERSON>mpty(t.Number())
				})
			)
		)
	)
);

export const wso2BillingHistorySchema = t.MaybeEmpty(
	t.Array(
		t.Partial(
			t.Object({
				InvoiceId: t.MaybeEmpty(t.String()),
				AccountNo: t.MaybeEmpty(t.String()),
				BillNo: t.Maybe<PERSON>mpty(t.String()),
				BillDate: t.MaybeEmpty(t.String()),
				BillDueDate: t.MaybeEmpty(t.String()),
				TotalCurrent: t.MaybeEmpty(t.String()),
				TotalTaxGST: t.MaybeEmpty(t.String()),
				TotalDue: t.MaybeEmpty(t.String()),
				TotalOutstanding: t.MaybeEmpty(t.String()),
				TotalPreviousPayment: t.MaybeEmpty(t.String()),
				TotalPrevious: t.MaybeE<PERSON>y(t.String()),
				TotalPrevAdj: t.MaybeEmpty(t.String()),
				TotalRebate: t.MaybeEmpty(t.String()),
				URL: t.MaybeEmpty(t.String())
			})
		)
	)
);

export const wso2PaymentHistorySchema = t.MaybeEmpty(
	t.Array(
		t.Partial(
			t.Object({
				AccountNo: t.MaybeEmpty(t.String()),
				PaymentAmount: t.MaybeEmpty(t.String()),
				PaymentMode: t.MaybeEmpty(t.String()),
				RecieptNumber: t.MaybeEmpty(t.String()),
				TransactionDatetime: t.MaybeEmpty(t.String()),
				PaymentMethod: t.MaybeEmpty(t.String()),
				POCDescription: t.MaybeEmpty(t.String())
			})
		)
	)
);

export const wso2CreditUtilizationSchema = t.MaybeEmpty(
	t.Partial(
		t.Object({
			AccountNo: t.MaybeEmpty(t.String()),
			CreditUtilization: t.MaybeEmpty(t.String()),
			CreditLimit: t.MaybeEmpty(t.String()),
			ExposurePct: t.MaybeEmpty(t.String()),
			DefaultCreditLimit: t.MaybeEmpty(t.String()),
			CLDeposit: t.MaybeEmpty(t.String()),
			GPMCreditLimit: t.MaybeEmpty(t.String()),
			CUDate: t.MaybeEmpty(t.String())
		})
	)
);

export const wso2LatestDueUnbilledUsageSchema = t.Optional(
	t.Partial(
		t.Object({
			OutstandingAmount: t.MaybeEmpty(t.String()),
			UnbilledAmount: t.MaybeEmpty(t.String()),
			OpenBillDue: t.MaybeEmpty(t.String()),
			PendingBillDue: t.MaybeEmpty(t.String())
		})
	)
);

export const wso2PurchaseHistorySchema = t.Optional(
	t.MaybeEmpty(
		t.Object({
			ReturnCode: t.MaybeEmpty(t.String()),
			ReturnMessage: t.MaybeEmpty(t.String()),
			ReturnTimestamp: t.MaybeEmpty(t.String()),
			SubscriberAndPurchaseDetails: t.MaybeEmpty(
				t.Array(
					t.Partial(
						t.Object({
							PurchaseId: t.MaybeEmpty(t.String()),
							ProductName: t.MaybeEmpty(t.String()),
							ProductId: t.MaybeEmpty(t.String()),
							PurchaseDate: t.MaybeEmpty(t.String()),
							EffectiveDate: t.MaybeEmpty(t.String()),
							ExpiryDate: t.MaybeEmpty(t.String()),
							OriginalChargeAmount: t.MaybeEmpty(t.String()),
							DiscountAmount: t.MaybeEmpty(t.String()),
							TotalAmount: t.MaybeEmpty(t.String()),
							MSISDN: t.MaybeEmpty(t.String()),
							PaymentStatus: t.MaybeEmpty(t.String())
						})
					)
				)
			)
		})
	)
);

export const wso2DownloadBillDetailsSchema = t.Optional(
	t.MaybeEmpty(
		t.Array(
			t.MaybeEmpty(
				t.Object({
					BillingAccountNumber: t.MaybeEmpty(t.String()),
					BillDetails: t.Array(
						t.Optional(
							t.Object({
								InvoiceNumber: t.MaybeEmpty(t.String()),
								URL: t.MaybeEmpty(t.String())
							})
						)
					)
				})
			)
		)
	)
);

export const wso2LightweightBillingDetailsResObjSchema = t.MaybeEmpty(
	t.Object({
		BillingTrend: wso2BillingTrendSchema,
		BillingHistory: wso2BillingHistorySchema,
		PaymentHistory: wso2PaymentHistorySchema,
		CreditUtilization: wso2CreditUtilizationSchema,
		LatestDueUnbilledUsage: wso2LatestDueUnbilledUsageSchema,
		PurchaseHistory: wso2PurchaseHistorySchema,
		DownloadBillDetails: wso2DownloadBillDetailsSchema
	})
);

export type Wso2LightWeightBillingDetailsResObj = Static<
	typeof wso2LightweightBillingDetailsResObjSchema
>;

export const wso2LightweightBillingDetailsResSchema = t.Optional(
	t.Nullable(
		t.Object({
			Status: t.Optional(
				t.Object({
					Type: t.Optional(t.String()),
					Code: t.Optional(t.String()),
					Message: t.Optional(t.String())
				})
			),
			Response: wso2LightweightBillingDetailsResObjSchema
		})
	)
);

export type Wso2LightWeightBillingDetailsRes = Static<
	typeof wso2LightweightBillingDetailsResSchema
>;
