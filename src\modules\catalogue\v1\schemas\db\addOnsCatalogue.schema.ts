import {
	boolean,
	doublePrecision,
	integer,
	json,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

const deviceDescriptionSchema = t.Array(t.String());
export type DeviceDescription = Static<typeof deviceDescriptionSchema>;

export const addonsCatalogueTableSchema = pgTable('addons_catalogue', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Name: text('name').unique().notNull(),
	DisplayName: text('display_name').notNull(),
	Category: text('category').notNull(),
	Tag: text('tag').notNull(),
	Description: json('description').$type<DeviceDescription>(),
	Summary: text('summary').notNull(),
	Specification: text('specification'),
	ImageUrl: text('image_url').notNull(),
	MonthlyCommitment: doublePrecision('monthly_commitment').notNull(),
	DiscountPercentage: doublePrecision('discount_percentage').notNull(),
	RRP: doublePrecision('recommended_retail_price'),
	ContractTerm: integer('contract_term').notNull(),
	PartNumber: text('part_number'),
	ProductId: text('product_id'),
	IndexNo: integer('index_no'),
	PartnerId: text('partner_id'),
	VoucherName: text('voucher_name'),
	IsBundle: boolean('is_bundle').default(false).notNull(),
	StartDate: timestamp('start_date', { mode: 'date' }).notNull(),
	EndDate: timestamp('end_date', { mode: 'date' }).notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdateAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectAddonsCatalogue =
	typeof addonsCatalogueTableSchema.$inferSelect;
