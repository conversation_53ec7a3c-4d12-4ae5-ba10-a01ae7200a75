import {
	doublePrecision,
	integer,
	json,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';
import { ottPlanCatalogueTableSchema } from './ottPlanCatalogue.schema';

const tvPackDescriptionSchema = t.Array(t.String());
export type TvPackDescription = Static<typeof tvPackDescriptionSchema>;

export const tvPackCatalogueTableSchema = pgTable('tv_pack_catalogue', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	SiebelTvPackName: text('siebel_tv_pack_name').notNull(),
	TvPackName: text('tv_pack_name').notNull(),
	PlanId: text('plan_id')
		.notNull()
		.references(() => ottPlanCatalogueTableSchema.PlanId, {
			onDelete: 'cascade'
		}),
	PlanType: text('plan_type').notNull(),
	PlanSpeed: text('plan_speed').notNull(),
	Description: json('description').$type<TvPackDescription>().notNull(),
	Summary: text('summary'),
	ImageUrl: text('image_url'),
	VideoUrl: text('video_url'),
	MonthlyCommitment: doublePrecision('monthly_commitment').notNull(),
	DiscountPercentage: doublePrecision('discount_percentage').notNull(),
	VoucherName: text('voucher_name'),
	ContractTerm: integer('contract_term').notNull(),
	PartNumber: text('part_number'),
	ProductId: text('product_id'),
	CommitmentName: text('commitment_name'),
	CommitmentPartNumber: text('commitment_part_number'),
	CommitmentProductId: text('commitment_product_id'),
	StartDate: timestamp('start_date', { mode: 'date' }).notNull(),
	EndDate: timestamp('end_date', { mode: 'date' }).notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectTvPackCatalogue =
	typeof tvPackCatalogueTableSchema.$inferSelect;

export type InsertTvPackCatalogue =
	typeof tvPackCatalogueTableSchema.$inferInsert;
