import { type Static, t } from 'elysia';

const wso2GetIbillBillingDetailsReqSchema = t.Object({
	AccountNo: t.String(),
	BillDate: t.String()
});

export type Wso2GetIbillBillingDetailsReq = Static<
	typeof wso2GetIbillBillingDetailsReqSchema
>;

export const wso2GetIbillBillingDetailsResSchema = t.Object({
	Response: t.Object({
		BillingDetails: t.Partial(
			t.Object({
				CustomerName: t.MaybeEmpty(t.String()),
				CustomerId: t.MaybeEmpty(t.String()),
				AccountNumber: t.Maybe<PERSON>mpty(t.String()),
				BillDate: t.Maybe<PERSON>mpty(t.String()),
				BillType: t.MaybeEmpty(t.String()),
				Data: t.Partial(
					t.Object({
						CurrentBillCharges: t.Object({
							BillDate: t.MaybeEmpty(t.String()),
							BillAmount: t.Maybe<PERSON>mpty(t.String()),
							PreviousBalance: t.Maybe<PERSON>mpty(t.String()),
							PreviousBalancePercent: t.<PERSON>(t.String()),
							RecurringCharges: t.MaybeEmpty(t.String()),
							RecurringChargesPercent: t.MaybeEmpty(t.String()),
							UsageCharges: t.Array(
								t.Object({
									Description: t.MaybeEmpty(t.String()),
									ChargeAmount: t.MaybeEmpty(t.String()),
									ChargeAmountPercent: t.MaybeEmpty(t.String())
								})
							),
							UsageChargesTotal: t.MaybeEmpty(t.String()),
							UsageChargesPercent: t.MaybeEmpty(t.String()),
							OtherCharges: t.MaybeEmpty(t.String()),
							OtherChargesPercent: t.MaybeEmpty(t.String()),
							ServiceTax: t.MaybeEmpty(t.String()),
							ServiceTaxPercent: t.MaybeEmpty(t.String()),
							ServiceDetails: t.Array(
								t.Object({
									ServiceDesc: t.MaybeEmpty(t.String()),
									Charges: t.Array(
										t.Object({
											Description: t.MaybeEmpty(t.String()),
											StartDate: t.MaybeEmpty(t.String()),
											EndDate: t.MaybeEmpty(t.String()),
											Period: t.MaybeEmpty(t.String()),
											ChargeAmount: t.MaybeEmpty(t.String())
										})
									)
								})
							)
						}),
						PreviousBillCharges: t.Array(
							t.Object({
								BillAmount: t.MaybeEmpty(t.String()),
								BillDate: t.MaybeEmpty(t.String())
							})
						),
						Payments: t.Array(
							t.Object({
								AmountPaid: t.MaybeEmpty(t.String()),
								PaidOn: t.MaybeEmpty(t.String())
							})
						)
					})
				),
				BillStreamCode: t.MaybeEmpty(t.String()),
				BillDeliveryDate: t.MaybeEmpty(t.String()),
				BillMedia: t.MaybeEmpty(t.String()),
				BannerTypeIndicator: t.MaybeEmpty(t.String()),
				OutstandingAmount: t.MaybeEmpty(t.String()),
				RoundingAmount: t.MaybeEmpty(t.String()),
				Address1: t.MaybeEmpty(t.String()),
				Address2: t.MaybeEmpty(t.String()),
				City: t.MaybeEmpty(t.String()),
				State: t.MaybeEmpty(t.String()),
				ZipCode: t.MaybeEmpty(t.String()),
				Country: t.MaybeEmpty(t.String())
			})
		)
	})
});

export type Wso2GetIbillBillingDetailsRes = Static<
	typeof wso2GetIbillBillingDetailsResSchema
>;
