import { type Static, t } from 'elysia';

export const werasUpdateRewardsFlagReqSchema = t.Object({
	voucherFlag: t.String(),
	redemptionId: t.Number()
});

export type WerasUpdateRewardsFlagReq = Static<
	typeof werasUpdateRewardsFlagReqSchema
>;

export const werasUpdateRewardsFlagResSchema = t.Object({
	status: t.<PERSON>({
		description: 'Indicates whether the request was successful',
		example: true
	}),
	code: t.Number({
		description: 'HTTP-like status code of the response',
		example: 200
	}),
	data: t.Object({
		message: t.String({
			description: 'Response message indicating the result of the update',
			example: 'Success'
		})
	})
});

export type WerasUpdateRewardsFlagRes = Static<
	typeof werasUpdateRewardsFlagResSchema
>;
