import { eq } from 'drizzle-orm';
import { getDbInstance } from '../../../../config/db.config';
import type { SourceEnum } from '../../../../enum/header.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import TemporalIntegration from '../../../../integration/temporal/temporal.integration';
import { UE_ERROR } from '../../../../middleware/error';
import type {
	UpdateOrderReq,
	UpdateOrderRes
} from '../../../order/v1/schemas/api/ottCallback.schema';
import OttCallback from '../../../order/v1/services/ottCallback.service';
import { temporalUserTaskTableSchema } from '../schemas/db/temporalUserTask.schema';

export class OttCallbackService {
	private db = getDbInstance();
	private integrationId: string;
	private ottCallback: OttCallback;
	private temporalIntegration: TemporalIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.ottCallback = new OttCallback(this.integrationId);
		this.temporalIntegration = new TemporalIntegration(this.integrationId);
	}

	async updateOttOrder(
		_source: SourceEnum,
		req: UpdateOrderReq
	): Promise<UpdateOrderRes> {
		// Step 1: Update orderableTxnHistoryTable
		const { order } = await this.ottCallback.handleOrderUpdateDb(req);

		// Step 2: Retrieve temporal_user_task by OrderId
		const [temporalTask] = await this.db
			.select()
			.from(temporalUserTaskTableSchema)
			.where(eq(temporalUserTaskTableSchema.OrderId, order.OrderId))
			.execute();

		if (!temporalTask) {
			throw new UE_ERROR(
				'Temporal task not found for this OrderId',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId, response: {} }
			);
		}

		// Update temporal task to completed
		await this.db
			.update(temporalUserTaskTableSchema)
			.set({
				TaskStatus: 'COMPLETED'
			})
			.where(eq(temporalUserTaskTableSchema.Id, temporalTask.Id))
			.execute();

		const workflowId = temporalTask.WorkflowId;

		// Step 3: Trigger Temporal user task signal
		await this.temporalIntegration.triggerUserTaskSignal({
			WorkflowId: workflowId
		});

		// Step 4: Return standard response
		return {
			responseCode: '200',
			responseMsg: 'Success',
			orderRefNo: req.orderRefNo
		};
	}
}
