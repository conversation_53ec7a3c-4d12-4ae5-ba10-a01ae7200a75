import {
	boolean,
	integer,
	jsonb,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';

export const netcoreActivityTxnHistoryTableSchema = pgTable(
	'netcore_activity_txn_history',
	{
		Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),

		TxnId: text('txn_id').notNull(),
		TxnType: text('txn_type').notNull(),

		RequestBody: jsonb('request_body').notNull(),
		ResponseBody: jsonb('response_body').notNull(),

		IsSuccess: boolean('is_success').notNull(),

		CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
		UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
	}
);
