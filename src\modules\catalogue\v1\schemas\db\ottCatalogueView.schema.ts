import { doublePrecision, integer, pgTable, text } from 'drizzle-orm/pg-core';
import { boolean, json } from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

const ottPackageDetailsSchema = t.Array(t.String());
export type OttPackageDetails = Static<typeof ottPackageDetailsSchema>;

export const ottCatalogueViewSchema = pgTable('ott_catalogue_view', {
	PlanId: text('plan_id').notNull(),
	PlanType: text('plan_type').notNull(),
	AllowActivation: boolean('allow_activation').notNull(),
	OttPlanName: text('ott_plan_name').notNull(),
	OttBundleImageUrl: text('ott_bundle_image_url'),
	OttSwapGroup: text('ott_swap_group'),
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	OttName: text('ott_name').notNull(),
	OttVideoUrl: text('ott_video_url'),
	OttIsActive: boolean('ott_is_active').notNull(),
	OttMerchantId: integer('ott_merchant_id').notNull(),
	OttSequence: integer('ott_sequence').notNull(),
	OttUniversalLink: text('ott_universal_link').notNull(),
	OttIconPath: text('ott_icon_path').notNull(),
	OttLoginType: text('ott_login_type').notNull(),
	OttLoginInstruction: text('ott_login_instruction'),
	OttDescription: text('ott_description'),
	OttVerificationInstruction: text('ott_verification_instruction'),
	OttActivationLink: text('ott_activation_link'),
	OttPrice: doublePrecision('ott_price').notNull(),
	OttOmgId: integer('ott_omg_id').notNull(),
	OttProductId: text('ott_product_id').notNull(),
	OttPackageType: text('ott_package_type'),
	OttPackageDetails: json('ott_package_details').$type<OttPackageDetails>(),
	OttPackageDuration: text('ott_package_duration'),
	TmBundleId: text('tm_bundle_id'),
	TmProductDesc: text('tm_product_desc'),
	NetflixBundleId: text('netflix_bundle_id'),
	NetflixOfferId: text('netflix_offer_id'),
	SelectionType: text('selection_type').notNull(),
	SelectionData: json('selection_data').notNull()
});

export type SelectOttCatalogueView = typeof ottCatalogueViewSchema.$inferSelect;
