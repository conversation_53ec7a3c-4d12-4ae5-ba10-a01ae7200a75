import { type Static, t } from 'elysia';

// Request Schema
export const mmagTrackOrdersReqSchema = t.Object({
	request: t.Object({
		General_Order_Id: t.String({ minLength: 1, maxLength: 200 })
	})
});

export type MmagTrackOrdersReq = Static<typeof mmagTrackOrdersReqSchema>;

// Nested: Tracking Entry
const mmagTrackingEntrySchema = t.Object({
	RowNo: t.Integer(),
	Tracking_No: t.String(),
	Tracking_Code: t.String(),
	Tracking_Date: t.String(),
	Tracking_Desc: t.String()
});

// Nested: Order Object
const mmagTrackingOrderSchema = t.Object({
	RowNo: t.Integer(),
	DOP_Order_No: t.String(),
	DOP_Order_Date: t.String(),
	CRM_Order_Id: t.String(),
	CRM_Order_Date: t.String(),
	CRM_Order_Type: t.String(),
	Si<PERSON>el_Order_No: t.String(),
	Frontend_Order_Id: t.String(),
	Courier_Id: t.String(),
	Courier_Name: t.String(),
	Courier_Url: t.String(),
	status: t.String(),
	code: t.Integer(),
	message: t.String(),
	tracking: t.Array(mmagTrackingEntrySchema)
});

// Main Response Schema
export const mmagTrackOrdersResSchema = t.Object({
	response: t.Object({
		status: t.String(),
		code: t.Integer(),
		message: t.String(),
		orders: t.Array(mmagTrackingOrderSchema)
	})
});

export type MmagTrackOrdersRes = Static<typeof mmagTrackOrdersResSchema>;
