import { deleteCache } from '../config/cache.config';
import { CacheKeyEnum } from '../enum/cacheKey.enum';
import type { LightweightFlagEnum } from '../enum/wso2.enum';

/** This function is used to delete the cache for the given billing account number
It's easier to define a function if the cache key is used in multiple places
since it's easier to keep track of the cache key format*/
export const deleteWso2NovaBillingProfileCache = async (
	billingAccountNo: string
) => {
	await deleteCache(
		`${CacheKeyEnum.WSO2_NOVA_BILLING_PROFILE}-${billingAccountNo}`
	);
};

export const deleteWso2CustomerAccountCache = async (
	idValue: string,
	lightweightFlag: LightweightFlagEnum
) => {
	await deleteCache(
		`${CacheKeyEnum.WSO2_CUSTOMER_ACCOUNTS}-${lightweightFlag}-${idValue}`
	);
};

export const deleteWso2LightweightBillingDetailsCache = async (
	billingAccountNo: string
) => {
	await deleteCache(
		`${CacheKeyEnum.WSO2_LIGHTWEIGHT_BILLING_DETAILS}-${billingAccountNo}`
	);
};

export const deleteLinkedAccountProfileCache = async (
	ownerBillAccNo: string
) => {
	await deleteCache(`${CacheKeyEnum.LINKED_ACCOUNT_PROFILE}-${ownerBillAccNo}`);
};
