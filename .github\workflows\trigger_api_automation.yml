name: Trigger API Automation

on:
  pull_request:
    types:
       - opened
       - synchronize
       - reopened
    
jobs:
  trigger-api-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Send repository dispatch event
        run: |
          curl -X POST -H "Accept: application/vnd.github.v3+json" \
          -H "Authorization: token ${{ secrets.AT_ACTIONS_TOKEN }}" \
          --data '{"event_type": "run-api-tests"}' \
          https://api.github.com/repos/tmberhad-unifi/AutoTest-APITesting/dispatches