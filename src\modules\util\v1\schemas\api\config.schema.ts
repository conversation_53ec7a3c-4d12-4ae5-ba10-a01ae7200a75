import { type Static, t } from 'elysia';
import { OsTypeEnum } from '../../../../../enum/lov.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const maintenancePageResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Array(
			t.Object({
				ContentId: t.String({ examples: ['Account/activity'] }),
				ContentName: t.String({ examples: ['My Activity Page'] }),
				ActionType: t.String({ examples: ['REDIRECT'] }),
				RedirectUrl: t.Nullable(
					t.String({ examples: ['URL_ADDRESS.example.com'] })
				),
				Platform: t.String({ examples: ['WEB'] }),
				Mode: t.String({ examples: ['PLANNED'] }),
				IsUp: t.<PERSON>(),
				Ettr: t.Date()
			})
		)
	},
	{ description: 'Maintenance page list successfully retrieved' }
);

export type MaintenancePageRes = Static<typeof maintenancePageResSchema>;

export const forceUpdateReqSchema = t.Object({
	OsType: t.Enum(OsTypeEnum)
});

export const forceUpdateResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Version: t.String({ examples: ['1.0.0'] }),
			ReleaseDate: t.Date(),
			OsType: t.Enum(OsTypeEnum),
			TemporaryForceUpdate: t.Nullable(t.Boolean()),
			ForceUpdate: t.Boolean(),
			CreatedAt: t.Date()
		})
	},
	{ description: 'Force update list successfully retrieved' }
);

export type ForceUpdateRes = Static<typeof forceUpdateResSchema>;
