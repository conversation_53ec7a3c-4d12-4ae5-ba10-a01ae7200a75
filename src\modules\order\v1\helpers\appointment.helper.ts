import { eq, sql } from 'drizzle-orm';
import { getDbInstance } from '../../../../config/db.config';
import { ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2MWAppointmentIDReq,
	Wso2MWAppointmentIDRes
} from '../../../../integration/wso2/order/schemas/api/wso2NovaIdReset';
import type {
	ActivityStruct,
	TmActionIntegration,
	Wso2AppointmentBookingReq,
	Wso2MWSwiftAppointmentCreateReq
} from '../../../../integration/wso2/order/schemas/api/wso2SwiftAppointment';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate } from '../../../../shared/common';
import type { AppointmentBookingReq } from '../schemas/api/addOnsOrder.schema';
import { orderableTxnHistoryTableSchema } from '../schemas/db/orderable.schema';

class AppointmentHelper {
	private mwIntegration: MwIntegration;
	private integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async bookAppointment(
		req: AppointmentBookingReq,
		servicePointId: string
	): Promise<string> {
		const activityStruct: ActivityStruct =
			await this.novaAppointmentBooking(req);

		const tmActionIntegrationRes: TmActionIntegration =
			await this.swiftAppointment(
				activityStruct,
				req.RNORegion,
				servicePointId,
				req.AppointmentId
			);

		await this.novaIdReset(
			activityStruct.ActivityId,
			req.OrderId,
			tmActionIntegrationRes.ActivityId
		);

		await getDbInstance()
			.update(orderableTxnHistoryTableSchema)
			.set({
				OrderStatus: ProgressStatusEnum.APPOINTMENT_BOOKED,
				OrderProgress: sql`order_progress::jsonb || ${JSON.stringify([
					{
						Status: ProgressStatusEnum.APPOINTMENT_BOOKED,
						Timestamp: `${getMyTimeZoneDate().toISOString()}`
					}
				])}::jsonb`,
				UpdatedAt: getMyTimeZoneDate()
			})
			.where(eq(orderableTxnHistoryTableSchema.OrderId, req.OrderId))
			.catch(err => {
				throw new UE_ERROR(
					`Encountered error while updating order progress: ${String(err)}`,
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: 'ADDON-0027'
					}
				);
			});
		return tmActionIntegrationRes.ActivityId;
	}

	private async novaAppointmentBooking(
		req: AppointmentBookingReq
	): Promise<ActivityStruct> {
		const wso2Req: Wso2AppointmentBookingReq = {
			appointmentBookingPortal: {
				RequestHeader: {
					ApptBookingStruct: {
						OrderNumber: req.OrderId,
						ExternalId: req.OrderId,
						AppointmentId: req.AppointmentId,
						AppointmentPreference: 'DOW',
						SlotStart: req.SlotStart,
						SlotEnd: req.SlotEnd,
						AddressIndicator: 'Old'
					}
				}
			}
		};

		const wso2Res =
			await this.mwIntegration.Wso2OrderIntegration.getWso2AppointmentBooking(
				wso2Req
			);

		const activityStructList = wso2Res.ReplyHeader.ActivityStruct || [];

		if (activityStructList.length === 0) {
			throw new UE_ERROR(
				'Invalid response structure from Nova Appointment - No activities found',
				StatusCodeEnum.CONFLICT,
				{
					integrationId: this.integrationId,
					response: 'APPOINTMEN-BOOKING-RES-EMPTY-NOVA'
				}
			);
		}

		return activityStructList[0];
	}

	private async swiftAppointment(
		activity: ActivityStruct,
		rnoRegion: string,
		servicePointId: string,
		appointmentId: string
	): Promise<TmActionIntegration> {
		const mwSwiftAppointmentCreateRequest: Wso2MWSwiftAppointmentCreateReq = {
			SWIFTAppointmentCreateRequest: {
				ListOfTmEaiCreateSlotActivityReq: {
					TmActionIntegration: [
						{
							Status: 'Scheduled',
							ActivityId: 'DUMMY',
							ExternalActivityId: appointmentId,
							TypeActivityType: 'Slot',
							DateTimeStart: activity.DateTimeStart,
							DateTimeEnd: activity.DateTimeEnd,
							Duration: activity.Duration,
							AssetId: servicePointId,
							ServiceRegion: rnoRegion,
							SubType: 'Installation',
							SwiftFlag: 'Y'
						}
					]
				}
			}
		};

		const wso2Res =
			await this.mwIntegration.Wso2OrderIntegration.getWso2SwiftAppointment(
				mwSwiftAppointmentCreateRequest
			);

		const responseActivities =
			wso2Res?.SWIFTAppointmentCreateResponse?.ListOfTmEaiCreateSlotActivityRes
				?.TmActionIntegration;

		if (!responseActivities || responseActivities.length === 0) {
			throw new UE_ERROR(
				'Invalid response structure from MW Swift Appointment - No activities found',
				StatusCodeEnum.CONFLICT,
				{
					integrationId: this.integrationId,
					response: 'APPOINTMEN-BOOKING-RES-EMPTY-SWIFT'
				}
			);
		}
		return responseActivities[0];
	}

	private async novaIdReset(
		activityId: string,
		orderId: string,
		newExternalActivityId: string
	): Promise<Wso2MWAppointmentIDRes> {
		const wso2Req: Wso2MWAppointmentIDReq = {
			novaActivityIDReset: {
				RequestHeader: {
					ApptIDStruct: [
						{
							ActivityId: activityId,
							CurrentExternalActivityId: orderId,
							NewExternalActivityId: newExternalActivityId
						}
					]
				}
			}
		};
		return await this.mwIntegration.Wso2OrderIntegration.getWso2NovaIdReset(
			wso2Req
		);
	}
}

export default AppointmentHelper;
