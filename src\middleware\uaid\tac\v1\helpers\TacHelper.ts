import { and, eq, gt, sql } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { getDbInstance } from '../../../../../config/db.config';
import { StatusCodeEnum } from '../../../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../../error';
import type CredentialHelper from '../../../identity/v1/helpers/CredentialHelper';
import { sha256 } from '../../../util/encryption';
import {
	type SelectTac,
	lockedAccDbSchema,
	tacDbSchema
} from '../schemas/models/tac';

class TacHelper {
	private db: NodePgDatabase;
	private integrationId: string;

	constructor(integrationId: string) {
		this.db = getDbInstance();
		this.integrationId = integrationId;
	}

	async isTacExist(credentialKey: string): Promise<SelectTac[]> {
		const existingTac: SelectTac[] = await this.db
			.select()
			.from(tacDbSchema)
			.where(
				and(
					eq(tacDbSchema.CredentialKey, credentialKey),
					gt(tacDbSchema.ExpiredAt, sql`now()`)
				)
			)
			.limit(1);

		if (existingTac.length === 0)
			throw new UE_ERROR(
				'The TAC does not exist / already expired.',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId, response: null }
			);

		return existingTac;
	}

	async isWhitelistedCredential(credentialValue: string): Promise<boolean> {
		const whitelisted: string[] =
			process.env.WHITELIST_CREDENTIALS?.split(',') ?? [];

		return whitelisted.includes(credentialValue);
	}

	async isSkipTacValidation(
		tacNumber: string | null,
		credentialValue: string
	): Promise<boolean> {
		const isWhitelisted = await this.isWhitelistedCredential(credentialValue);
		if (!isWhitelisted) return false;

		const isValidTac = tacNumber === null || tacNumber === '8888';
		if (!isValidTac) {
			throw new UE_ERROR(
				'Invalid TAC number for whitelisted credentials!',
				StatusCodeEnum.UNPROCESSABLE_ENTITY,
				{ integrationId: this.integrationId, response: null }
			);
		}
		return isValidTac;
	}

	async verifyTacNumber(
		credentialHelper: CredentialHelper,
		credentialKey: string,
		credentialValue: string,
		tacNumber: string
	): Promise<void> {
		const skipTacValidation: boolean = await this.isSkipTacValidation(
			tacNumber,
			credentialValue
		);
		if (!skipTacValidation) {
			// if TAC not exist or expired, return 404
			const existingTac: SelectTac[] = await this.isTacExist(credentialKey);

			// if credential is temporarily locked, return 423 LOCKED
			if (existingTac[0].RetryCount === 3)
				await credentialHelper.lockCredential(credentialKey);

			const tacNumberKey: string = sha256(credentialKey, tacNumber).toString();

			const verifyTacData: SelectTac[] = await this.db
				.select()
				.from(tacDbSchema)
				.where(
					and(
						eq(tacDbSchema.CredentialKey, credentialKey),
						eq(tacDbSchema.TacNumber, tacNumberKey),
						gt(tacDbSchema.ExpiredAt, sql`now()`)
					)
				)
				.limit(1);

			// if the tac matched & still valid, then delete from table
			// else, return error
			if (verifyTacData.length > 0) {
				await this.db
					.delete(tacDbSchema)
					.where(eq(tacDbSchema.CredentialKey, credentialKey));
				await this.db
					.delete(lockedAccDbSchema)
					.where(eq(lockedAccDbSchema.CredentialKey, credentialKey));
			} else {
				try {
					// plus 1 for retry-count
					await this.db
						.update(tacDbSchema)
						.set({ RetryCount: sql`${tacDbSchema.RetryCount} + 1` })
						.where(eq(tacDbSchema.CredentialKey, credentialKey));
				} catch (error) {
					throw new UE_ERROR(
						'Error update retry-count in mw_locked_acc',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: error }
					);
				}

				throw new UE_ERROR(
					'Verification failed: Invalid TacNumber. Max retry 3 times',
					StatusCodeEnum.UNPROCESSABLE_ENTITY,
					{ integrationId: this.integrationId, response: null }
				);
			}
		}
	}
}

export default TacHelper;
