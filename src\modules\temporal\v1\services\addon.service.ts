import { addDays, format } from 'date-fns';
import { AddOnsRequestCategoryEnum } from '../../../../enum/addOns.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { TemporalOrderStatus } from '../../../../enum/temporal.enum';
import {
	LightweightFlagEnum,
	SystemNameEnum
} from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type { Wso2ReserveStockRes } from '../../../../integration/wso2/order/schemas/api/wso2ReserveStock';
import type { Wso2UnreserveStockRes } from '../../../../integration/wso2/order/schemas/api/wso2UnreserveStock';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import type { BaseResponse } from '../../../../shared/schemas/api/responses.schema';
import type {
	AddOnsEligibilityReq,
	AddOnsEligibilityRes
} from '../../../eligibility/v1/schemas/api/addOns.schema';
import AddOnsEligibility from '../../../eligibility/v1/services/addOns.service';
import AppointmentHelper from '../../../order/v1/helpers/appointment.helper';
import type { ProductList as Product } from '../../../order/v1/schemas/api/addOnsOrder.schema';
import type { AddOnsOrderReq } from '../../../order/v1/schemas/api/addOnsOrder.schema';
import type { CheckStockByListRes } from '../../../order/v1/schemas/api/checkStockByList.schema';
import type { OttActivateReq } from '../../../order/v1/schemas/api/ottActivate.schema';
import type {
	SelectCustomerOrder,
	SelectOrderableTxnHistory
} from '../../../order/v1/schemas/db/orderable.schema';
import StockOrder from '../../../order/v1/services/stock.service';
import AppointmentRecordHelper from '../../../record/v1/helpers/appointment.helper';
import type {
	AddOnsAppointmentSlotReq,
	AddOnsAppointmentSlotRes
} from '../../../record/v1/schemas/api/addOns.schema';
import type {
	AddOnsReserveOrderRes,
	TemporalAddOnsOrderReq
} from '../schemas/api/addons.schema';
import type Order from './order.service';

export class AddOns {
	private order: Order;
	private stockOrder: StockOrder;
	private integrationId: string;
	private idTokenInfo: IdTokenInfo | undefined;
	private appointmentHelper: AppointmentHelper;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string, order: Order, idTokenInfo?: IdTokenInfo) {
		this.integrationId = integrationId;
		this.order = order;
		this.stockOrder = new StockOrder(integrationId);
		this.idTokenInfo = idTokenInfo;
		this.mwIntegration = new MwIntegration(this.integrationId);
		this.appointmentHelper = new AppointmentHelper(integrationId);
	}

	private validateOrderTxn(
		orderTxn: SelectOrderableTxnHistory
	): BaseResponse | null {
		if (!orderTxn) {
			throw new UE_ERROR('Order not found', StatusCodeEnum.NOT_FOUND_ERROR, {
				integrationId: this.integrationId
			});
		}

		return null;
	}

	async checkStock(body: TemporalAddOnsOrderReq): Promise<CheckStockByListRes> {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);
		this.validateOrderTxn(orderTxn);

		if (!orderTxn.OrderData.Products) {
			throw new UE_ERROR(
				'Products List not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const partnerIds: { PartnerIds: string[] } = {
			PartnerIds: orderTxn.OrderData.Products.map(
				(d: Record<string, unknown>) => d.PartnerId
			).filter(Boolean) as string[]
		};

		const checkStockByListRes: CheckStockByListRes =
			await this.stockOrder.getDeviceStockStatusByList(partnerIds);

		if (!checkStockByListRes.Success) {
			throw new UE_ERROR(
				'Error checking stock status',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		orderTxn.OrderData = {
			...orderTxn.OrderData,
			CheckStockByListRes: checkStockByListRes.Response
		};
		await this.order.updateOrderableTxn(orderTxn);

		return checkStockByListRes;
	}

	async checkEligibility(
		body: TemporalAddOnsOrderReq
	): Promise<AddOnsEligibilityRes> {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);

		this.validateOrderTxn(orderTxn);

		if (!orderTxn.BillingAccountNo) {
			throw new UE_ERROR(
				'Billing account number not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderTxn.CustomerId) {
			throw new UE_ERROR(
				'Customer ID not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const orderData = orderTxn.OrderData;
		const isOttCategory =
			orderTxn.OrderCategory === AddOnsRequestCategoryEnum.OTT;
		const otts = isOttCategory ? (orderData as OttActivateReq).OttOrder : [];
		const productName = isOttCategory
			? ''
			: (orderData as AddOnsOrderReq).Products[0].ProductName;

		const addOnsEligibilityReq: AddOnsEligibilityReq = {
			EncryptedBillAccNo: await encrypt(orderTxn.BillingAccountNo),
			Category: orderTxn.OrderCategory as AddOnsRequestCategoryEnum,
			ProductName: productName,
			Otts: otts
		};

		const [orderCust]: SelectCustomerOrder[] =
			await this.order.getOrderCustByCustId(orderTxn.CustomerId);
		if (!orderCust) {
			throw new UE_ERROR(
				'Customer record not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const addOnsEligibility = new AddOnsEligibility(this.integrationId, {
			IdType: orderCust.IdType,
			IdValue: orderCust.IdValue
		} as IdTokenInfo);
		const addOnsEligibilityRes: AddOnsEligibilityRes =
			await addOnsEligibility.getAddOnsEligibility(addOnsEligibilityReq);

		orderTxn.OrderData = {
			...orderTxn.OrderData,
			AddOnsEligibilityRes: addOnsEligibilityRes.Response
		};
		await this.order.updateOrderableTxn(orderTxn);

		return addOnsEligibilityRes;
	}
	async reserveStock(
		body: TemporalAddOnsOrderReq
	): Promise<AddOnsReserveOrderRes> {
		if (!this.idTokenInfo) {
			throw new UE_ERROR(
				'Id token info not found',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const stockOrder = new StockOrder(this.integrationId);
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);
		this.validateOrderTxn(orderTxn);
		if (!orderTxn.BillingAccountNo) {
			throw new UE_ERROR(
				'Billing account number not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const reserveStockOrder: Product[] = orderTxn.OrderData.Products.filter(
			(product: Product) => product.DeliveryPartner?.toUpperCase() === 'MMAG'
		);

		if (reserveStockOrder.length === 0) {
			throw new UE_ERROR(
				'No items to reserve stock',
				StatusCodeEnum.BAD_REQUEST_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		const reserveItems = reserveStockOrder.map(item => ({
			Item_SKU: item.PartnerId,
			Item_Name: item.ProductName,
			Rate_Plan: item.ProductName,
			Item_Qty: `${item.TotalQuantity}`
		}));
		const reserveStockRes: Wso2ReserveStockRes = await stockOrder.reserveStock(
			orderTxn.BillingAccountNo,
			orderTxn.OrderId,
			{
				Items: reserveItems
			}
		);
		const reservationNo = reserveStockRes.response.Reservation_No;

		orderTxn.OrderData = {
			...orderTxn.OrderData,
			ReservationNo: reservationNo
		};
		await this.order.updateOrderableTxn(orderTxn);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				ReservationNo: reservationNo
			}
		};
	}

	async unreserveStock(body: TemporalAddOnsOrderReq): Promise<BaseResponse> {
		if (!this.idTokenInfo) {
			throw new UE_ERROR(
				'Id token info not found',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);
		this.validateOrderTxn(orderTxn);

		const stockOrder = new StockOrder(this.integrationId);

		const res: Wso2UnreserveStockRes = await stockOrder.unreserveStock(
			orderTxn.OrderId,
			orderTxn.OrderData.ReservationNo,
			TemporalOrderStatus.ORDER_CANCELED,
			'UE'
		);

		if (res.response.status === 'Unsuccessful') {
			if (res.response.code !== 603) {
				// MMAG 603:unsuccessful due to reservation not found
				throw new UE_ERROR(
					'Error unreserving stock',
					StatusCodeEnum.UE_INTERNAL_SERVER,
					{
						integrationId: this.integrationId,
						response: res.response.message
					}
				);
			}
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId
		};
	}

	async bookAppointment(body: TemporalAddOnsOrderReq) {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);
		this.validateOrderTxn(orderTxn);

		if (!orderTxn.OrderData.AppointmentRequest) {
			throw new UE_ERROR(
				'Appointment request not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderTxn.CustomerId) {
			throw new UE_ERROR(
				'Customer ID not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}
		const [orderCust]: SelectCustomerOrder[] =
			await this.order.getOrderCustByCustId(orderTxn.CustomerId);

		if (!orderTxn.BillingAccountNo) {
			throw new UE_ERROR(
				'Billing account number not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const wso2SAReq: Wso2ServiceAccountReq = {
			idType: orderCust.IdType,
			idValue: orderCust.IdValue,
			BillingAccountNo: orderTxn.BillingAccountNo,
			SystemName: SystemNameEnum.NOVA
		};

		const wso2SARes: Wso2ServiceAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2SAReq,
				LightweightFlagEnum.NO,
				true,
				false
			);

		if (!wso2SARes) {
			throw new UE_ERROR(
				'Service account not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!wso2SARes.Response?.ServiceAccount?.[0].ServicePointId) {
			throw new UE_ERROR(
				'Service point id not found',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		//Result ActivityId
		const activityId = await this.appointmentHelper.bookAppointment(
			orderTxn.OrderData?.AppointmentRequest,
			wso2SARes.Response.ServiceAccount[0].ServicePointId ?? ''
		);

		if (!activityId) {
			throw new UE_ERROR(
				'Internal error: Activity id not returned',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		orderTxn.OrderData = {
			...orderTxn.OrderData,
			ActivityId: activityId
		};

		await this.order.updateOrderableTxn(orderTxn);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				ActivityId: activityId
			}
		};
	}

	async bookAppointmentReschedule(body: TemporalAddOnsOrderReq) {
		const [orderTxn]: SelectOrderableTxnHistory[] =
			await this.order.getOrderableTxnByOrderId(body.OrderId);

		this.validateOrderTxn(orderTxn);

		const appointmentRequest = orderTxn.OrderData.AppointmentRequest;
		if (!appointmentRequest) {
			throw new UE_ERROR(
				'Appointment request not found in order data',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderTxn.CustomerId) {
			throw new UE_ERROR(
				'Customer ID missing in transaction',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		if (!orderTxn.BillingAccountNo) {
			throw new UE_ERROR(
				'Billing account number missing in transaction',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const { earliestStartDate, latestStartDate } =
			this.addOneDayToStartAndEndDate(
				appointmentRequest.SlotStart,
				appointmentRequest.SlotEnd
			);

		const [orderableCust] = await this.order.getOrderCustByCustId(
			orderTxn.CustomerId
		);

		const wso2SAReq: Wso2ServiceAccountReq = {
			idType: orderableCust.IdType,
			idValue: orderableCust.IdValue,
			BillingAccountNo: orderTxn.BillingAccountNo,
			SystemName: SystemNameEnum.NOVA
		};

		const wso2SARes: Wso2ServiceAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
				wso2SAReq,
				LightweightFlagEnum.NO,
				true,
				false
			);

		const accountNo = wso2SARes?.Response?.ServiceAccount?.[0]?.AccountNo;
		if (!accountNo) {
			throw new UE_ERROR(
				'Service account number not found from WSO2',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const wso2CAReq: Wso2CustomerAccountReq = {
			idType: orderableCust.IdType,
			idValue: orderableCust.IdValue
		};

		const wso2CARes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CAReq,
				LightweightFlagEnum.NO,
				true
			);

		const customerAccount = wso2CARes?.Response?.CustomerAccounts?.[0];
		const contactId =
			customerAccount?.PreferredCustomerContactDetails?.ContactId;
		if (!contactId) {
			throw new UE_ERROR(
				'Preferred contact ID not found for customer account',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const product = orderTxn.OrderData.Products?.[0];
		if (!product) {
			throw new UE_ERROR(
				'Product details missing in order data',
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const appointmentReq: AddOnsAppointmentSlotReq = {
			EncryptedBillAccNo: orderTxn.BillingAccountNo,
			AccountNo: accountNo,
			ContactId: contactId,
			DeviceName: product.ProductName,
			PartNumber: product.PartNumber
		};

		try {
			const res: AddOnsAppointmentSlotRes =
				await this.getAppointmentSlotsTemporal(
					appointmentReq,
					orderTxn.OrderSegment,
					earliestStartDate,
					latestStartDate,
					orderTxn.OrderId
				);

			const slot = res.Response.AppointmentSlots?.[0];
			if (!slot) {
				throw new UE_ERROR(
					'No appointment slots available for rescheduling',
					StatusCodeEnum.NOT_FOUND_ERROR,
					{ integrationId: this.integrationId }
				);
			}

			orderTxn.OrderData = {
				...orderTxn.OrderData,
				AppointmentRequest: {
					...orderTxn.OrderData.AppointmentRequest,
					SlotStart: slot.SlotStart,
					SlotEnd: slot.SlotEnd,
					AppointmentId: slot.AppointmentId
				}
			};

			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Message: 'Appointment re-scheduled successfully'
			};
		} catch (error) {
			throw error instanceof UE_ERROR
				? error
				: new UE_ERROR(
						'Failed to reschedule appointment',
						StatusCodeEnum.UE_INTERNAL_SERVER,
						{ integrationId: this.integrationId, response: error }
					);
		}
	}

	private async getAppointmentSlotsTemporal(
		req: AddOnsAppointmentSlotReq,
		segment: string,
		earliestStartDate: string,
		latestStartDate: string,
		orderId?: string
	): Promise<AddOnsAppointmentSlotRes> {
		if (!this.idTokenInfo) {
			throw new UE_ERROR(
				'Id token info not found',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}
		const appointmentRecordHelper = new AppointmentRecordHelper(
			this.integrationId,
			this.idTokenInfo
		);
		const resultAppointmentHelper =
			await appointmentRecordHelper.appointmentSlotRecordHelper(
				req,
				segment,
				earliestStartDate,
				latestStartDate,
				orderId
			);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: resultAppointmentHelper
		};
	}

	private addOneDayToStartAndEndDate(
		startDate: string,
		endDate: string
	): {
		earliestStartDate: string;
		latestStartDate: string;
	} {
		const dateFormat = 'MM/dd/yyyy HH:mm:ss';

		const addOneDay = (date: Date) => addDays(date, 1);

		return {
			earliestStartDate: format(addOneDay(new Date(startDate)), dateFormat),
			latestStartDate: format(addOneDay(new Date(endDate)), dateFormat)
		};
	}
}
