import { type Static, t } from 'elysia';
import { IdTypeEnum } from '../../../../../../enum/user.enum';
import { baseResponseSchema } from '../../../../../../shared/schemas/api/responses.schema';
import { actionResponseSchema } from '../../../../util/schemas/responses';

const profileDataSchema = t.Array(
	t.Object({
		mw_identity: t.Object({
			Id: t.Integer(),
			Name: t.Nullable(t.String()),
			UserId: t.String(),
			IdKey: t.Nullable(t.String()),
			CreatedAt: t.Nullable(t.Date()),
			UpdatedAt: t.Nullable(t.Date()),
			UpdateCount: t.Integer()
		}),
		mw_identification: t.Nullable(
			t.Object({
				Id: t.Integer(),
				IdKey: t.Nullable(t.String()),
				IdType: t.Nullable(t.String()),
				IdValue: t.Nullable(t.String()),
				IsIdVerified: t.Nullable(t.<PERSON>()),
				CreatedAt: t.Nullable(t.Date()),
				UpdatedAt: t.Nullable(t.Date())
			})
		),
		mw_credential: t.Object({
			Id: t.String(),
			UserId: t.String(),
			CredentialKey: t.Nullable(t.String()),
			CredentialType: t.Nullable(t.String()),
			CredentialValue: t.Nullable(t.String()),
			IsVerified: t.Nullable(t.Boolean()),
			IsPrimary: t.Nullable(t.Boolean()),
			IsUnifiNumber: t.Nullable(t.Boolean()),
			CreatedAt: t.Nullable(t.Date()),
			UpdatedAt: t.Nullable(t.Date()),
			UpdateCount: t.Integer()
		})
	})
);

export type ProfileData = Static<typeof profileDataSchema>;

export const verifyProfileReqSchema = t.Object({
	IdType: t.Enum(IdTypeEnum),
	IdValue: t.String({
		minLength: 5,
		examples: [
			'780*******07',
			'PG0344816-D',
			'02bd5d300e847c9476229460e191bd4bbde24e50766a95efd4b75afd455b6d6e'
		]
	}),
	BillingAccountNo: t.String({ minLength: 5, examples: ['**********'] }),
	ServiceId: t.Optional(t.String({ minLength: 5, examples: ['test@unifi'] }))
});
export type VerifyProfileReq = Static<typeof verifyProfileReqSchema>;

export const verifyProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			...actionResponseSchema.properties,
			AccountExist: t.Boolean()
		})
	},
	{ description: 'Service details successfully verified.' }
);

export type VerifyProfileRes = Static<typeof verifyProfileResSchema>;
