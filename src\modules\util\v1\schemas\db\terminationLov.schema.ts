import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const terminationLovTableSchema = pgTable('termination_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	SystemName: text('system_name').notNull(),
	Key: text('key').notNull(),
	Value: text('value').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});
