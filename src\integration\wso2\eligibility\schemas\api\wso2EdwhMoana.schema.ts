import { type Static, t } from 'elysia';

const wso2EdwhMoanaReqSchema = t.Object({
	requestHeader: t.Object({ requestId: t.String(), eventName: t.String() }),
	edwhRequest: t.Object({ customerSvcId: t.String(), segment: t.String() })
});

export type Wso2EdwhMoanaReq = Static<typeof wso2EdwhMoanaReqSchema>;

export const wso2EdwhMoanaResSchema = t.MaybeEmpty(
	t.Object({
		responseHeader: t.Object({
			rqUuid: t.String(),
			requestId: t.String(),
			status: t.String(),
			statusCode: t.Number(),
			errorCode: t.Nullable(t.String()),
			errorMessage: t.Nullable(t.String()),
			errorDetail: t.Nullable(t.String()),
			errorPayload: t.Nullable(t.String())
		}),
		edwhResponse: t.Object({
			eligibleFreeUG: t.MaybeEmpty(t.String()),
			eligibleFreeSpeedUG: t.Maybe<PERSON>mpty(t.String()),
			nwkSuccessFreeUGNWK: t.MaybeEmpty(t.String()),
			eligibleFreeSpeedReason: t.MaybeEmpty(t.String())
		})
	})
);

export type Wso2EdwhMoanaRes = Static<typeof wso2EdwhMoanaResSchema>;
