import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { LightweightFlagEnum } from '../../../../enum/wso2.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CustomerAccountReq,
	Wso2CustomerAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2CustomerAccount.schema';
import type {
	Wso2ServiceAccountReq,
	Wso2ServiceAccountRes
} from '../../../../integration/wso2/user/schemas/api/wso2ServiceAccount.schema';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getLightweightServiceInfo } from '../../../../shared/common';
import { encrypt } from '../../../../shared/encryption/aesGcm';
import type { LightweightServiceInfo } from '../../../../shared/schemas/api/lightweightServiceInfo.schema';
import SubscribedAddOns from '../../../user/v1/helpers/subscribedAddOns.helper';
import type {
	SubscribedOttList,
	SubscribedOttListRes
} from '../../../user/v1/schemas/api/billingAccount.schema';
import type {
	BadgeByBillingAccountRes,
	BillingAccountBadgeObj
} from '../schemas/api/badge.schema';

class BadgeNotification {
	private integrationId: string;
	private mwIntegration: MwIntegration;
	private idTokenInfo: IdTokenInfo;

	constructor(integrationId: string, idTokenInfo: IdTokenInfo) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
		this.idTokenInfo = idTokenInfo;
	}

	async getBadgeNotificationsByBillingAccounts(): Promise<BadgeByBillingAccountRes> {
		const billingAccountBadges: BillingAccountBadgeObj[] = [];
		const wso2CustomerAccReq: Wso2CustomerAccountReq = {
			idType: this.idTokenInfo.IdType,
			idValue: this.idTokenInfo.IdValue
		};

		const wso2CustomerAccRes: Wso2CustomerAccountRes =
			await this.mwIntegration.Wso2UserIntegration.getWso2CustomerAccount(
				wso2CustomerAccReq,
				LightweightFlagEnum.YES
			);

		for (const ca of wso2CustomerAccRes.Response?.CustomerAccounts ?? []) {
			const systemName: string = ca.SystemName || '';
			for (const ba of ca.BillingAccounts ?? []) {
				const billingAccountNo = ba.AccountNumber || '';
				const encryptedBillAccNo: string = await encrypt(billingAccountNo);
				const wso2ServiceAccountReq: Wso2ServiceAccountReq = {
					idType: this.idTokenInfo.IdType,
					idValue: this.idTokenInfo.IdValue,
					SystemName: systemName,
					BillingAccountNo: billingAccountNo
				};

				const wso2ServiceAccountRes: Wso2ServiceAccountRes =
					await this.mwIntegration.Wso2UserIntegration.getWso2ServiceAccount(
						wso2ServiceAccountReq,
						LightweightFlagEnum.NO
					);

				const serviceInfo: LightweightServiceInfo = getLightweightServiceInfo(
					wso2ServiceAccountRes
				);

				// To check ott activation status
				const subscribedAddOn = new SubscribedAddOns(this.integrationId);
				const subscribedOtts: SubscribedOttListRes =
					await subscribedAddOn.getOttListByTvPack(
						serviceInfo.accountId,
						serviceInfo.tvPackName,
						serviceInfo.planSpeed,
						serviceInfo.netflixPlanName
					);

				const subscribedOttList: SubscribedOttList = [
					...subscribedOtts.OttSelectionCustChoice,
					...subscribedOtts.OttSelectionFixed,
					...subscribedOtts.OttAlaCarte
				];

				const hasNonActivatedOtts: boolean = subscribedOttList.some(
					ott => ott.OttActivateStatus === 'Activate'
				);

				billingAccountBadges.push({
					EncryptedBillAccNo: encryptedBillAccNo,
					Badges: hasNonActivatedOtts
						? [
								{
									BadgeMessage: 'Activate your streaming app.Don’t miss out!',
									BadgeType: 'info'
								}
							]
						: []
				});
			}
		}
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: {
				BillingAccounts: billingAccountBadges
			}
		};
	}
}

export default BadgeNotification;
