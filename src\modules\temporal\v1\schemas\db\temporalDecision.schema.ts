import {
	boolean,
	integer,
	jsonb,
	numeric,
	pgTable,
	text,
	timestamp
} from 'drizzle-orm/pg-core';
import { type Static, t } from 'elysia';

export const temporalDecisionRuleSchema = t.Object({
	rules: t.Array(
		t.Object({
			results: t.Array(
				t.Object({
					value: t.String(),
					column: t.String()
				})
			),
			conditions: t.Array(
				t.Object({
					value: t.Union([t.String(), t.Array(t.String())]),
					column: t.String(),
					operator: t.String()
				})
			)
		})
	),
	inputColumns: t.Array(t.String()),
	resultColumns: t.Array(t.String())
});

export type TemporalDecisionRule = Static<typeof temporalDecisionRuleSchema>;

export const temporalDecisionTableSchema = pgTable('temporal_decision', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Name: text('name').notNull(),
	Description: text('description'),
	Version: numeric('version').notNull(),
	Active: boolean('active').default(true),
	Rules: jsonb('rules').$type<TemporalDecisionRule>(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectTemporalDecisionTable =
	typeof temporalDecisionTableSchema.$inferSelect;
