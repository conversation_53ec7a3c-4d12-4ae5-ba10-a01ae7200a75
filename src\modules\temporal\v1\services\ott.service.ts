import { eq } from 'drizzle-orm';
import { getDbInstance } from '../../../../config/db.config';

import { ProgressStatusEnum } from '../../../../enum/order.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	OmgActivateOttReq,
	OmgActivateOttRes
} from '../../../../integration/omg/schemas/api/omgActivateOtt.schema';
import type {
	OmgOttSwapOrderReq,
	OmgOttSwapOrderRes
} from '../../../../integration/omg/schemas/api/omgSwapOtt.schema';
import { UE_ERROR } from '../../../../middleware/error';
import type { IdTokenInfo } from '../../../../middleware/uaid/util/schemas/idTokenInfo.schema';
import { getMyTimeZoneDate } from '../../../../shared/common';

import type {
	OttActivateReq,
	OttActivateRes
} from '../../../order/v1/schemas/api/ottActivate.schema';
import type {
	OttSwapReq,
	OttSwapRes
} from '../../../order/v1/schemas/api/ottSwap.schema';
import { orderableTxnHistoryTableSchema } from '../../../order/v1/schemas/db/orderable.schema';

import Ott from '../../../order/v1/services/ott.service';

export class OttService {
	private db = getDbInstance();
	private mwIntegration: MwIntegration;

	constructor(
		private integrationId: string,
		private idTokenInfo?: IdTokenInfo // Required for Private API only
	) {
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async handlePrepareOttActivationOrder(
		sourceSystem: string,
		segment: string,
		req: OttActivateReq
	): Promise<OttActivateRes> {
		if (!this.idTokenInfo) {
			throw new UE_ERROR(
				'idTokenInfo is required for prepare order',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const ottService = new Ott(this.integrationId, this.idTokenInfo);
		const orderRefNo = await ottService.prepOttActivationOrder(
			sourceSystem,
			segment,
			req
		);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: 'Order successfully prepared',
			Response: {
				OrderRefNo: orderRefNo
			}
		};
	}

	async handleSubmitOttActivation(req: {
		OrderId: string;
	}): Promise<OttActivateRes> {
		const orderRefNo = req.OrderId;

		const record = await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(eq(orderableTxnHistoryTableSchema.OrderId, orderRefNo))
			.execute();

		if (!record || record.length === 0) {
			throw new UE_ERROR(
				`OrderRefNo not found: ${orderRefNo}`,
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const orderRecord = record[0];
		const orderData = orderRecord.OrderData as OttActivateReq;

		const omgReq: OmgActivateOttReq = {
			ottPlanId: orderData.OttPlanId,
			accountType: orderData.AccountType,
			accountId: orderData.AccountId,
			orderRefNo,
			ottOrder: orderData.OttOrder.map(ott => ({
				ottMerchantId: ott.OttMerchantId,
				ottProductId: ott.OttProductId,
				ottOmgId: ott.OttOmgId,
				ottLoginType: ott.OttLoginType,
				ottUserId: ott.OttUserId
			}))
		};

		const omgRes: OmgActivateOttRes =
			await this.mwIntegration.OmgIntegration.omgOttOrder(omgReq);

		await this.db
			.update(orderableTxnHistoryTableSchema)
			.set({
				OrderStatus: ProgressStatusEnum.SUBMITTED,
				OrderProgress: [
					...(orderRecord.OrderProgress || []),
					{
						Status: ProgressStatusEnum.SUBMITTED,
						Timestamp: getMyTimeZoneDate().toISOString()
					}
				]
			})
			.where(eq(orderableTxnHistoryTableSchema.OrderId, orderRefNo))
			.execute();

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: omgRes.responseMsg,
			Response: {
				OrderRefNo: orderRefNo
			}
		};
	}

	async handlePrepareOttSwapOrder(
		sourceSystem: string,
		segment: string,
		req: OttSwapReq
	): Promise<OttSwapRes> {
		if (!this.idTokenInfo) {
			throw new UE_ERROR(
				'idTokenInfo is required for prepare swap',
				StatusCodeEnum.UE_INTERNAL_SERVER,
				{ integrationId: this.integrationId }
			);
		}

		const ottService = new Ott(this.integrationId, this.idTokenInfo);
		const orderRefNo = await ottService.prepOttSwapOrder(
			sourceSystem,
			segment,
			req
		);

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: 'Swap order successfully prepared',
			Response: {
				OrderRefNo: orderRefNo
			}
		};
	}

	async handleSubmitOttSwap(req: { OrderId: string }): Promise<OttSwapRes> {
		const orderRefNo = req.OrderId;

		const record = await this.db
			.select()
			.from(orderableTxnHistoryTableSchema)
			.where(eq(orderableTxnHistoryTableSchema.OrderId, orderRefNo))
			.execute();

		if (!record || record.length === 0) {
			throw new UE_ERROR(
				`OrderRefNo not found: ${orderRefNo}`,
				StatusCodeEnum.NOT_FOUND_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const orderRecord = record[0];
		const orderData = orderRecord.OrderData as OttSwapReq;

		const omgReq: OmgOttSwapOrderReq = {
			ottPlanId: orderData.OttPlanId,
			accountType: orderData.AccountType,
			accountId: orderData.AccountId,
			orderRefNo,
			ottTxnId: orderData.OttTxnId,
			ottMerchantId: orderData.OttMerchantId,
			ottProductId: orderData.OttProductId,
			ottOmgId: orderData.OttOmgId,
			ottLoginType: orderData.OttLoginType,
			ottUserId: orderData.OttUserId
		};

		const omgRes: OmgOttSwapOrderRes =
			await this.mwIntegration.OmgIntegration.omgOttSwapOrder(omgReq);

		await this.db
			.update(orderableTxnHistoryTableSchema)
			.set({
				OrderStatus: ProgressStatusEnum.SUBMITTED,
				OrderProgress: [
					...(orderRecord.OrderProgress || []),
					{
						Status: ProgressStatusEnum.SUBMITTED,
						Timestamp: getMyTimeZoneDate().toISOString()
					}
				]
			})
			.where(eq(orderableTxnHistoryTableSchema.OrderId, orderRefNo))
			.execute();

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: omgRes.responseMsg,
			Response: {
				OrderRefNo: omgRes.orderRefNo
			}
		};
	}
}
