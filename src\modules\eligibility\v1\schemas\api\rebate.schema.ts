import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const eligibleRebateSchema = t.Object({
	ReferenceNumber: t.String({ examples: [''] }),
	AccountNo: t.String({ examples: ['*********'] }),
	Description: t.String({ examples: ['24 Hours Adjustment MyUnifi'] }),
	ServiceID: t.String({ examples: ['test@unifi'] })
});

export type EligibilityRebate = Static<typeof eligibleRebateSchema>;

const listOfEligibilityRebateSchema = t.Array(eligibleRebateSchema);

export type ListOfEligibilityRebate = Static<
	typeof listOfEligibilityRebateSchema
>;

export const rebateResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: listOfEligibilityRebateSchema
	},
	{ description: "Customer's eligibility successfully retrieved." }
);

export type RebateRes = Static<typeof rebateResSchema>;
