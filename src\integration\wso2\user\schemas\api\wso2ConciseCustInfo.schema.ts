import { type Static, t } from 'elysia';

const wso2ConciseCustInfoReqSchema = t.Object({
	requestHeader: t.Optional(
		t.Object({ requestId: t.String(), eventName: t.String() })
	),
	kciRequest: t.Object({
		customerId: t.Nullable(t.String()),
		serviceNo: t.Nullable(t.String()),
		serviceId: t.Nullable(t.String()),
		account: t.String()
	})
});

export type Wso2ConciseCustInfoReq = Static<
	typeof wso2ConciseCustInfoReqSchema
>;

const wso2KCIResponseDataSchema = t.Object({
	timestamp: t.Nullable(t.String()),
	servicestatus: t.Nullable(t.Number()),
	indicator: t.Nullable(t.String()),
	creditlimit: t.Nullable(t.Number()),
	creditusage: t.Nullable(t.Number()),
	tosdate: t.Nullable(t.String()),
	advancepaymentflag: t.Nullable(t.String()),
	advancepaymentamount: t.Nullable(t.Number()),
	advancepaymentmade: t.Nullable(t.Number()),
	advancepaymentdate: t.Nullable(t.String()),
	accountlivefinal: t.Nullable(t.String()),
	accountstatus: t.Nullable(t.String()),
	cpbr: t.Nullable(t.String()),
	delinquencydays: t.Nullable(t.Number()),
	delinquencyreason: t.Nullable(t.String()),
	exposureperc: t.Nullable(t.Number()),
	exposureamount: t.Nullable(t.Number()),
	lastaction: t.Nullable(t.String()),
	lastactiondate: t.Nullable(t.String()),
	lastpromisestatus: t.Nullable(t.String()),
	lastprovisionaction: t.Nullable(t.String()),
	lastprovisionactiondate: t.Nullable(t.String()),
	latestapbalance: t.Nullable(t.Number()),
	lineofbusiness: t.Nullable(t.String()),
	nextaction: t.Nullable(t.String()),
	nextactiondate: t.Nullable(t.String()),
	noofdayspastdue: t.Nullable(t.Number()),
	outstandingamount: t.Nullable(t.Number()),
	overdueamount: t.Nullable(t.Number()),
	strategylastaction: t.Nullable(t.String()),
	strategylastactiondate: t.Nullable(t.String()),
	toscounter: t.Nullable(t.Number()),
	szmailcode: t.Nullable(t.String()),
	collectionstrategy: t.Nullable(t.String()),
	szsmsstatus: t.Nullable(t.String()),
	dtsmsdate: t.Nullable(t.String()),
	szrequestedby: t.Nullable(t.String()),
	szrequestedtype: t.Nullable(t.String()),
	bucketcode: t.Nullable(t.Number()),
	dtrequesteddate: t.Nullable(t.String()),
	dtprocessdate: t.Nullable(t.String()),
	szdueto: t.Nullable(t.String()),
	cstatus: t.Nullable(t.String()),
	creditutilisationupdatedate: t.String(),
	cblacklistyn: t.Nullable(t.String()),
	dtblacklisted: t.Nullable(t.String()),
	cwriteoff: t.Nullable(t.String()),
	dtwriteoff: t.Nullable(t.String()),
	fwriteoffamt: t.Nullable(t.Number()),
	fldwriteamt: t.Nullable(t.Number()),
	productcode: t.Nullable(t.String()),
	customername: t.Nullable(t.String()),
	accountname: t.Nullable(t.String()),
	p2pamount: t.Nullable(t.Number()),
	p2pdate: t.Nullable(t.String()),
	icbr: t.Nullable(t.String()),
	billingdate: t.Nullable(t.String()),
	duedate: t.Nullable(t.String()),
	accountno: t.Union([t.String(), t.Number()]),
	serviceno: t.Nullable(t.String()),
	servicename: t.Nullable(t.String()),
	packagename: t.Nullable(t.String()),
	siservicestatus: t.Nullable(t.String()),
	billamount: t.Nullable(t.Number()),
	lastpaymentdate: t.Nullable(t.String()),
	lastpaymentamount: t.Nullable(t.Number())
});

export type Wso2KCIResponseData = Static<typeof wso2KCIResponseDataSchema>;

export const wso2ConciseCustInfoResSchema = t.Object({
	responseHeader: t.Object({
		rqUuid: t.String(),
		requestId: t.String(),
		status: t.String(),
		statusCode: t.Number(),
		errorCode: t.Nullable(t.Number()),
		errorMessage: t.Nullable(t.String()),
		errorDetail: t.Nullable(t.String()),
		errorPayload: t.Nullable(t.String())
	}),
	kciResponse: t.Nullable(
		t.Object({
			kciresponseData: t.Array(wso2KCIResponseDataSchema)
		})
	)
});

export type Wso2ConciseCustInfoRes = Static<
	typeof wso2ConciseCustInfoResSchema
>;
