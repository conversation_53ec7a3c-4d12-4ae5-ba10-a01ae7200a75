import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const addressLovTableSchema = pgTable('address_lov', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	Type: text('type').notNull(),
	Value: text('value').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});
