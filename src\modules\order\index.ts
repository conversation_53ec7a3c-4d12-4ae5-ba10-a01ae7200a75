import Elysia from 'elysia';
import orderableV1Routes from './v1/controllers/orderable.controller';
import { ottV1Routes } from './v1/controllers/ott.controller';
import { ottCallbackV1Routes } from './v1/controllers/ottCallback.controller';
import stockV1Routes from './v1/controllers/stock.controller';

export const privateOrderV1Routes = new Elysia({
	prefix: '/v1/order'
})
	.use(orderableV1Routes)
	.use(ottV1Routes)
	.use(stockV1Routes);

export const protectedOrderV1Routes = new Elysia({
	prefix: '/v1/order'
}).use(ottCallbackV1Routes);
