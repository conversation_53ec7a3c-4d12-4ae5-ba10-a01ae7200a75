import { format } from 'date-fns';
import { and, desc, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import randomString from 'random-string-gen';
import { getDbInstance } from '../../../../config/db.config';
import { SourceEnum } from '../../../../enum/header.enum';
import {
	EmailEnum,
	TacRequestTypeEnum,
	TacStatusEnum,
	TacTextEnum
} from '../../../../enum/notification.enum';
import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import type { Wso2EmailReq } from '../../../../integration/wso2/notification/schemas/api/wso2Email.schema';
import type {
	Wso2SmsReq,
	Wso2SmsRes
} from '../../../../integration/wso2/notification/schemas/api/wso2Sms.schema';
import Wso2NotificationIntegration from '../../../../integration/wso2/notification/wso2Notification.integration';
import { UE_ERROR } from '../../../../middleware/error';
import { getMyTimeZoneDate } from '../../../../shared/common';
import type {
	GenerateTacReq,
	TacResponse,
	VerifyTacReq
} from '../schemas/api/tac.schema';
import {
	type SelectTac,
	tacCounterTableSchema
} from '../schemas/db/tacCounter.schema';

class TacNotification {
	private db: NodePgDatabase;
	private integrationId: string;
	private wso2NotificationIntegration: Wso2NotificationIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.db = getDbInstance();
		this.wso2NotificationIntegration = new Wso2NotificationIntegration(
			integrationId
		);
	}

	//init tac response
	private initializeResponse(): TacResponse {
		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Message: '',
			Response: { FeFlag: false, PrefixTac: '' }
		};
	}
	//generate tac public function
	async generateTac(req: GenerateTacReq, source: string): Promise<TacResponse> {
		if (req.RequestType === TacRequestTypeEnum.EMAIL) {
			return this.handleEmailTac(req);
		}
		if (req.RequestType === TacRequestTypeEnum.SMS) {
			return this.handleSmsTac(req, source);
		}
		return this.initializeResponse();
	}

	//handle generate tac email req
	private async handleEmailTac(req: GenerateTacReq): Promise<TacResponse> {
		//genegrate new TAC
		const { tac, expireDate }: { tac: string; expireDate: Date } =
			this.generateTacCode();

		const insertResult = await this.db
			.insert(tacCounterTableSchema)
			.values({
				Email: req.Value,
				TAC: tac,
				ExpiredAt: expireDate,
				Status: TacStatusEnum.ACTIVE,
				TacCounter: 0
			})
			.returning();

		if (insertResult.length > 0) {
			const savedTac: SelectTac = insertResult[0];
			return this.sendEmailTac(savedTac, req);
		}

		const res = this.initializeResponse();
		res.Response.FeFlag = false;
		res.Message = TacTextEnum.DB_SAVE_FAILED;
		return res;
	}

	private async sendEmailTac(
		savedTac: SelectTac,
		req: GenerateTacReq
	): Promise<TacResponse> {
		const response: TacResponse = this.initializeResponse();
		const formattedToday: string = format(
			getMyTimeZoneDate(),
			'yyyy-MM-dd HH:mm:ss'
		);
		const msg = `TM: Your TAC is ${savedTac.TAC} for online service request on ${formattedToday}.`;
		const bodyRequest: Wso2EmailReq = {
			from: EmailEnum.FROM_NOREPLY,
			to: req.Value,
			subject: "[No-Reply] You've got notification for login TAC-CODE",
			body: msg
		};

		const wso2Response: boolean =
			await this.wso2NotificationIntegration.getWso2SendEmail(
				bodyRequest,
				this.integrationId,
				'tac verification'
			);

		if (wso2Response) {
			savedTac.TacCounter = (savedTac.TacCounter || 0) + 1;
			response.Response.FeFlag = true;
			response.Message = TacTextEnum.TAC_SEND_SUCCESS;
			response.Response.PrefixTac = this.extractPrefix(savedTac.TAC);

			await this.db
				.update(tacCounterTableSchema)
				.set({ TacCounter: savedTac.TacCounter })
				.where(eq(tacCounterTableSchema.Id, savedTac.Id));
			return response;
		}

		throw new UE_ERROR(TacTextEnum.TAC_SEND_FAILED, StatusCodeEnum.WSO2_ERROR, {
			integrationId: this.integrationId,
			response: TacTextEnum.TAC_SEND_FAILED
		});
	}

	private async handleSmsTac(
		req: GenerateTacReq,
		source: string
	): Promise<TacResponse> {
		const mobileNumber: string = this.checkPhoneNoFormat(req.Value);
		const latestActiveTacResult: SelectTac[] = await this.db
			.select()
			.from(tacCounterTableSchema)
			.where(
				and(
					eq(tacCounterTableSchema.MobileNumber, mobileNumber),
					eq(tacCounterTableSchema.Status, TacStatusEnum.ACTIVE)
				)
			)
			.orderBy(desc(tacCounterTableSchema.CreatedAt))
			.limit(1)
			.execute();

		if (latestActiveTacResult.length > 0) {
			return this.handleExistingSmsTac(
				latestActiveTacResult[0],
				mobileNumber,
				source
			);
		}

		return this.generateAndSendSms(mobileNumber, source);
	}

	private async handleExistingSmsTac(
		latestMobileTac: SelectTac,
		mobileNumber: string,
		source: string
	): Promise<TacResponse> {
		const response: TacResponse = this.initializeResponse();
		const isExpired: boolean =
			getMyTimeZoneDate() > new Date(latestMobileTac.ExpiredAt);
		if (isExpired) {
			await this.db
				.update(tacCounterTableSchema)
				.set({ Status: TacStatusEnum.DEACTIVATED })
				.where(eq(tacCounterTableSchema.Id, latestMobileTac.Id))
				.execute();
			return this.generateAndSendSms(mobileNumber, source);
		}

		if (latestMobileTac.TacCounter >= 3) {
			response.Response.FeFlag = false;
			response.Message = `Please try after ${latestMobileTac.ExpiredAt}`;
			return response;
		}

		const formattedToday: string = format(
			getMyTimeZoneDate(),
			'yyyy-MM-dd HH:mm:ss'
		);
		const expireDate: string = format(
			latestMobileTac.ExpiredAt,
			'yyyy-MM-dd HH:mm:ss'
		);
		const sms: string = `TM: TAC is ${latestMobileTac.TAC} for online service request on ${formattedToday}. Code expires in ${expireDate} mins.`;

		const bodyRequest: Wso2SmsReq = { phoneno: mobileNumber, sms };
		const wso2Response: Wso2SmsRes =
			await this.wso2NotificationIntegration.getWso2SendSms(bodyRequest);

		if (wso2Response.status === 0) {
			latestMobileTac.TacCounter += 1;
			response.Response.FeFlag = true;
			response.Message = TacTextEnum.TAC_SEND_SUCCESS;
			response.Response.PrefixTac = this.extractPrefix(latestMobileTac.TAC);

			await this.db
				.update(tacCounterTableSchema)
				.set({ TacCounter: latestMobileTac.TacCounter })
				.where(eq(tacCounterTableSchema.Id, latestMobileTac.Id));
			return response;
		}

		throw new UE_ERROR(TacTextEnum.TAC_SEND_FAILED, StatusCodeEnum.WSO2_ERROR, {
			integrationId: this.integrationId,
			response: TacTextEnum.TAC_SEND_FAILED
		});
	}

	async generateAndSendSms(
		mobileNumber: string,
		source: string
	): Promise<TacResponse> {
		const { tac, expireDate }: { tac: string; expireDate: Date } =
			this.generateTacCode(mobileNumber);

		const savedTac = await this.saveTacToDatabase(
			mobileNumber,
			tac,
			expireDate
		);
		if (!savedTac)
			throw new UE_ERROR(
				TacTextEnum.DB_SAVE_FAILED,
				StatusCodeEnum.WSO2_ERROR,
				{
					integrationId: this.integrationId,
					response: TacTextEnum.DB_SAVE_FAILED
				}
			);

		// Handle Dynatrace mobile number
		if (this.isDynaTraceNumber(mobileNumber)) {
			return await this.handleDynaTraceTac(savedTac);
		}

		// Automation Testing Bypass
		//* Right now, I don't see any need for this TAC to be inserted into the DB. The TAC value is stored in process env
		if (
			source === SourceEnum.TEST_AUTOMATION &&
			process.env.AUTOTEST_NUM &&
			mobileNumber === process.env.AUTOTEST_NUM &&
			process.env.AUTOTEST_TAC
		) {
			return {
				Success: true,
				Code: StatusCodeEnum.OK,
				IntegrationId: this.integrationId,
				Message: TacTextEnum.TAC_SEND_SUCCESS,
				Response: {
					FeFlag: true,
					PrefixTac: this.extractPrefix(process.env.AUTOTEST_TAC)
				}
			};
		}

		return await this.sendTacSms(mobileNumber, savedTac, expireDate);
	}

	//Save TAC to Database
	private async saveTacToDatabase(
		mobile: string,
		tac: string,
		expireDate: Date
	): Promise<SelectTac | null> {
		const insertResult: SelectTac[] = await this.db
			.insert(tacCounterTableSchema)
			.values({
				MobileNumber: mobile,
				TAC: tac,
				ExpiredAt: expireDate,
				Status: TacStatusEnum.ACTIVE,
				TacCounter: 0
			})
			.returning();
		return insertResult.length > 0 ? insertResult[0] : null;
	}

	//Check if mobile number is Dynatrace
	private isDynaTraceNumber(mobileNumber: string): boolean {
		return process.env.DYNA_TRACE_NUMBER === mobileNumber;
	}

	//Handle Dynatrace TAC case
	private async handleDynaTraceTac(savedTac: SelectTac): Promise<TacResponse> {
		savedTac.TacCounter = (savedTac.TacCounter || 0) + 1;
		const response = this.initializeResponse();
		response.Response.FeFlag = true;
		response.Message = TacTextEnum.TAC_SEND_SUCCESS;
		response.Response.PrefixTac = this.extractPrefix(savedTac.TAC);

		await this.db
			.update(tacCounterTableSchema)
			.set({ TacCounter: savedTac.TacCounter })
			.where(eq(tacCounterTableSchema.Id, savedTac.Id));

		return response;
	}

	//Send TAC via SMS
	private async sendTacSms(
		mobileNumber: string,
		savedTac: SelectTac,
		expireDate: Date
	): Promise<TacResponse> {
		const formattedToday = format(getMyTimeZoneDate(), 'yyyy-MM-dd HH:mm:ss');
		const formattedExpireDate = format(expireDate, 'yyyy-MM-dd HH:mm:ss');
		const smsMessage = `TM: TAC is ${savedTac.TAC} for online service request on ${formattedToday}. Code expires in ${formattedExpireDate} mins.`;

		const bodyRequest: Wso2SmsReq = { phoneno: mobileNumber, sms: smsMessage };
		const wso2Response: Wso2SmsRes =
			await this.wso2NotificationIntegration.getWso2SendSms(bodyRequest);

		if (wso2Response.status === 0) {
			savedTac.TacCounter = (savedTac.TacCounter || 0) + 1;
			const response = this.initializeResponse();
			response.Response.FeFlag = true;
			response.Message = TacTextEnum.TAC_SEND_SUCCESS;
			response.Response.PrefixTac = this.extractPrefix(savedTac.TAC);

			await this.db
				.update(tacCounterTableSchema)
				.set({ TacCounter: savedTac.TacCounter })
				.where(eq(tacCounterTableSchema.Id, savedTac.Id));

			return response;
		}

		throw new UE_ERROR(TacTextEnum.TAC_SEND_FAILED, StatusCodeEnum.WSO2_ERROR, {
			integrationId: this.integrationId,
			response: TacTextEnum.TAC_SEND_FAILED
		});
	}

	async verifyTac(
		req: VerifyTacReq,
		requestType: string,
		source: string
	): Promise<TacResponse> {
		// Initialize the response object
		const response: TacResponse = this.initializeResponse();

		// Automation Testing Bypass
		if (
			source === SourceEnum.TEST_AUTOMATION &&
			process.env.AUTOTEST_NUM &&
			req.Value === process.env.AUTOTEST_NUM &&
			process.env.AUTOTEST_TAC &&
			req.TacCode === process.env.AUTOTEST_TAC
		) {
			response.Response.FeFlag = true;
			response.Message = TacTextEnum.TAC_VERIFIED;
			return response;
		}

		// Validate and format the type (sms/email)
		const value: string =
			requestType === 'sms' ? this.checkPhoneNoFormat(req.Value) : req.Value;
		const type = requestType === 'sms' ? 'MobileNumber' : 'Email';
		if (!value) {
			throw new UE_ERROR(
				'TAC verify Throw Error',
				StatusCodeEnum.NOT_ACCEPTABLE_ERROR,
				{
					integrationId: this.integrationId,
					response: 'Invalid request type.'
				}
			);
		}

		// Query database for matching TAC
		const results: SelectTac[] = await this.db
			.select()
			.from(tacCounterTableSchema)
			.where(
				and(
					eq(tacCounterTableSchema[type], value),
					eq(tacCounterTableSchema.TAC, req.TacCode)
				)
			)
			.orderBy(desc(tacCounterTableSchema.CreatedAt))
			.limit(1)
			.execute();

		if (results.length === 0) {
			response.Response.FeFlag = false;
			response.Message = TacTextEnum.TAC_NOT_VERIFIED;
			return response;
		}

		const foundTac = results[0];

		if (foundTac.Status !== TacStatusEnum.ACTIVE) {
			response.Response.FeFlag = false;
			response.Message = TacTextEnum.TAC_ALREADY_USED;
			return response;
		}

		// Handle duplicate active TACs
		const duplicateResults: SelectTac[] = await this.db
			.select()
			.from(tacCounterTableSchema)
			.where(
				and(
					eq(tacCounterTableSchema[type], value),
					eq(tacCounterTableSchema.Status, TacStatusEnum.ACTIVE)
				)
			)
			.orderBy(desc(tacCounterTableSchema.CreatedAt))
			.execute();

		if (duplicateResults.length > 0) {
			for (const tacRecord of duplicateResults) {
				await this.db
					.update(tacCounterTableSchema)
					.set({ Status: TacStatusEnum.DEACTIVATED })
					.where(eq(tacCounterTableSchema.Id, tacRecord.Id));
			}
		}

		response.Response.FeFlag = true;
		response.Message = TacTextEnum.TAC_VERIFIED;
		return response;
	}

	//correct number format
	checkPhoneNoFormat(phoneNo: string): string {
		if (phoneNo) {
			if (phoneNo.startsWith('+6')) {
				phoneNo.substring(1);
			} else if (phoneNo.startsWith('006')) {
				phoneNo.substring(2);
			}
		}
		return phoneNo;
	}

	//generate tac code function
	private generateTacCode(mobileNumber?: string) {
		const currentDate: Date = getMyTimeZoneDate();
		if (mobileNumber) {
			if (this.isDynaTraceNumber(mobileNumber)) {
				return {
					tac: process.env.DYNA_TRACE_TAC || '',
					expireDate: new Date(currentDate.setHours(currentDate.getHours() + 6))
				};
			}
		}
		const letters: string = randomString({
			length: 4,
			type: 'alphabetic'
		}).toUpperCase();
		const numbers = Math.floor(1000 + Math.random() * 9000);
		return {
			tac: `${letters}-${numbers}`,
			expireDate: new Date(currentDate.setMinutes(currentDate.getMinutes() + 3))
		};
	}
	//extract prefix function
	private extractPrefix(tac: string): string {
		return tac.includes('-') ? tac.substring(0, tac.indexOf('-') + 1) : '';
	}
}

export default TacNotification;
