import { type Static, t } from 'elysia';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

const formattedAlert = t.Array(
	t.Object({
		Id: t.Number(),
		AlertTime: t.Date(),
		AlertTitle: t.String(),
		AlertCauseId: t.Number(),
		Ettr: t.Date(),
		AdditionalRemarks: t.String(),
		AlertStatusId: t.Number(),
		Displayed: t.<PERSON>(),
		CreatedBy: t.String(),
		UpdatedBy: t.String(),
		CreatedAt: t.Date(),
		UpdatedAt: t.Date(),
		alert_main_rels: t.Array(
			t.Object({
				Id: t.Number(),
				Order: t.Number(),
				ParentId: t.Number(),
				Path: t.String(),
				AlertServiceLovId: t.Nullable(t.Number()),
				AlertAreaLovId: t.Nullable(t.Number())
			})
		)
	})
);

export type FormattedAlert = Static<typeof formattedAlert>;

const unifiAlertsResObjectSchema = t.Object({
	Title: t.String({
		example:
			'Loss of broadband connection ( <PERSON><PERSON>, Johor;  Parit Bunga, Johor )'
	}),
	Cause: t.String({ example: 'Cable Theft' }),
	ServiceImpacted: t.Array(t.String({ example: 'Fibre broadband' })),
	AreaAffected: t.Array(t.String({ example: 'Apin-Apin, Sabah' })),
	Ettr: t.Date({ example: '2025-04-10 12:00:00.000' }),
	AdditionalRemarks: t.String({ example: 'Require status from ' }),
	Status: t.String({ example: 'Resolved' }),
	Displayed: t.Boolean()
});

export type UnifiAlertsResObject = Static<typeof unifiAlertsResObjectSchema>;

export const unifiAlertsResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Array(unifiAlertsResObjectSchema)
	},
	{
		description: 'Alerts successfully retrieved'
	}
);

export type UnifiAlertsRes = Static<typeof unifiAlertsResSchema>;
