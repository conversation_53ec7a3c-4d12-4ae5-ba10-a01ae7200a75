import { type Static, t } from 'elysia';
import { AddOnsRequestCategoryEnum } from '../../../../../enum/addOns.enum';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';

export const appointmentBookingStructureSchema = t.Optional(
	t.Object(
		{
			OrderId: t.String({
				description: 'Order ID for the appointment booking',
				example: 'MW6-2025052801184'
			}),
			RNORegion: t.String({
				description: 'Region of the appointment slot',
				example: 'ZONE SKUDAI'
			}),
			AppointmentId: t.String({
				description: 'Appointment slot ID returned from availability service',
				example: 'S2A-250430C6B089'
			}),
			SlotStart: t.String({
				description:
					'Start time of selected appointment slot (format: MM/DD/YYYY HH:mm:ss)',
				example: '05/13/2025 12:30:00'
			}),
			SlotEnd: t.String({
				description:
					'End time of selected appointment slot (format: MM/DD/YYYY HH:mm:ss)',
				example: '05/13/2025 14:00:00'
			})
		},
		{
			description:
				'Appointment slot selection details. [Required for: MESH WIFI 6]'
		}
	)
);

export type AppointmentBookingReq = Static<
	typeof appointmentBookingStructureSchema
>;

export const billingAddressSchema = t.Object({
	AddressType: t.String(),
	UnitLot: t.MaybeEmpty(t.String({ examples: ['A-9-38'] })),
	FloorNo: t.Optional(t.String({ examples: ['9'] })),
	BuildingName: t.Optional(t.String({ examples: ['FTTH BLOK A KONDO PETAL'] })),
	StreetType: t.String({ examples: ['JALAN'] }),
	StreetName: t.String({ examples: ['TANJONG RAKIT 2'] }),
	Section: t.String({ examples: ['TANJONG RAKIT'] }),
	City: t.String({ examples: ['SHAH ALAM'] }),
	Postcode: t.String({ examples: ['68000'] }),
	State: t.String({ examples: ['SELANGOR'] }),
	Country: t.String({ examples: ['MALAYSIA'] })
});

export type BillingAddress = Static<typeof billingAddressSchema>;

const productListSchema = t.Object({
	Id: t.Number(),
	ContractTerm: t.String({
		description: 'Selected device addon contract term based on SME mapping',
		example: '24'
	}),
	DeliveryPartner: t.Nullable(
		t.String({
			description: 'Selected delivery partner',
			example: 'MMAG'
		})
	),
	ProductName: t.String({
		description: 'Selected product name',
		example: 'Sharp TV 60inch'
	}),
	ProductPrice: t.String({
		description: 'Selected product price',
		example: '15'
	}),
	LeasingType: t.String({
		description: 'Leasing type of product',
		example: 'lease to own'
	}),
	MonthlyCommitment: t.String({
		description: 'Monthly commitment for the selected product',
		example: '15'
	}),
	PartnerId: t.String({
		description: 'Partnership ID for product name based on Siebel mapping',
		example: 'SM-UA75DU7000KXXM'
	}),
	PurchasePrice: t.String({
		description: 'Purchase price of the product',
		example: '15'
	}),
	TotalQuantity: t.Number({
		description: 'Total quantity of selected product',
		example: 2
	}),
	PartNumber: t.Nullable(
		t.String({
			description: 'Part number of the product',
			example: 'PR009139'
		})
	)
});

export type ProductList = Static<typeof productListSchema>;

export const customerInfoSchema = t.Object({
	AccountNo: t.String({
		description: 'Siebel account number associated with the customer',
		example: '1-XXXXXXX'
	}),
	ContactId: t.String({
		description: 'Siebel contact ID identifying the user within the system',
		example: '1-XXXXXXX'
	}),
	Email: t.String({}),
	ContactNo: t.String({}),
	BillingEmail: t.String({
		description: 'Billing email address of the customer',
		format: 'email',
		example: '<EMAIL>'
	}),
	BillingContactNo: t.String({
		description: 'Mobile contact number of the customer',
		example: '***********'
	}),
	BillingAddress: billingAddressSchema,
	EncryptedBillAccNo: t.String({
		description:
			'AES-encrypted billing account number (used for internal verification)',
		example: '6a6de1ec5f40bb147c979ced0a55cbf7c8efb66...'
	}),
	FullName: t.String({
		description: 'Full name of the customer placing the order',
		example: 'Ahmad Rizal Bin Ismail'
	})
});

export type CustomerInfo = Static<typeof customerInfoSchema>;

export const addOnsOrderReqSchema = t.Object({
	Category: t.Enum(AddOnsRequestCategoryEnum, {
		description:
			'Add-on request category to determine addon flow and validation logic'
	}),
	CustomerInfo: customerInfoSchema,
	Products: t.Array(productListSchema, {
		description: 'List of selected products.',
		minLength: 1,
		maxLength: 2
	}),
	AppointmentRequest: appointmentBookingStructureSchema,
	VoucherCode: t.Nullable(
		t.String({
			description: 'Optional voucher code for discounts or promotions',
			example: 'VOUCHER123'
		})
	),
	ReferralCode: t.Nullable(
		t.String({
			description: 'Optional referral code for additional benefits',
			example: 'REFERRAL456'
		})
	)
});

export type AddOnsOrderReq = Static<typeof addOnsOrderReqSchema>;

export const addOnsOrderResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			Status: t.String({
				description:
					'Overall status of the add-on order request, typically SUCCESS if processed successfully by backend and Siebel',
				example: 'SUCCESS'
			}),
			OrderNo: t.String({
				description:
					'Unique Siebel-generated order number assigned to this add-on request',
				example: '1-25-SMD1840436'
			}),
			IptvId: t.Optional(
				t.String({
					description: 'Assigned IPTV service ID. [Returned for: TV PACK]',
					example: 'johnsmith@iptv'
				})
			)
		})
	},
	{
		description:
			'Add-on order response returned after submitting to Siebel via WSO2.'
	}
);

export type AddOnsOrderRes = Static<typeof addOnsOrderResSchema>;
