import { type Static, t } from 'elysia';
import {
	ConsumerIdTypeEnum,
	CredentialTypeEnum
} from '../../../../../../enum/user.enum';
import { baseResponseSchema } from '../../../../../../shared/schemas/api/responses.schema';
import { getBrnIdResSchema } from '../../../../sme/v1/schemas/api/sme';
import { actionResponseSchema } from '../../../../util/schemas/responses';
import { credentialsResSchema } from './credential';

export const fetchIdentityReqSchema = t.Object({
	CredentialType: t.Enum(CredentialTypeEnum),
	CredentialValue: t.String({
		minLength: 10,
		examples: ['0123456789', '<EMAIL>']
	})
});

export type FetchIdentityReq = Static<typeof fetchIdentityReqSchema>;

export const getIdSchema = t.Nullable(
	t.Object({
		IdType: t.Nullable(t.String({ enum: Object.values(ConsumerIdTypeEnum) })),
		IdValue: t.Nullable(t.String({ examples: ['570831550001'] })),
		IdToken: t.Optional(
			t.String({
				examples: [
					'680aje0d9cc158d1f1ce112c262d4cdf485a3a6a3cbca712c9a73e216be14957'
				]
			})
		),
		IsIdVerified: t.Optional(t.Boolean())
	})
);

export type GetId = Static<typeof getIdSchema>;

export const idTokenSchema = t.Nullable(
	t.Object({
		IdType: t.String(),
		IdValue: t.String({ examples: ['999999-99-9999'] }),
		IsIdVerified: t.Boolean(),
		CreatedAt: t.Optional(t.String({ examples: ['2024-11-12 15:40:25'] })),
		UpdatedAt: t.Optional(t.String({ examples: ['2024-11-12 15:41:00'] }))
	})
);

export type IdTokenInfo = Static<typeof idTokenSchema>;

const selectIdentitySchema = t.Object({
	SessionId: t.Optional(
		t.String({
			examples: [
				'680aje0d9cc158d1f1ce112c262d4cdf485a3a6a3cbca712c9a73e216be14957'
			]
		})
	),
	UserId: t.String({ examples: ['w0SrNWz0RJydB12'] }),
	Name: t.Nullable(t.String({ examples: ['Tester ABC'] })),
	Consumer: t.MaybeEmpty(
		t.Object({
			IdType: t.Nullable(t.String({ enum: Object.values(ConsumerIdTypeEnum) })),
			IdValueMasked: t.Nullable(t.String({ examples: ['800********77'] })),
			IdToken: t.Optional(
				t.String({
					examples: [
						'680aje0d9cc158d1f1ce112c262d4cdf485a3a6a3cbca712c9a73e216be14957'
					]
				})
			),
			IsIdVerified: t.Optional(t.Boolean())
		})
	),
	Business: t.Optional(t.Array(getBrnIdResSchema)),
	Credentials: t.Array(credentialsResSchema)
});

export type SelectIdentityApi = Static<typeof selectIdentitySchema>;

export const identityResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: selectIdentitySchema
	},
	{ description: 'Identity profile successfully retrieved' }
);

export type IdentityRes = Static<typeof identityResSchema>;

export const updateProfileReqSchema = t.Object({
	Name: t.Optional(t.String({ minLength: 3, examples: ['Tester ABC'] })),
	IdNumber: t.Optional(
		t.String({ minLength: 5, examples: ['570831550001', 'A00000000'] })
	),
	IdType: t.Optional(t.Enum(ConsumerIdTypeEnum)),
	IdValue: t.Optional(t.String({ minLength: 5, examples: ['570831550001'] })),
	CredentialType: t.Optional(t.Enum(CredentialTypeEnum)),
	CredentialValue: t.Optional(
		t.String({
			minLength: 10,
			examples: ['0123456789', '<EMAIL>']
		})
	)
});

export type UpdateProfileReq = Static<typeof updateProfileReqSchema>;

export const updateProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			...actionResponseSchema.properties
		})
	},
	{ description: 'Identity profile successfully updated' }
);

export type UpdateProfileRes = Static<typeof updateProfileResSchema>;

export const deleteProfileResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: actionResponseSchema
	},
	{ description: 'Identity profile successfully deleted' }
);

export type DeleteProfileRes = Static<typeof deleteProfileResSchema>;

// signup
export const signUpReqSchema = t.Object({
	Name: t.String({ minLength: 3, examples: ['Tester ABC'] }),
	Email: t.String({ minLength: 10, examples: ['<EMAIL>'] }),
	MobileNumber: t.String({ minLength: 10, examples: ['0123456789'] }),
	IdValue: t.String({ minLength: 5, examples: ['570831550001'] }),
	IdType: t.Enum(ConsumerIdTypeEnum)
});

export type SignUpReq = Static<typeof signUpReqSchema>;

export const signUpResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			...actionResponseSchema.properties,
			Credentials: t.Optional(t.Array(credentialsResSchema))
		})
	},
	{ description: 'Profile created / updated / no change' }
);

export type SignUpRes = Static<typeof signUpResSchema>;

// forgot-login-id
export const forgotLoginIdReqSchema = t.Object({
	IdValue: t.String({ minLength: 5, examples: ['123456121234'] }),
	IdType: t.Enum(ConsumerIdTypeEnum)
});

export type ForgotLoginIdReq = Static<typeof forgotLoginIdReqSchema>;

export const forgotLoginIdResSchema = t.Object(
	{
		...baseResponseSchema.properties,
		Response: t.Object({
			...actionResponseSchema.properties,
			Credentials: t.Array(credentialsResSchema)
		})
	},
	{
		description: 'Retrieve credentials (if the ID Number existed)'
	}
);

export type ForgotLoginIdRes = Static<typeof forgotLoginIdResSchema>;
