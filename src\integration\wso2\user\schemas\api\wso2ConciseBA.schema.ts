import { type Static, t } from 'elysia';

export const wso2ConciseBAResSchema = t.Object({
	LAST_PROVISION_ACTION_DATE: t.MaybeEmpty(t.String()),
	ACC_LIVE_FINAL: t.MaybeEmpty(t.String()),
	ACCOUNT_STATUS: t.MaybeEmpty(t.String()),
	ADVANCE_PAYMENT_AMOUNT: t.Maybe<PERSON>mpty(t.String()),
	ADVANCE_PAYMENT_FLAG: t.MaybeEmpty(t.String()),
	ADVANCE_PAYMENT_MADE: t.MaybeEmpty(t.String()),
	BUCKET_CODE: t.MaybeEmpty(t.String()),
	CPBR: t.MaybeEmpty(t.String()),
	WRITE_OFF_STATUS: t.MaybeEmpty(t.String()),
	CREDIT_LIMIT: t.MaybeEmpty(t.String()),
	CREDIT_USAGE: t.MaybeEmpty(t.String()),
	DELINQUENCY_DAYS: t.MaybeEmpty(t.String()),
	ERROR_CODE: t.MaybeEmpty(t.String()),
	ERROR_DESC: t.MaybeEmpty(t.String()),
	EXPOSURE: t.MaybeEmpty(t.String()),
	EXPOSURE_AMOUNT: t.MaybeEmpty(t.String()),
	WRITE_OFF_AMOUNT: t.MaybeEmpty(t.String()),
	LATEST_WRITE_OFF_AMOUNT_DUE: t.MaybeEmpty(t.String()),
	LAST_PROMISE_STATUS: t.MaybeEmpty(t.String()),
	LAST_PROVISION_ACTION: t.MaybeEmpty(t.String()),
	LATEST_AP_BALANCE: t.MaybeEmpty(t.String()),
	LINE_OF_BUSINESS: t.MaybeEmpty(t.String()),
	NO_DAYS_PAST_DUE: t.MaybeEmpty(t.String()),
	OUTSTANDING_AMOUNT: t.MaybeEmpty(t.String()),
	OVERDUE_AMOUNT: t.MaybeEmpty(t.String()),
	REQUEST_TYPE: t.MaybeEmpty(t.String()),
	SERVICE_STATUS: t.MaybeEmpty(t.String()),
	TOS_COUNTER: t.MaybeEmpty(t.String()),
	TIMESTAMP: t.MaybeEmpty(t.String()),
	BLACKLIST_TAGGING: t.MaybeEmpty(t.String()),
	INDICATOR: t.MaybeEmpty(t.String()),
	CREDIT_UTILISATION_UPDATE: t.MaybeEmpty(t.String())
});

export type Wso2ConciseBARes = Static<typeof wso2ConciseBAResSchema>;
