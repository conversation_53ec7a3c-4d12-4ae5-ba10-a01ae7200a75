import { StatusCodeEnum } from '../../../../enum/statusCode.enum';
import { MwIntegration } from '../../../../integration/mw.integration';
import type {
	Wso2CheckStockInventory,
	Wso2CheckStockRes
} from '../../../../integration/wso2/order/schemas/api/wso2CheckStock.schema';
import type {
	Wso2ReserveStockReq,
	Wso2ReserveStockRes
} from '../../../../integration/wso2/order/schemas/api/wso2ReserveStock';
import type {
	Wso2UnreserveStockReq,
	Wso2UnreserveStockRes
} from '../../../../integration/wso2/order/schemas/api/wso2UnreserveStock';
import type {
	CheckStockByListObj,
	CheckStockByListReq,
	CheckStockByListRes
} from '../schemas/api/checkStockByList.schema';
import type { ReserveStockReq } from '../schemas/api/reserveStock.schema';

class StockOrder {
	private integrationId: string;
	private mwIntegration: MwIntegration;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
		this.mwIntegration = new MwIntegration(integrationId);
	}

	async getDeviceStockStatusByList(
		req: CheckStockByListReq
	): Promise<CheckStockByListRes> {
		const wso2CheckStockRes: Wso2CheckStockRes =
			await this.mwIntegration.Wso2OrderIntegration.getWso2CheckStock(
				req.PartnerIds
			);

		const checkStockArr: CheckStockByListObj[] = [];
		for (const itemSku of wso2CheckStockRes?.response.Items ?? []) {
			checkStockArr.push({
				PartnerId: itemSku.Item_SKU,
				StockStatus: itemSku.Inventory
					? this.getStockStatus(itemSku.Inventory)
					: 'Out of Stock',
				Message:
					itemSku.Inventory && itemSku.Inventory.length > 0
						? 'Stock data fetched successfully.'
						: `No inventory data available for item SKU: ${itemSku.Item_SKU}`,
				Inventory:
					itemSku.Inventory?.map(inventory => ({
						BranchCode: inventory.Branch_Code,
						BranchName: inventory.Branch_Name,
						QtyOnHand: inventory.Qty_on_Hand,
						QtyReserved: inventory.Qty_Reserved,
						QtyAvailable: inventory.Qty_Available
					})) ?? []
			});
		}

		return {
			Success: true,
			Code: StatusCodeEnum.OK,
			IntegrationId: this.integrationId,
			Response: checkStockArr
		};
	}

	private getStockStatus(inventory: Wso2CheckStockInventory[]): string {
		const totalQtyAvailable = inventory.reduce(
			(total, inv) => total + inv.Qty_Available,
			0
		);

		if (totalQtyAvailable > 5) {
			return 'Available';
		}

		if (totalQtyAvailable >= 2 && totalQtyAvailable <= 5) {
			return 'Selling Fast';
		}

		return 'Restocking In Progress';
	}

	async reserveStock(
		billingAccNo: string,
		orderId: string,
		reserveStockReq: ReserveStockReq
	): Promise<Wso2ReserveStockRes> {
		if (reserveStockReq.Items.length >= 2) {
			const wso2Req: Wso2ReserveStockReq = {
				Request: {
					Frontend_Order_Id: orderId,
					ItemsExt: reserveStockReq.Items.map(itemsObj => ({
						Item_SKU: itemsObj.Item_SKU,
						Item_Name: itemsObj.Item_Name,
						Item_Type: 'DEVICE',
						Rate_Plan: itemsObj.Rate_Plan,
						Item_Line_ID: billingAccNo,
						Item_Qty: itemsObj.Item_Qty
					}))
				}
			};

			const wso2Res: Wso2ReserveStockRes =
				await this.mwIntegration.Wso2OrderIntegration.getWso2ReserveStock(
					wso2Req
				);

			return wso2Res;
		}

		const wso2Req: Wso2ReserveStockReq = {
			Request: {
				Frontend_Order_Id: orderId,
				Items: {
					Item_SKU: reserveStockReq.Items[0].Item_SKU,
					Item_Name: reserveStockReq.Items[0].Item_Name,
					Item_Type: 'DEVICE',
					Rate_Plan: reserveStockReq.Items[0].Rate_Plan,
					Item_Line_ID: billingAccNo,
					Item_Qty: reserveStockReq.Items[0].Item_Qty
				}
			}
		};

		return await this.mwIntegration.Wso2OrderIntegration.getWso2ReserveStock(
			wso2Req
		);
	}

	async unreserveStock(
		orderId: string,
		reservationNo: string,
		billingAccNo: string,
		reason: string
	): Promise<Wso2UnreserveStockRes> {
		const wso2Req: Wso2UnreserveStockReq = {
			Request: {
				Frontend_Order_Id: orderId,
				Reservation_No: reservationNo,
				Item_Line_ID: billingAccNo,
				Reason: reason,
				Unreserved_By: 'UE'
			}
		};

		const wso2Res: Wso2UnreserveStockRes =
			await this.mwIntegration.Wso2OrderIntegration.getWso2UnreserveStock(
				wso2Req
			);

		return wso2Res;
	}
}

export default StockOrder;
