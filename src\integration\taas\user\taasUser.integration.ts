import { envConfig } from '../../../config/env.config';
import { StatusCodeEnum } from '../../../enum/statusCode.enum';
import { UE_ERROR } from '../../../middleware/error';
import { fetchApi } from '../../helper/fetchApi.helper';
import { getTaasApimToken } from '../helper/taasApimToken.helper';
import type {
	TaaSServiceDetailsReq,
	TaasServiceDetailsRes
} from './schemas/api/taasServiceDetails.schema';

class TaasUserIntegration {
	private integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async getTaasCustomerServiceAccount(
		bodyRequest: TaaSServiceDetailsReq
	): Promise<TaasServiceDetailsRes> {
		const url: string = envConfig().TAAS_SERVICE_DETAILS_URL;
		const token: string = await getTaasApimToken();
		const body = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${token}`,
				'x-calling-application': 'UE',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(bodyRequest)
		};
		const res = await fetchApi(this.integrationId, url, body);

		if (!res.ok) {
			throw new UE_ERROR(
				'Failed to get Taas Customer Service Details',
				StatusCodeEnum.TAAS_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		const resBody = await res.json().catch(error => {
			throw new UE_ERROR(
				'Failed to parse JSON response',
				StatusCodeEnum.TAAS_ERROR,
				{ integrationId: this.integrationId, response: String(error) }
			);
		});

		return resBody as TaasServiceDetailsRes;
	}
}

export default TaasUserIntegration;
