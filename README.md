<div align="center">

# 🌐 Universal Engine

> 🚀 A cutting-edge backend service powered by **ElysiaJS** and **Bun runtime**, designed for the **Unifi eXperience Engine**

[![Version](https://img.shields.io/badge/version-0.0.1-blue?style=for-the-badge)](https://github.com/your-repo)
[![Bun](https://img.shields.io/badge/bun-latest-orange?style=for-the-badge&logo=bun)](https://bun.sh)
[![ElysiaJS](https://img.shields.io/badge/elysiajs-1.2.15-green?style=for-the-badge)](https://elysiajs.com)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.7.3-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Docker](https://img.shields.io/badge/Docker-Enabled-blue?style=for-the-badge&logo=docker)](https://www.docker.com/)

</div>

---

<div align="center">

### 🎨 Quick Links
[📚 Documentation (Universal Hub)](https://didactic-adventure-re12ey6.pages.github.io/) •
[📃 API Documentation (Scalar Doc)](https://myunifi-dev.myu.unifi.com.my/ue/login) •
[👀 Universal Ops Repo](https://github.com/tmberhad-unifi/universal-ops) •
[💡 Universal Hub Repo](https://github.com/tmberhad-unifi/universal-hub) •
[📊 Status Dashboard]() •


</div>

## 🎯 System Architecture

🔄 Core Components:
- 🌐 Client Layer: HTTP/REST API endpoints
- 🚀 Universal Engine: Main application service
- 📊 OpenTelemetry: Distributed tracing system
- 💾 PostgreSQL: Primary database
- ⚡ Redis Cache: High-performance caching

🔗 Integration Points:
- 🔒 Token Authentication
- 🌍 CORS-enabled endpoints
- 📡 Real-time monitoring
- 🔍 Advanced logging

## ⚡ Tech Stack

🏃‍♂️ Runtime & Framework:
- 🎯 Bun Runtime (Latest)
- ⚡ ElysiaJS Framework
- 📝 TypeScript 5.7+

📊 Data Layer:
- 💾 PostgreSQL Database
- 🔄 DrizzleORM
- 📦 Redis Cache

🛠️ Development Tools:
- 📚 Scalar/Swagger Documentation
- 📖 Drizzle Studio Data Management
- 🧪 Vitest Testing Framework
- 🧹 Biome Linting
- 📝 TypeScript

🔄 DevOps & Infrastructure:
- 🐳 Docker Containerization
- 🚀 GitHub Actions CI/CD
- 📊 OpenTelemetry Monitoring
- 🔍 Pino Logging

## 🛠️ Prerequisites

### 🎨 Required Tools

| Tool | Version | Purpose |
|------|---------|----------|
| 💻 VS Code | Latest | Development IDE |
| 🏃 Bun | Latest | Runtime & Package Manager |
| 🐳 Docker | Latest | Containerization |
| 🌳 Git | Latest | Version Control |
| 🔍 DBeaver | Latest | Database Management |
| 🔍 Drizzle Studio | Latest | Database Management |

### 🎯 VS Code Extensions

📦 Core Extensions:
- 🎨 Material Icon Theme - Better file/folder icons
- 🌈 Indent Rainbow - Colorful indentation guides
- 📊 Git Graph - Visualize git history
- 🎯 Biome - Modern JavaScript/TypeScript linter
- 🐳 Docker - Container management
- 📝 Better Comments - Improved code documentation
- 🔠 Better TOML - TOML file support
- 🎨 Bracket Pair Colorizer - Color-coded bracket pairs

## 🚀 Getting Started

### 1️⃣ Environment Setup
```bash
# 📦 Extract environment files
unzip environments-*.zip  # 🔑 Password: #HappyCodingBuddy
cp .env.dev .env

# 📥 Install dependencies
bun install

# 🐳 Start services
docker-compose up -d
```

### 2️⃣ Development Commands

| Command | Description | Category |
|---------|-------------|-----------|
| 🏃 `bun dev` | Start dev server | Development |
| 🧪 `bun test` | Run tests | Testing |
| 🧹 `bun lint` | Run linter | Linting |
| 📈 `bun db:generate` | Generate SQL schema | Database |
| 📊 `bun db:up` | Run migrations | Database |
| 👓 `bun db:studio` | Start Drizzle Studio | Database |
| 🎯 `bun trace` | Generate TS trace | Debug |

## 🏗️ Project Structure

📁 Root Directory Layout:
```
universal-engine/
├── 📂 src/                  # Source code
│   ├── 📂 config/          # App configuration
│   ├── 📂 middleware/      # Custom middleware
│   ├── 📂 modules/          # API endpoints
│   └── 📂 shared/          # Shared utilities
├── 📂 migrations/          # Database migrations
├── 📂 tests/              # Test files
├── 📂 docker/             # Docker configs
├── 📄 .env               # Environment variables
└── 📄 package.json       # Project metadata
```

## 🔄 CI/CD Pipeline

🔁 Workflow Stages:

1. 📤 Code Push & Validation
   - ✅ Branch naming convention check
   - 🧹 Code style verification
   - 🔍 Dependency audit
   - 🧪 Unit test execution

2. 🏗️ Build Process
   - 📝 TypeScript compilation
   - 🐳 Docker image creation
   - 🔒 Security scanning
   - 📊 Performance checks

3. 🚀 Deployment Flow
   - 🌐 Environment-specific builds
   - 📦 Automated versioning
   - 🔄 Container registry push
   - 📡 Health checks

🔀 Branch Strategy:
- 🛠️ `feature/*`, `bugfix/*`, `hotfix/*` → Development
- 🧪 `sit/*`, `uat/*`, `preprod/*`, `staging/*` → Testing
- 📦 `release/*` → Release preparation
- ✨ `main` → Production

## ❓ Troubleshooting

### 🐳 Docker Issues
```bash
# 🔄 Reset Docker environment
docker-compose down -v
docker-compose up -d --build
```
#### Issue 1: `init-db` Fails on macOS Due to Permission Denied (MacOS Users Only)
```bash
permission denied: ./postgres/init-db.sh
```
##### Cause:

On macOS, the init-db.sh script may not have the proper execution permissions, causing Docker to fail when trying to run it.

##### Solution:

1. Grant execution permission to the script using the following command:
```bash
chmod +x ./postgres/init-db.sh
```
2. Then, restart your Docker container:
```bash
docker-compose restart
```

#### Issue 2: `init-db` Container Won’t Start
```bash
/usr/local/bin/docker-entrypoint.sh: line 351: /docker-entrypoint-initdb.d/init-db.sh: cannot execute: required file not found
```
##### Cause:

This typically occurs on Windows, where the line endings of the `postgres/init-db.sh` file are converted from Unix (LF) to Windows (CRLF).

##### Solution:

Convert the `init-db.sh` file back to Unix format. Here's how with **Notepad++**:

1. Open the `init-db.sh` file in Notepad++.
2. Go to **Edit > EOL Conversion > Unix (LF)**.
3. Save the file.

Here's how to do it in **VSCode**:

1. Open the `init-db.sh` file in VSCode.
2. Go to rigth side of Status Bar (The bar at the bottom of the window), the End of Line Sequence option is beside the encoding option
   ![Status Bar](/docs/img/crlf.png)
3. Once clicked, options will be shown on the top of the window. Select LF
   ![CRLF Option](/docs/img/crlf-option.png)

#### Issue 3: Docker Desktop Fails to Start (Windows Users Only)  

##### Cause:  

This typically occurs when you don't have the admin privileges to run Docker Desktop.

##### Solution:  

1. **Run PowerShell as Administrator:**  
   Open PowerShell with administrative privileges.  

2. **Enable Windows Features:**  
   Run the following commands to enable required features:  
   ```bash  
   dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart  
   dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart  
   dism.exe /online /enable-feature /featurename:Microsoft-Hyper-V /all /norestart  
   ```  

3. **Add Your Account to the Docker Users Group:**  
   Replace `${your_account}` with your username and run:  
   ```bash  
   net localgroup docker-users "tmmaster\${your_account}" /add  
   ```  

4. **Enable Hyper-V:**  
   ```bash  
   bcdedit /set hypervisorlaunchtype auto  
   ```  

5. **Install WSL (Windows Subsystem for Linux):**  
   ```bash  
   wsl --set-default-version 2  
   wsl --install -d Ubuntu  
   ```  
   Ensure the installation is performed in your user directory.  

6. **Restart Your Computer:**  
   Reboot your system to apply the changes.  

After completing these steps, Docker Desktop should start successfully.

---

<div align="center">

### 🌟 Project Status
![Status](https://img.shields.io/badge/Status-POC-blue?style=for-the-badge)
![Phase](https://img.shields.io/badge/Phase-0.0.1-orange?style=for-the-badge)

### 📫 Support & Contact

Need help? We're here to assist!

[![GitHub Issues](https://img.shields.io/badge/Open_Issues-GitHub-green?style=for-the-badge&logo=github)](https://github.com/tmberhad-unifi/universal-engine/issues/new)
[![Documentation](https://img.shields.io/badge/Read_Docs-Here-blue?style=for-the-badge&logo=bookstack)](https://didactic-adventure-re12ey6.pages.github.io/)
[![Email Support](https://img.shields.io/badge/Email-Support-red?style=for-the-badge&logo=mail.ru)](mailto:<EMAIL>)

Made with 💖 by the Universal Engine Team 🚀

</div>
