import { integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

export const linkedAccountsTableSchema = pgTable('linked_accounts', {
	Id: integer('id').primaryKey().generatedByDefaultAsIdentity(),
	AccountLabel: text('account_label').notNull(),
	ProductType: text('product_type').notNull(),
	NonOwnerIdType: text('non_owner_id_type').notNull(),
	NonOwnerIdValue: text('non_owner_id_value').notNull(),
	NonOwnerCredentialValue: text('non_owner_credential_value').notNull(),
	OwnerBillAccNo: text('owner_bill_acc_no').notNull(),
	OwnerIdType: text('owner_id_type').notNull(),
	OwnerIdValue: text('owner_id_value').notNull(),
	OwnerEmail: text('owner_email').notNull(),
	SystemName: text('system_name').notNull(),
	Relationship: text('relationship').notNull(),
	CreatedAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
	UpdatedAt: timestamp('updated_at', { mode: 'date' }).defaultNow().notNull()
});

export type SelectLinkedAccounts =
	typeof linkedAccountsTableSchema.$inferSelect;

export type InsertLinkedAccounts =
	typeof linkedAccountsTableSchema.$inferInsert;
