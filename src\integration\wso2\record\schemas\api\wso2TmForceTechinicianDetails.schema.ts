import { type Static, t } from 'elysia';

const wso2TmForceGetTechnicianDetailsReqSchema = t.Object({
	TechnicianDetailsRequest: t.Object({
		TTNumber: t.String(),
		OrderNumber: t.String()
	})
});

export type Wso2TmForceTechinicianDetailsReq = Static<
	typeof wso2TmForceGetTechnicianDetailsReqSchema
>;

export const wso2TmForceGetTechnicianDetailsResSchema = t.Object({
	Status: t.Object({
		Type: t.String(),
		Code: t.String(),
		Message: t.Nullable(t.String())
	}),
	Response: t.Optional(
		t.Object({
			TechnicianDetail: t.Object({
				TTNumber: t.String(),
				OrderNumber: t.String(),
				TechnicianPhoto: t.String(),
				TechnicianName: t.String(),
				TechnicianId: t.String(),
				TechnicianPhoneNumber: t.String(),
				Longitude: t.String(),
				Latitude: t.String(),
				ETTA: t.String(),
				AppointmentDate: t.String(),
				TTAppointmentDate: t.String(),
				OrderAppointmentDate: t.String()
			})
		})
	)
});

export type Wso2TmForceTechinicianDetailsRes = Static<
	typeof wso2TmForceGetTechnicianDetailsResSchema
>;
