import { envConfig } from '../../config/env.config';
import { StatusCodeEnum } from '../../enum/statusCode.enum';
import { UE_ERROR } from '../../middleware/error';
import { fetchApi } from '../helper/fetchApi.helper';
import type {
	MmagTrackOrdersReq,
	MmagTrackOrdersRes
} from './schemas/api/trackOrdersReq.schema';

class MmagIntegration {
	private integrationId: string;

	constructor(integrationId: string) {
		this.integrationId = integrationId;
	}

	async trackOrders(
		bodyRequest: MmagTrackOrdersReq
	): Promise<MmagTrackOrdersRes> {
		const url: string = envConfig().MMAG_TRACK_ORDERS_URL;
		const authToken: string = `Bearer ${process.env.MMAG_BASIC_AUTH}`;

		const body: RequestInit = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: authToken
			},
			body: JSON.stringify(bodyRequest)
		};

		const res = await fetchApi(this.integrationId, url, body);

		const resBody: MmagTrackOrdersRes = await res
			.json()
			.catch((error: unknown) => {
				throw new UE_ERROR(
					'Failed to parse MMAG JSON response',
					StatusCodeEnum.MMAG_ERROR,
					{ integrationId: this.integrationId, response: String(error) }
				);
			});

		if (!res.ok) {
			throw new UE_ERROR(
				'MMAG Track Orders API returned error',
				StatusCodeEnum.MMAG_ERROR,
				{ integrationId: this.integrationId }
			);
		}

		return resBody;
	}
}

export default MmagIntegration;
