import { type Static, t } from 'elysia';

const wso2OutstandingAmountReqSchema = t.Object({
	RetrieveBillingAmountRequest: t.Object({
		BillingAccountNo: t.String(),
		SystemName: t.String()
	})
});

export type Wso2OutstandingAmountReq = Static<
	typeof wso2OutstandingAmountReqSchema
>;

export const wso2OutstandingAmountResSchema = t.Object({
	Response: t.Object({
		RetrieveBillingAmountResponse: t.Object({
			ReturnCode: t.Nullable(t.String()),
			ReturnMessage: t.Nullable(t.String()),
			ReturnTimestamp: t.Nullable(t.String()),
			RetrieveOutStandingAmount: t.Object({
				TotalOutStandingAmount: t.Nullable(t.String())
			})
		})
	})
});

export type Wso2OutstandingAmountRes = Static<
	typeof wso2OutstandingAmountResSchema
>;
