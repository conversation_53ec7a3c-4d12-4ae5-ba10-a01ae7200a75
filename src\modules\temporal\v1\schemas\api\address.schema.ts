import { type Static, t } from 'elysia';
import { wso2GraniteAddressDetailsSchema } from '../../../../../integration/wso2/address/schema/api/wso2GraniteAddress.schema';
import { baseResponseSchema } from '../../../../../shared/schemas/api/responses.schema';
export const addressCheckNigReqSchema = t.Object({
	OrderId: t.String()
});

export type AddressCheckNigQueryReq = Static<typeof addressCheckNigReqSchema>;

export const getGraniteAddressByAddressIdReqSchema = t.Object({
	AddressId: t.String()
});

export const locationCoordinatesSchema = t.Object({
	Lat: t.Optional(t.Number()),
	Lon: t.Optional(t.Number())
});

export const installationAddressSchema = t.Object({
	FormattedAddress: t.String(),
	Apt: t.Optional(t.String()),
	AddressId: t.String(),
	Unit: t.String(),
	Floor: t.String(),
	Block: t.String(),
	Building: t.String(),
	StreetType: t.String(),
	StreetName: t.String(),
	Street: t.String(),
	Section: t.String(),
	PostCode: t.String(),
	City: t.String(),
	State: t.String(),
	Country: t.String(),
	Area: t.String(),
	AddressSource: t.String(),
	Location: t.Optional(locationCoordinatesSchema),
	Platform: t.String(),
	PremiseCategory: t.String(),
	Address1: t.Optional(t.String()),
	Address2: t.Optional(t.String()),
	Latitude: t.Optional(t.String()),
	Longitude: t.Optional(t.String())
});

export type InstallationAddress = Static<typeof installationAddressSchema>;

export const getGraniteAddressByKeywordAndStateReqSchema = t.Object({
	Unit: t.String(),
	Building: t.String(),
	Street: t.String(),
	Postcode: t.String(),
	State: t.String(),
	AddressId: t.String()
});

export type GetGraniteAddressByKeywordAndStateReq = Static<
	typeof getGraniteAddressByKeywordAndStateReqSchema
>;

export const addressCheckNigResSchema = t.Object({
	...baseResponseSchema.properties,
	Response: wso2GraniteAddressDetailsSchema
});

export type AddressCheckNigRes = Static<typeof addressCheckNigResSchema>;
